version: 0.2

phases:
  build:
    commands:
      - ls
      - java --version
      - echo $JAVA_HOME
      - echo $PATH
      - echo "Build started on $(date)"
      - ./gradlew build -Dquarkus.package.type=native -Dquarkus.native.add-all-charsets=true -Dquarkus.profile=prod,staging
      - echo "Checking cache contents after build..."

artifacts:
  files:
    - build/btms-1.0.0-runner
    - appspec.yml
    - scripts/**
  discard-paths: no

cache:
  paths:
    - '/root/.m2/**/*'
    - '.gradle/**/*'