plugins {
    id 'java'
    id 'io.quarkus'
	id "io.freefair.lombok" version "8.7.1"
}

repositories {
    mavenCentral()
    mavenLocal()
}

dependencies {
    implementation enforcedPlatform("${quarkusPlatformGroupId}:${quarkusPlatformArtifactId}:${quarkusPlatformVersion}")
    implementation 'io.quarkus:quarkus-arc'
    implementation 'io.quarkus:quarkus-resteasy'
    implementation 'io.quarkus:quarkus-resteasy-jackson'
    implementation 'io.quarkus:quarkus-smallrye-openapi'
    implementation 'io.quarkus:quarkus-smallrye-jwt-build'
    implementation 'io.quarkus:quarkus-smallrye-jwt'
    implementation 'io.quarkus:quarkus-hibernate-validator'
    implementation 'io.quarkus:quarkus-agroal'
    implementation 'io.quarkus:quarkus-hibernate-orm'
    implementation 'io.quarkus:quarkus-jdbc-mysql'
    implementation 'io.quarkus:quarkus-scheduler'
    implementation 'io.quarkiverse.loggingsentry:quarkus-logging-sentry:2.0.7'
	implementation 'jakarta.data:jakarta.data-api:1.0.0'
	annotationProcessor 'org.hibernate.orm:hibernate-jpamodelgen:6.2.19.Final'
    implementation 'com.google.guava:guava:33.3.0-jre'
    implementation 'com.github.jknack:handlebars:4.3.1'
    implementation 'io.quarkiverse.poi:quarkus-poi:2.1.1'
    implementation 'at.favre.lib:bcrypt:0.10.2' // https://github.com/patrickfav/bcrypt/releases/tag/v0.10.2
}

group 'com.tidesquare'
version '1.0.0'

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

compileJava {
    options.encoding = 'UTF-8'
    options.compilerArgs << '-parameters'
}