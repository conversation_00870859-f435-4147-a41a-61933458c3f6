<!DOCTYPE html>
<html lang="ko">
<!-- start: HEAD -->

<head>
    <title>항공 - 예약 요청 완료 (국제선)</title>
    <!-- start: META -->
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimum-scale=1.0, maximum-scale=1.0">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
</head>
<!-- end: HEAD -->

<body style="background: #fff; margin:0; padding:0; font-family: '돋움',<PERSON>um,'Apple SD Gothic Neo',Sans-serif;">
<div style="background: #fff; text-align: left">
    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="background: #fff;" align="center">
        <tbody>
        <tr>
            <td>
                <table border="0" cellpadding="0" cellspacing="0" width="689" style="background: #fff;" align="center">
                    <colgroup>
                        <col width="100">
                    </colgroup>
                    <tr>
                        <td>
                            <div style="padding: 40px 0 50px">
                                <img src="{{imgUrl}}/email/logo.png" alt="btms">
                            </div>
                        </td>
                    </tr>
                    <tr align="left">
                        <td style="font-size:24px; line-height: 32px; color:#000; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;" hei ght="38">
                            <div style="margin-bottom: 30px;letter-spacing: -1px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;">항공권 {{#if isRequest}}발권이 <b style="color: #4e81ff">요청</b> 되었습니다 {{else}}예약을 <b style="color: #4e81ff">요청</b> 하였습니다.{{/if}}</div>
                        </td>
                    </tr>
                    <tr>
                        <td style="padding-bottom: 50px; border: 0 none;  font-size: 12px; color: #181818; line-height: 20px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;">

							{{#eq userType 'ROLE_CUSTOMER'}}
								{{#if isAdmin}}
									<p style="font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif; margin: 0">
									안녕하세요. 출장담당자님,
									<br>아래와 같은 내용으로 {{reserverName}}님이 {{#if isRequest}}발권을 요청하였습니다.{{else}}항공 예약을 요청하였습니다.{{/if}}
									</p>
								{{else}}
									<p style="font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif; margin: 0">
									안녕하세요. <b>{{name}}</b>님,
									<br>아래와 같은 내용으로 항공 {{#if isRequest}}발권{{else}}예약{{/if}}이 요청되었습니다.
									<br>자세한 사항은 <a href="https://{{btmsUrl}}" target="_blank" style="padding:0;margin:0;border:0 none; font-size:12px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#4e81ff; text-align:left; text-decoration: underline;font-weight: bold;">MY페이지 > 예약내역</a>에서 확인해 주세요.
									</p>
								{{/if}}
							{{/eq}}

							{{#eq userType 'ROLE_AGENCY_USER'}}
								<p style="font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif; margin: 0">
									아래와 같은 내용으로 <b>{{reserverName}}</b>님이 항공 {{#if isRequest}}발권{{else}}예약{{/if}}을 요청하였습니다.
								</p>
							{{/eq}}
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; text-align: center;">
                                <tr>
                                    <td style="padding:0 0 14px;margin:0;border:0 none; font-size:16px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;font-weight: bold;line-height: 24px">예약 내역</td>
                                </tr>
                                <tr>
                                    <td style="padding:0 ;margin:0;border:0 none; border-top: 2px solid #34446e; font-size:14px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;">
                                        <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; text-align: center;">
                                            <colgroup>
                                                <col style="width: 90px">
                                                <col style="width: 300px">
                                                <col>
                                                <col style="width: 210px">
                                            </colgroup>
                                            <tbody>
                                            <tr>
                                                <th style="padding:13px 0px 13px 15px;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;background-color: #f2f6ff">
                                                    예약번호
                                                </th>
                                                <td style="padding:13px 20px;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;">
                                                    {{travel.id}} (항공사 예약번호: {{#eq travel.bookingAir.gdsType "AMADEUS"}}{{travel.bookingAir.otherPnrNo}}{{else}}{{travel.bookingAir.pnrNo}}{{/eq}})
                                                </td>
                                                <th style="padding:13px 0px 13px 15px;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;background-color: #f2f6ff">
                                                    예약상태
                                                </th>
                                                <td style="padding:13px 20px;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#4e81ff; text-align:left;font-weight: bold;">
                                                    {{#eq bookingAdminApprovalType "TravelRuleViolation"}}
                                                        예약품의중
                                                    {{else}}
                                                        예약완료
                                                    {{/eq}}
                                                </td>
                                            </tr>
                                            <tr>
                                                <th style="padding:13px 0px 13px 15px;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;background-color: #f2f6ff">
                                                    탑승객
                                                </th>
                                                <td style="padding:13px 20px;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;">
                                                    {{#each travel.bookingAir.bookingAirTravelers}}
                                                        {{#if @first}}
                                                            {{lastName}} {{firstName}} {{#if personCnt}}외 {{personCnt}}명{{/if}}
                                                        {{/if}}
                                                    {{/each}}
                                                </td>
                                                <th style="padding:13px 0px 13px 15px;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;background-color: #f2f6ff">
                                                    좌석등급
                                                </th>
                                                <td style="padding:13px 20px;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;">
                                                    {{#each travel.bookingAir.bookingAirSchedules}}
                                                        {{#if @first}}
                                                            {{seatType.text}}{{#if bookingClassCode}}({{bookingClassCode}}){{/if}}
                                                        {{/if}}
                                                    {{/each}}
                                                </td>
                                            </tr>
                                            <tr>
                                                <th style="padding:13px 0px 13px 15px;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;background-color: #f2f6ff">
                                                    항공사
                                                </th>
                                                <td colspan="3" style="padding:13px 20px;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;">
                                                    {{#each travel.bookingAir.bookingAirSchedules}}
                                                        {{#if @first}}
                                                            {{airline.name}}
                                                        {{/if}}
                                                    {{/each}}
                                                </td>
                                                <!--<th style="padding:13px 0px 13px 15px;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;background-color: #f2f6ff">
                                                    결제시한
                                                </th>
                                                <td style="padding:13px 20px;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;">
                                                    <a href="" id="" target="_blank" style="padding:0;margin:0;border:0 none; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left; text-decoration: underline;">마이페이지 확인</a>
                                                </td>-->
                                            </tr>
                                            {{#if documentNumberList}}
                                                <tr>
                                                    <th style="padding:13px 0px 13px 15px;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;background-color: #f2f6ff">
                                                        {{#ne company.btmsSetting.documentNumberUseTitle null}}
                                                            {{company.btmsSetting.documentNumberUseTitle}}
                                                        {{else}}
                                                            문서번호
                                                        {{/ne}}
                                                    </th>
                                                    <td colspan="3" style="padding:13px 20px;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;">
                                                        {{#each documentNumberList}}
                                                            {{documentNo}}{{#if @last}}{{else}}&nbsp;,&nbsp;{{/if}}
                                                        {{/each}}
                                                    </td>
                                                </tr>
											{{else}}
												{{#if company.btmsSetting.isDocumentNumberUse}}
													<tr>
														<th style="padding:13px 0px 13px 15px;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;background-color: #f2f6ff">
															{{#ne company.btmsSetting.documentNumberUseTitle null}}
																{{company.btmsSetting.documentNumberUseTitle}}
															{{else}}
																문서번호
															{{/ne}}
														</th>
														<td colspan="3" style="padding:13px 20px;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;">
														</td>
													</tr>
												{{/if}}
                                            {{/if}}
                                            {{#if attachFileList}}
                                                <tr>
                                                    <th style="padding:13px 0px 13px 15px;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;background-color: #f2f6ff">
                                                        첨부파일
                                                    </th>
                                                    <td colspan="3" style="padding:13px 20px;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;">
                                                        {{#each attachFileList}}
                                                            <a href="https://{{btmsUrl}}/common/file/custom/download?attachFileId={{id}}" target="_blank">{{originFileName}}</a>
                                                            {{#unless @last}},{{/unless}}
                                                        {{/each}}
                                                    </td>
                                                </tr>
                                            {{/if}}
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
								<tr>
                                    <td style="padding:0 0 50px;margin:0;border:0 none; font-size:14px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:center; line-height: 29px">
{{!--										{{#eq userType 'ROLE_CUSTOMER'}}--}}
{{!--											<a href="https://{{btmsUrl}}" target="_blank" style="display:block; width:230px;padding:0;margin:30px auto 0;border:0 none; font-size:15px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#fff; text-align:center; line-height:48px;border-radius:26px;font-weight: bold; text-decoration:none;background: #4e81ff;">비교견적서 보기</a>--}}
{{!--										{{/eq}}--}}
{{!--										{{#eq userType 'ROLE_AGENCY_USER'}}--}}
{{!--                                        	<a href="{{agencyUrl}}" target="_blank" style="display:block; width:230px;padding:0;margin:30px auto 0;border:0 none; font-size:15px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#fff; text-align:center; line-height:48px;border-radius:26px;font-weight: bold; text-decoration:none;background: #4e81ff;">비교견적서 보기</a>--}}
{{!--										{{/eq}}--}}
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; text-align: center;">
                                <tbody>
                                <tr>
                                    <td style="padding:0 0 14px;margin:0;border:0 none; font-size:16px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;font-weight: bold;line-height: 24px">여정 정보</td>
                                </tr>
                                <tr>
                                    <td style="padding:0 0 50px;margin:0;border:0 none; border-top: 2px solid #34446e; font-size:14px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;">
                                        <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; text-align: center;">
                                            <colgroup>
                                                <col style="width: 64px">
                                                <col>
                                                <col style="width: 170px">
                                            </colgroup>
                                            <tbody>
                                                {{#each bookingAirScheduleMap}}
                                                    {{#each this}}
                                                    <tr>
                                                        <td style="padding:13px 0px 18px 0;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:14px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;background-color: #fff; vertical-align: top">
															{{#if @first}}
                                                            <span style=" display: block; width: 51px; padding:0px;margin:0;border:0 none; border-radius: 14px; font-size:12px; line-height: 24px; font-weight: normal; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#fff; text-align:center;background-color: #34446e">
                                                                {{#eq ../travel.bookingAir.sectionType 'OneWay'}}편도{{/eq}}
                                                                {{#eq ../travel.bookingAir.sectionType 'RoundTrip'}}{{#eq ../bookingAirScheduleMapSize @key}}오는편{{else}}가는편{{/eq}}{{/eq}}
                                                                {{#eq ../travel.bookingAir.sectionType 'MultiCity'}}여정 {{../@key}}{{/eq}}
                                                            </span>
															{{/if}}
                                                        </td>
                                                        <td style="padding:13px 0px 18px 0;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;background-color: #fff">
															{{#if @first}}
                                                            <div style="padding:0 ;margin:0 0 15px;border:0 none; font-size:14px; line-height: 22px; font-weight: bold; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;">
                                                                {{../this.[0].fromAirport.name}}({{../this.[0].fromAirport.code}}) → {{#each ../this}}{{#if @last}}{{toAirport.name}}({{toAirport.code}}){{/if}}{{/each}}
                                                            </div>
															{{/if}}
                                                            <div style="padding:0 ;margin:0 0 8px;border:0 none; font-size:13px; line-height: 18px; font-weight: normal; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;">
                                                                {{date fromDate 'MM월 dd일'}} ({{date fromDate 'E'}}) {{date fromDate 'HH:mm'}} - {{date toDate 'MM월 dd일'}} ({{date toDate 'E'}}) {{date toDate 'HH:mm'}}
                                                            </div>
                                                            <div style="padding:0 ;margin:0 0 8px;border:0 none; font-size:13px; line-height: 18px; font-weight: normal; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;">{{fromAirport.code}} {{fromAirport.name}} → {{toAirport.code}} {{toAirport.name}}</div>
                                                            <div style="padding:0 ;margin:0 ;border:0 none; font-size:13px; line-height: 18px; font-weight: normal; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;">
                                                                <!--<img src="img/koreanair.png" style="margin-right:3px;" alt="이미지">-->
                                                                {{airline.name}}
                                                                {{#if opAirline.code}}
                                                                    {{#ne airline.code opAirline.code}}
                                                                        &nbsp;[실제운항] {{opAirline.name}}
                                                                    {{/ne}}
                                                                {{/if}} {{airlineFlightNo}}
                                                                <span style="padding:0 ;margin:0;border:0 none; font-size:11px; line-height: 22px; font-weight: bold; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#babdc3; text-align:left;">|</span> {{seatType.text}}({{bookingClassCode}})
                                                            </div>
                                                        </td>
                                                        <td style="padding:13px 0px 18px 0;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:right;background-color: #fff; vertical-align: top">
															{{#if @first}}
                                                            <div style="padding:0 ;margin:0;border:0 none; font-size:14px; line-height: 20px; font-weight: mp; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#4e81ff; text-align:left;">총 {{trimString ../this.[0].totalTime 0 2}}시간 {{trimString ../this.[0].totalTime 2 4}}분</div>
															{{/if}}
                                                            <div style="padding:0 ;margin:0;border:0 none; font-size:13px; line-height: 19px; font-weight: normal; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;">
                                                                <!--12시간 5분 (예약상태: <span style="padding:0 ;margin:0;border:0 none; font-size:13px; line-height: 22px; font-weight: bold; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#4e81ff; text-align:left;">OK</span>)-->
                                                                {{trimString leadTime 0 2}}시간 {{trimString leadTime 2 4}}분
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    {{#unless @last}}
                                                        {{#eq @index 0}}
                                                            <tr>
                                                                <td colspan="3" style="height: 39px; padding:0;margin:0;border:0 none;  font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#49b999; text-align:center; background-color: rgba(73, 185, 153, 0.07);">{{trimString ../this.[1].groundTime 0 2}}시간 {{trimString ../this.[1].groundTime 2 4}}분 대기</td>
                                                            </tr>
                                                        {{/eq}}
                                                        {{#eq @index 1}}
                                                            <tr>
                                                                <td colspan="3" style="height: 39px; padding:0;margin:0;border:0 none;  font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#49b999; text-align:center; background-color: rgba(73, 185, 153, 0.07);">{{trimString ../this.[2].groundTime 0 2}}시간 {{trimString ../this.[2].groundTime 2 4}}분 대기</td>
                                                            </tr>
                                                        {{/eq}}
                                                        {{#eq @index 2}}
                                                            <tr>
                                                                <td colspan="3" style="height: 39px; padding:0;margin:0;border:0 none;  font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#49b999; text-align:center; background-color: rgba(73, 185, 153, 0.07);">{{trimString ../this.[3].groundTime 0 2}}시간 {{trimString ../this.[3].groundTime 2 4}}분 대기</td>
                                                            </tr>
                                                        {{/eq}}
                                                    {{/unless}}
                                                {{/each}}
                                                {{/each}}
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="padding:40px 0 14px;margin:0;border:0 none; font-size:16px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;font-weight: bold;line-height: 24px">요금 정보</td>
                                </tr>
                                <tr>
                                    <td style="padding:0 ;margin:0;border:0 none;  font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;">
                                        <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; text-align: center;">
                                            <colgroup>
                                                <col style="width: 30px">
                                                <col style="width: 140px">
                                                <col style="width: 87px">
                                                <col style="width: 90px">
                                                <col style="width: 80px">
                                                <col style="width: 80px">
                                                <col>
                                            </colgroup>
                                            <thead>
                                            <tr>
                                                <th style=" height:40px;padding:0;margin:0;border:0 none; border-bottom: 2px solid #34446e;font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#34446e; text-align:center; font-weight: bold; line-height:normal">NO</th>
                                                <th style=" height:40px;padding:0;margin:0;border:0 none; border-bottom: 2px solid #34446e;font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#34446e; text-align:center; font-weight: bold; line-height:normal">영문성명</th>
                                                <th style=" height:40px;padding:0;margin:0;border:0 none; border-bottom: 2px solid #34446e;font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#34446e; text-align:center; font-weight: bold; line-height:normal">생년월일</th>
                                                <th style=" height:40px;padding:0;margin:0;border:0 none; border-bottom: 2px solid #34446e;font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#34446e; text-align:center; font-weight: bold; line-height:normal">항공료</th>
                                                <th style=" height:40px;padding:0;margin:0;border:0 none; border-bottom: 2px solid #34446e;font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#34446e; text-align:center; font-weight: bold; line-height:normal">TAX</th>
                                                <th style=" height:40px;padding:0;margin:0;border:0 none; border-bottom: 2px solid #34446e;font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#34446e; text-align:center; font-weight: bold; line-height:normal">발권수수료</th>
                                                <th style=" height:40px;padding:0;margin:0;border:0 none; border-bottom: 2px solid #34446e;font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#34446e; text-align:center; font-weight: bold; line-height:normal">합계</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            {{#each travel.bookingAir.bookingAirTravelers}}
                                                <tr>
                                                    <td style="height: 50px;padding:0;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:center; line-height:normal">{{plus @index 1}}</td>
                                                    <td style="height: 50px;padding:0;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:center; line-height:normal">{{lastName}} {{firstName}}</td>
                                                    <td style="height: 50px;padding:0;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:center; line-height:normal">{{#if traveler.birthday}}{{stringDateFormat traveler.birthday '-' 'ymd'}}{{/if}}</td>
                                                    <td style="height: 50px;padding:0;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:center; line-height:normal">{{comma fareAmount}}</td>
                                                    <td style="height: 50px;padding:0;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:center; line-height:normal">{{comma tax}}</td>
                                                    <td style="height: 50px;padding:0;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:center; line-height:normal">{{comma commissionAmount}}</td>
                                                    <td style="height: 50px;padding:0;margin:0;border:0 none; border-bottom: 1px solid #e2e4e8; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:center; line-height:normal">{{comma reserveAmount}}</td>
                                                </tr>
                                            {{/each}}
                                            <tr>
                                                <td colspan="8" style="padding:20px 0 ;margin:0;border:0 none; font-size:16px; line-height: 24px; font-weight: bold; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:center;">총 결제 예상 요금 <span style="display:inline-block;padding:0 ;margin:0 0 0 17px;border:0 none; font-size:16px; line-height: 24px; font-weight: bold; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#4e81ff; text-align:left;">{{comma totalAmount}}</span>원</td>
                                            </tr>
                                            <tr>
                                                <td colspan="8" style="padding:20px 0 0;margin:0;border:0 none; font-size:12px; line-height: 18px; font-weight: normal; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:center;">(항공료 {{comma fareAmount}}원 + TAX {{comma taxAmount}}원 + 발권수수료 {{comma commissionAmount}}원) <br> <span style="padding:0 ;margin:0;border:0 none; font-size:12px; line-height: 22px; font-weight: normal; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#757f92; text-align:left;">유류할증료, 제세공과금은 발권일 시점의 환율에 따라 변동될 수 있습니다.</span>

                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
								<tr>
                                    <td style="padding:0 0 50px;margin:0;border:0 none; font-size:14px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:center; line-height: 29px">
										{{#eq userType 'ROLE_CUSTOMER'}}
											<a href="https://{{btmsUrl}}" target="_blank" style="display:block; width:230px;padding:0;margin:30px auto 0;border:0 none; font-size:15px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#fff; text-align:center; line-height:48px;border-radius:26px;font-weight: bold; text-decoration:none;background: #4e81ff;">예약내역 확인</a>
										{{/eq}}
										{{#eq userType 'ROLE_AGENCY_USER'}}
											<a href="{{agencyUrl}}" target="_blank" style="display:block; width:230px;padding:0;margin:30px auto 0;border:0 none; font-size:15px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#fff; text-align:center; line-height:48px;border-radius:26px;font-weight: bold; text-decoration:none;background: #4e81ff;">여행사 사이트</a>
										{{/eq}}
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <table border="0" cellpadding="0" cellspacing="0" style="width: 100%; text-align: center;">
                                            <tbody>
                                            <tr>
                                                <td style="padding:0 0 15px;margin:0;border:0 none; font-size:15px; line-height: 22px; font-weight: bold; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left;">꼭 확인하세요!</td>
                                            </tr>
                                            <tr>
                                                <td style="padding:0 0 50px;margin:0;border:0 none; font-size:13px; line-height: 19px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#000; text-align:left; letter-spacing: -1px;    word-spacing: -1px; word-break: keep-all;">*&nbsp&nbsp결제시한 내 결제를 완료하지 않을 경우 예약은 취소됩니다.
													<br>*&nbsp&nbsp결제시한은 항공사 사정에 따라 변경될 수 있습니다.
													<br>*&nbsp&nbsp예약 변경 및 취소는 항공권의 요금 규정에 따라 가능 여부를 확인 후 진행합니다. 출국 전 경유지 및 목적지 변경/취소를 원하실 경우    <a href="https://{{btmsUrl}}" target="_blank" style="padding:0;margin:0;border:0 none; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#4e81ff; text-align:left; line-height:normal; transition-duration: underline">트립뷰 고객센터</a>를 통해 문의 하세요.
													<br>*&nbsp&nbsp경유지 및 목적지 국가의 비자 필요 여부를 미리 확인하여 출국 전 반드시 비자를 발급 받아야 합니다. 비자 발급이 필요하신 경우   <a href="" id="" target="_blank" style="padding:0;margin:0;border:0 none; font-size:13px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#4e81ff; text-align:left; line-height:normal; transition-duration: underline">VISA &gt; 신청하기</a>를 통해 발급 받을 수 있습니다.
													<br>*&nbsp&nbsp외교부 해외안전여행 사이트 (각국의 입국허가 요건) <a href="https://www.0404.go.kr/dev/country.mofa?idx=&hash=&chkvalue=no2&stext=&group_idx=&alert_level=0" target="_blank">바로가기</a>

												</td>                                            </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td style="background: #f2f4f9;">
                <table border="0" cellpadding="0" cellspacing="0" width="689" align="center">
                    <col style="width: 50%" span="2">
                    <tbody>
                    <tr>
                        <td colspan="2" style="padding:31px 0 20px 0;margin:0;border:0 none; font-size:12px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#757f92; text-align:left;">
                            본 메일은 발신전용으로 회신하실 경우 답변을 받으실 수 없습니다.
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2" style="padding:0 0 15px;margin:0;border:0 none; font-size:12px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#757f92; text-align:left;">
                            궁금하신 점은 <a href="https://{{btmsUrl}}" target="_blank" style="padding:0;margin:0;border:0 none; font-size:12px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#4e81ff; text-align:left; text-decoration:underline;">트립뷰 고객센터</a>를 이용하세요.
                        </td>
                    </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr>
            <td style="background: #f2f4f9; border-top: 1px solid #e2e4e8;">
                <table border="0" cellpadding="0" cellspacing="0" width="689" align="center">
                    <colgroup>
                        <col style="width: 50%" span="2">
                    </colgroup>
                    <tbody>
                    <tr>
                        <td style="padding:20px 0 31px ;margin:0;border:0 none; font-size:12px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#757f92; text-align:left;">
                            <div style="padding:0;margin:0 0 12px;border:0 none; font-size:12px; font-family: '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#757f92; text-align:left; line-height: 18px">타이드스퀘어 투어비스 I 소재지 : 서울특별시 중구 남대문로 78, 8층 에이호(명동1가, 타임워크명동빌딩) (우)04534 <br>사업자등록번호 : 497-85-00706 I 관광사업등록증번호 : 제2015-000033호 I 대표이사 : 윤민</div>
                            <div style="padding:0;margin:0;border:0 none; font-size:12px; font-family:Roboto, '돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#757f92; text-align:left; line-height: 18px">Copyright &copy; <storng style="padding:0;margin:0;border:0 none; font-size:12px; font-family: Roboto ,'돋움',Dotum,'Apple SD Gothic Neo',Sans-serif;color:#757f92; text-align:left;font-weight: bold">TIDESQUARE TOURVIS CO. Ltd,</storng> All Rights Reserved.</div>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        </tbody>
    </table>
</div>
</body>

</html>