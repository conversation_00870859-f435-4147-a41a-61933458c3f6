quarkus.hibernate-orm.database.generation=none
quarkus.hibernate-orm.validate-in-dev-mode=false
# <PERSON><PERSON><PERSON> thước pool kết nối
quarkus.datasource.jdbc.max-size=20
%staging.quarkus.swagger-ui.always-include=true
%staging.quarkus.log.min-level=TRACE
%staging.quarkus.hibernate-orm.log.bind-parameters=true
%dev.quarkus.log.min-level=TRACE
%dev.quarkus.hibernate-orm.log.bind-parameters=true
quarkus.smallrye-jwt.enabled=false
quarkus.transaction-manager.default-transaction-timeout=120s
quarkus.native.resources.includes=messages_*.properties,templates/**/*.hbs
# CORS
quarkus.http.cors=true
quarkus.http.cors.origins=/.*/
quarkus.http.cors.methods=GET,PUT,POST,PATCH,DELETE,HEAD
quarkus.http.cors.headers=Authorization,Content-Type
# Sentry
quarkus.log.sentry.dsn=https://<EMAIL>/215
quarkus.log.sentry.level=ERROR
quarkus.log.sentry.in-app-packages=*
