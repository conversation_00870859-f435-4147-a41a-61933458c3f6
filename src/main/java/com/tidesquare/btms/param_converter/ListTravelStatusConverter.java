package com.tidesquare.btms.param_converter;

import java.util.List;

import com.tidesquare.btms.constant.TravelStatus;

import jakarta.ws.rs.ext.ParamConverter;

public class ListTravelStatusConverter implements ParamConverter<List<TravelStatus>> {
    @Override
    public List<TravelStatus> fromString(String value) {
        return value == null || value.isEmpty() ? null : List.of(value.split(",")).stream().map(e -> TravelStatus.valueOf(e)).toList();
    }

    @Override
    public String toString(List<TravelStatus> value) {
        return value == null || value.isEmpty() ? null : String.join(",", value.stream().map(e -> e.name()).toList());
    }
}
