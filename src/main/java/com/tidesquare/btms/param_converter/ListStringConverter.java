package com.tidesquare.btms.param_converter;

import java.util.List;

import jakarta.ws.rs.ext.ParamConverter;

public class ListStringConverter implements ParamConverter<List<String>> {

    @Override
    public List<String> fromString(String value) {
        return value == null || value.isEmpty() ? null : List.of(value.split(","));
    }

    @Override
    public String toString(List<String> value) {
        return value == null || value.isEmpty() ? null : String.join(",", value);
    }

}
