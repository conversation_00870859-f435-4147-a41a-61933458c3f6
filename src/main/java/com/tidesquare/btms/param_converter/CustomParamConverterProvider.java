package com.tidesquare.btms.param_converter;

import java.lang.annotation.Annotation;
import java.lang.reflect.Type;

import jakarta.ws.rs.ext.ParamConverter;
import jakarta.ws.rs.ext.ParamConverterProvider;
import jakarta.ws.rs.ext.Provider;

@Provider
public class CustomParamConverterProvider implements ParamConverterProvider {

    @SuppressWarnings("unchecked")
    @Override
    public <T> ParamConverter<T> getConverter(Class<T> rawType, Type genericType, Annotation[] annotations) {
        for (Annotation annotation : annotations) {
            if (annotation.annotationType().equals(ListString.class)) {
                return (ParamConverter<T>) new ListStringConverter();
            }
            if (annotation.annotationType().equals(ListTravelStatus.class)) {
                return (ParamConverter<T>) new ListTravelStatusConverter();
            }
        }

        return null;
    }
}
