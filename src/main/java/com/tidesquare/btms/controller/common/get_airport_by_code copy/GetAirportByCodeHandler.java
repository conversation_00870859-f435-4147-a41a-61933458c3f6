package com.tidesquare.btms.controller.common.get_airport_by_code;

import com.tidesquare.btms.dto.response.AirportRes;
import com.tidesquare.btms.entity.Airport;
import com.tidesquare.btms.service.AirportService;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class GetAirportByCodeHandler {

    @Inject
    private AirportService airportService;

    public AirportRes run(String code) {
        Airport airport = this.airportService.findByCode(code);
        return AirportRes.fromEntity(airport);
    }
}
