package com.tidesquare.btms.controller.common.search_city;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.dto.response.CityRes;
import com.tidesquare.btms.entity.City;
import com.tidesquare.btms.entity.City_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

@ApplicationScoped
public class SearchCityHandler {

    @Inject
    private StatelessSession statelessSession;

    public List<CityRes> run(String keyword, Long countryId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<City> query = builder.createQuery(City.class);
        Root<City> root = query.from(City.class);
        query.select(root);

        List<Predicate> predicates = new ArrayList<>();
        predicates.add(builder.equal(root.get(City_.isUse), true));
        if (countryId != null && !countryId.equals(0L)) {
            predicates.add(builder.equal(root.get(City_.countryId), countryId));
        }

        predicates.add(builder.or(
                builder.like(root.get(City_.name), "%" + keyword + "%"),
                builder.like(root.get(City_.cityCode), "%" + keyword + "%"),
                builder.like(root.get(City_.nameEng), "%" + keyword + "%"),
                builder.like(root.get(City_.nameSynonym1), "%" + keyword + "%"),
                builder.like(root.get(City_.nameSynonym2), "%" + keyword + "%")));

        query.where(predicates.toArray(new Predicate[0]));
        query.orderBy(builder.asc(root.get(City_.name)));

        List<City> cities = this.statelessSession.createQuery(query).setMaxResults(20).getResultList();
        return cities.stream().map(city -> CityRes.fromEntity(city)).collect(Collectors.toList());
    }
}
