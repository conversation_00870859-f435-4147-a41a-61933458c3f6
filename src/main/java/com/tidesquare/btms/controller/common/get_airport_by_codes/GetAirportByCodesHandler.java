package com.tidesquare.btms.controller.common.get_airport_by_codes;

import java.util.List;

import com.tidesquare.btms.dto.response.AirportRes;
import com.tidesquare.btms.entity.Airport;
import com.tidesquare.btms.service.AirportService;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class GetAirportByCodesHandler {

    @Inject
    private AirportService airportService;

    public List<AirportRes> run(String codes) {
        List<Airport> airports = this.airportService.findByCodes(List.of(codes.split(",")));
        return AirportRes.fromEntities(airports);
    }
}
