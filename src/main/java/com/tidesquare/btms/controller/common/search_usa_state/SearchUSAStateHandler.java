package com.tidesquare.btms.controller.common.search_usa_state;

import java.util.List;
import java.util.stream.Collectors;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.dto.response.USAStateRes;
import com.tidesquare.btms.entity.USAState;
import com.tidesquare.btms.entity.USAState_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;

@ApplicationScoped
public class SearchUSAStateHandler {

    @Inject
    private StatelessSession statelessSession;

    public List<USAStateRes> run(String keyword) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<USAState> query = builder.createQuery(USAState.class);
        Root<USAState> root = query.from(USAState.class);
        query.select(root);

        query.where(builder.or(
                builder.like(root.get(USAState_.code), "%" + keyword + "%"),
                builder.like(root.get(USAState_.name), "%" + keyword + "%"),
                builder.like(root.get(USAState_.nameEn), "%" + keyword + "%")));
        query.orderBy(builder.asc(root.get(USAState_.name)));

        List<USAState> usaStates = this.statelessSession.createQuery(query).setMaxResults(20).getResultList();
        return usaStates.stream().map(usaState -> USAStateRes.fromEntity(usaState)).collect(Collectors.toList());
    }
}
