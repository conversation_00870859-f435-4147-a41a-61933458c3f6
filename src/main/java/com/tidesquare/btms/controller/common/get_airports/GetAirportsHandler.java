package com.tidesquare.btms.controller.common.get_airports;

import java.util.ArrayList;
import java.util.List;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.constant.CountryType;
import com.tidesquare.btms.dto.response.AirportMainRes;
import com.tidesquare.btms.entity.AirportMain;
import com.tidesquare.btms.entity.AirportMain_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;

@ApplicationScoped
public class GetAirportsHandler {

    @Inject
    private StatelessSession statelessSession;

    public GetAirportsRes run() {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<AirportMain> query = builder.createQuery(AirportMain.class);
        Root<AirportMain> root = query.from(AirportMain.class);
        query.select(root);
        query.where(builder.equal(root.get(AirportMain_.isUse), true));
        query.orderBy(builder.asc(root.get(AirportMain_.sectionId)), builder.asc(root.get(AirportMain_.displayOrder)));

        List<AirportMain> airportMains = this.statelessSession.createQuery(query).getResultList();
        List<AirportMainRes> domesticAirportMains = new ArrayList<>();
        List<AirportMainRes> overseasAirportMains = new ArrayList<>();
        for (AirportMain airportMain : airportMains) {
            AirportMainRes airportMainRes = AirportMainRes.fromEntity(airportMain);
            if (airportMain.getCountryType().equals(CountryType.Domestic)) {
                domesticAirportMains.add(airportMainRes);
            } else {
                overseasAirportMains.add(airportMainRes);
            }
        }

        return GetAirportsRes.builder()
                .domestic(domesticAirportMains)
                .overseas(overseasAirportMains)
                .build();
    }
}
