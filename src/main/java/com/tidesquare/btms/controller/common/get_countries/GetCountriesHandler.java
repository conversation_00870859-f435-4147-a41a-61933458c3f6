package com.tidesquare.btms.controller.common.get_countries;

import java.util.List;
import java.util.stream.Collectors;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.dto.response.CountryRes;
import com.tidesquare.btms.entity.Country;
import com.tidesquare.btms.entity.Country_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;

@ApplicationScoped
public class GetCountriesHandler {

    @Inject
    private StatelessSession statelessSession;

    public List<CountryRes> run() {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Country> query = builder.createQuery(Country.class);
        Root<Country> root = query.from(Country.class);
        query.select(root);
        query.where(builder.equal(root.get(Country_.isUse), true));
        query.orderBy(builder.asc(root.get(Country_.name)));

        List<Country> countries = this.statelessSession.createQuery(query).getResultList();
        return countries.stream().map(country -> CountryRes.fromEntity(country)).collect(Collectors.toList());
    }
}
