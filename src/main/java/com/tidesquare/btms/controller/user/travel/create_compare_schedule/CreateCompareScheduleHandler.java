package com.tidesquare.btms.controller.user.travel.create_compare_schedule;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.entity.BookingAir;
import com.tidesquare.btms.entity.BookingAirLowest;
import com.tidesquare.btms.entity.BookingAirLowestDetail;
import com.tidesquare.btms.entity.BookingAirLowestFlight;
import com.tidesquare.btms.entity.BookingAir_;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.CompareAirSchedule;
import com.tidesquare.btms.entity.CompareFlightDetail;
import com.tidesquare.btms.entity.CompareScheduleDetail;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.BookingAirLowestDetailRepo;
import com.tidesquare.btms.repository.BookingAirLowestFlightRepo;
import com.tidesquare.btms.repository.BookingAirLowestRepo;
import com.tidesquare.btms.repository.BookingAirRepo;
import com.tidesquare.btms.repository.CompanyRepo;
import com.tidesquare.btms.repository.CompareAirScheduleRepo;
import com.tidesquare.btms.repository.CompareFlightDetailRepo;
import com.tidesquare.btms.repository.CompareScheduleDetailRepo;
import com.tidesquare.btms.service.stella.dto.request.JourneyReq;
import com.tidesquare.btms.service.stella.dto.request.SegmentReq;
import com.tidesquare.btms.service.travel.TravelService;
import com.tidesquare.btms.utils.AirUtil;
import com.tidesquare.btms.utils.DateUtil;
import com.tidesquare.btms.utils.StringUtils;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response.Status;

@ApplicationScoped
public class CreateCompareScheduleHandler {

    @Inject
    private TravelService travelService;

    @Inject
    private CompareAirScheduleRepo compareAirScheduleRepo;

    @Inject
    private CompareScheduleDetailRepo compareScheduleDetailRepo;

    @Inject
    private CompareFlightDetailRepo compareFlightDetailRepo;

    @Inject
    private BookingAirRepo bookingAirRepo;

    @Inject
    private BookingAirLowestRepo bookingAirLowestRepo;

    @Inject
    private BookingAirLowestDetailRepo bookingAirLowestDetailRepo;

    @Inject
    private BookingAirLowestFlightRepo bookingAirLowestFlightRepo;

    @Inject
    private CompanyRepo companyRepo;

    @Transactional(rollbackOn = Exception.class)
    public void run(UserInfo userInfo, Long travelId, CreateCompareScheduleBody body) {
        boolean isReserver = this.travelService.checkUserIsReverserOfTravel(userInfo.getUserId(), travelId);
        if (!isReserver) {
            throw new ApiException(Status.FORBIDDEN, ErrorCode.E403_TRAVEL_FORBIDDEN);
        }

        Company company = this.companyRepo.findById(userInfo.getCompanyId());
        BookingAir bookingAir = this.bookingAirRepo.findByTravelId(travelId);

        if (body.getLowestSchedule() != null) {
            this.saveLowestSchedule(company, bookingAir, body.getLowestSchedule());
        }
        if (body.getCompareSchedules() != null) {
            for (int i = 0; i < body.getCompareSchedules().size(); i++) {
                this.saveCompareSchedule(company, bookingAir, body.getCompareSchedules().get(i), body.getSelectedSchedule() == i);
            }
        }
    }

    private void saveLowestSchedule(Company company, BookingAir bookingAir, CreateCompareScheduleSchedule lowestSchedule) {
        List<JourneyReq> journeys = lowestSchedule.getJourneys();
        boolean isOverseas = this.getIsOverseas(journeys);

        BookingAirLowest bookingAirLowest = new BookingAirLowest();
        bookingAirLowest.setSectionType(journeys.getFirst().getJourneyType());
        bookingAirLowest.setIsRuleViolation(lowestSchedule.isRuleViolation());
        bookingAirLowest.setIsCorporateFare(journeys.getFirst().getFares().isIncludedCorporateFare());
        bookingAirLowest.setBaggageAllowance(String.join(", ", journeys.stream().map(journey -> journey.getBaggageAllowance()).toList()));

        long totalAirFare = 0;
        long totalTax = 0;
        List<List<BookingAirLowestFlight>> bookingAirLowestFlightss = new ArrayList<>();
        List<BookingAirLowestDetail> bookingAirLowestDetails = new ArrayList<>();
        int scheduleSeqNo = 1;
        for (int i = 0; i < journeys.size(); i++) {
            JourneyReq journey = journeys.get(i);
            List<SegmentReq> segments = journey.getSegments();

            int totalFlightTime = 0;
            int totalWaitingTime = 0;
            List<BookingAirLowestFlight> bookingAirLowestFlights = new ArrayList<>();
            for (int j = 0; j < segments.size(); j++) {
                SegmentReq segment = segments.get(j);
                BookingAirLowestFlight bookingAirLowestFlight = new BookingAirLowestFlight();
                bookingAirLowestFlight.setAirlineCode(segment.getCarrierCode());
                bookingAirLowestFlight.setAirlineFlightNo(segment.getFlightNumber());
                bookingAirLowestFlight.setAirlineName(segment.getCarrierCodeName());
                bookingAirLowestFlight.setBookingClassCode(segment.getBookingClass());
                bookingAirLowestFlight.setDateVariation((int) DateUtil.diffDays(segment.getArrivalDate().substring(0, 10), segment.getDepartureDate().substring(0, 10), "yyyy-MM-dd"));
                bookingAirLowestFlight.setFromAirportCode(segment.getDeptAirport());
                bookingAirLowestFlight.setFromAirportName(segment.getDeptAirportName());
                bookingAirLowestFlight.setFromDate(DateUtil.string2Date(segment.getDepartureDate(), "yyyy-MM-dd'T'HH:mm:ss"));
                bookingAirLowestFlight.setGdsBookingStatusCode(false);
                bookingAirLowestFlight.setNumberOfSeats(journey.getFares().getAvailableCount());
                bookingAirLowestFlight.setToAirportCode(segment.getArrAirport());
                bookingAirLowestFlight.setToAirportName(segment.getArrAirportName());
                bookingAirLowestFlight.setToDate(DateUtil.string2Date(segment.getArrivalDate(), "yyyy-MM-dd'T'HH:mm:ss"));
                bookingAirLowestFlight.setOperatingAirlineCode(segment.getLegs().getFirst().getOperatingCarrier());
                bookingAirLowestFlight.setOperatingAirlineName(segment.getLegs().getFirst().getOperatingCarrierName());
                bookingAirLowestFlight.setSeatClassName(segment.getCabinClass().getText());
                bookingAirLowestFlight.setScheduleSeqNo(scheduleSeqNo++);
                String flightTimeHhmm = DateUtil.formatMinuteHHmm(segment.getFlightTime());
                totalFlightTime += segment.getFlightTime();
                if (!StringUtils.isNullOrEmpty(flightTimeHhmm)) {
                    bookingAirLowestFlight.setLeadHour(flightTimeHhmm.substring(0, 2));
                    bookingAirLowestFlight.setLeadMin(flightTimeHhmm.substring(2, 4));
                }
                totalWaitingTime += segment.getWaitingTime();
                String waitingTimeHhmm = DateUtil.formatMinuteHHmm(segment.getWaitingTime());
                if (!StringUtils.isNullOrEmpty(waitingTimeHhmm)) {
                    bookingAirLowestFlight.setGroundHour(waitingTimeHhmm.substring(0, 2));
                    bookingAirLowestFlight.setGroundMin(waitingTimeHhmm.substring(2, 4));
                }
                bookingAirLowestFlights.add(bookingAirLowestFlight);
            }
            bookingAirLowestFlightss.add(bookingAirLowestFlights);

            BookingAirLowestDetail bookingAirLowestDetail = new BookingAirLowestDetail();
            bookingAirLowestDetail.setArrivalTerminal(journey.getSegments().getLast().getLegs().getLast().getArrivalTerminal());
            bookingAirLowestDetail.setDepartureTerminal(journey.getSegments().getFirst().getLegs().getFirst().getDepartureTerminal());
            bookingAirLowestDetail.setStopoverNo(journey.getStops());
            bookingAirLowestDetail.setItinerarySeq(i + 1);
            String flightTimeHhmm = DateUtil.formatMinuteHHmm(totalFlightTime);
            bookingAirLowestDetail.setFlightHour(flightTimeHhmm.substring(0, 2));
            bookingAirLowestDetail.setFlightMin(flightTimeHhmm.substring(2, 4));
            String totalTimeHhmm = DateUtil.formatMinuteHHmm(totalFlightTime + totalWaitingTime);
            bookingAirLowestDetail.setTotalHour(totalTimeHhmm.substring(0, 2));
            bookingAirLowestDetail.setTotalMin(totalTimeHhmm.substring(2, 4));
            bookingAirLowestDetail.setSeatWaiting(false);
            bookingAirLowestDetails.add(bookingAirLowestDetail);

            totalAirFare += journey.getFares().getPaxTypeFares().getFirst().getAirFare();
            totalTax += (journey.getFares().getPaxTypeFares().getFirst().getAirTax() + journey.getFares().getPaxTypeFares().getFirst().getFuelChg());
        }
        bookingAirLowest.setFareAmount(totalAirFare);
        bookingAirLowest.setTaxAmount(totalTax);
        bookingAirLowest.setTicketAmount(totalAirFare + totalTax);

        long commissionAmount = Math.round(AirUtil.calculationTasf(company, !isOverseas, totalAirFare, 0, totalTax, lowestSchedule.getJourneys().getFirst().getSegments().getFirst().getCabinClass()));
        bookingAirLowest.setTasfAmount(commissionAmount);
        bookingAirLowest.setTotalAmount(commissionAmount + totalAirFare + totalTax);
        bookingAirLowest.setLastTicketDate(new Date());
        bookingAirLowest.setLastTicketDay(0);

        this.bookingAirLowestRepo.insert(bookingAirLowest);
        for (int i = 0; i < bookingAirLowestDetails.size(); i++) {
            BookingAirLowestDetail bookingAirLowestDetail = bookingAirLowestDetails.get(i);
            bookingAirLowestDetail.setBookingAirLowestId(bookingAirLowest.getId());
            this.bookingAirLowestDetailRepo.insert(bookingAirLowestDetail);

            List<BookingAirLowestFlight> bookingAirLowestFlights = bookingAirLowestFlightss.get(i);
            for (int j = 0; j < bookingAirLowestFlights.size(); j++) {
                BookingAirLowestFlight bookingAirLowestFlight = bookingAirLowestFlights.get(j);
                bookingAirLowestFlight.setBookingAirLowestDetailId(bookingAirLowestDetail.getId());
                this.bookingAirLowestFlightRepo.insert(bookingAirLowestFlight);
            }
        }

        this.bookingAirRepo.update(bookingAir.getId(), new HashMap<String, Object>() {
            {
                put(BookingAir_.BOOKING_AIR_LOWEST, bookingAirLowest);
            }
        });
    }

    private void saveCompareSchedule(Company company, BookingAir bookingAir, CreateCompareScheduleSchedule compareSchedule, boolean isSelected) {
        List<JourneyReq> journeys = compareSchedule.getJourneys();
        boolean isOverseas = this.getIsOverseas(journeys);

        CompareAirSchedule compareAirSchedule = new CompareAirSchedule();
        compareAirSchedule.setIsMasterSchedule(isSelected);
        compareAirSchedule.setBookingAirId(bookingAir.getId());
        compareAirSchedule.setSectionType(journeys.getFirst().getJourneyType());
        compareAirSchedule.setIsRuleViolation(compareSchedule.isRuleViolation());
        compareAirSchedule.setIsCorporateFare(journeys.getFirst().getFares().isIncludedCorporateFare());
        compareAirSchedule.setBaggageAllowance(String.join(", ", journeys.stream().map(journey -> journey.getBaggageAllowance()).toList()));

        long totalAirFare = 0;
        long totalTax = 0;
        List<List<CompareFlightDetail>> compareFlightDetailss = new ArrayList<>();
        List<CompareScheduleDetail> compareScheduleDetails = new ArrayList<>();
        int scheduleSeqNo = 1;
        for (int i = 0; i < journeys.size(); i++) {
            JourneyReq journey = journeys.get(i);
            List<SegmentReq> segments = journey.getSegments();

            List<CompareFlightDetail> compareFlightDetails = new ArrayList<>();
            int totalFlightTime = 0;
            int totalWaitingTime = 0;
            for (int j = 0; j < segments.size(); j++) {
                SegmentReq segment = segments.get(j);
                CompareFlightDetail compareFlightDetail = new CompareFlightDetail();
                compareFlightDetail.setAirlineCode(segment.getCarrierCode());
                compareFlightDetail.setAirlineFlightNo(segment.getFlightNumber());
                compareFlightDetail.setAirlineName(segment.getCarrierCodeName());
                compareFlightDetail.setBookingClassCode(segment.getBookingClass());
                compareFlightDetail.setDateVariation((int) DateUtil.diffDays(segment.getArrivalDate().substring(0, 10), segment.getDepartureDate().substring(0, 10), "yyyy-MM-dd"));
                compareFlightDetail.setFromAirportCode(segment.getDeptAirport());
                compareFlightDetail.setFromAirportName(segment.getDeptAirportName());
                compareFlightDetail.setFromDate(DateUtil.string2Date(segment.getDepartureDate(), "yyyy-MM-dd'T'HH:mm:ss"));
                compareFlightDetail.setGdsBookingStatusCode(false);
                compareFlightDetail.setNumberOfSeats(journey.getFares().getAvailableCount());
                compareFlightDetail.setToAirportCode(segment.getArrAirport());
                compareFlightDetail.setToAirportName(segment.getArrAirportName());
                compareFlightDetail.setToDate(DateUtil.string2Date(segment.getArrivalDate(), "yyyy-MM-dd'T'HH:mm:ss"));
                compareFlightDetail.setOperatingAirlineCode(segment.getLegs().getFirst().getOperatingCarrier());
                compareFlightDetail.setOperatingAirlineName(segment.getLegs().getFirst().getOperatingCarrierName());
                compareFlightDetail.setSeatClassName(segment.getCabinClass().getText());
                compareFlightDetail.setScheduleSeqNo(scheduleSeqNo++);
                String flightTimeHhmm = DateUtil.formatMinuteHHmm(segment.getFlightTime());
                totalFlightTime += segment.getFlightTime();
                if (!StringUtils.isNullOrEmpty(flightTimeHhmm)) {
                    compareFlightDetail.setLeadHour(flightTimeHhmm.substring(0, 2));
                    compareFlightDetail.setLeadMin(flightTimeHhmm.substring(2, 4));
                }
                String waitingTimeHhmm = DateUtil.formatMinuteHHmm(segment.getWaitingTime());
                totalWaitingTime += segment.getWaitingTime();
                if (!StringUtils.isNullOrEmpty(waitingTimeHhmm)) {
                    compareFlightDetail.setGroundHour(waitingTimeHhmm.substring(0, 2));
                    compareFlightDetail.setGroundMin(waitingTimeHhmm.substring(2, 4));
                }
                compareFlightDetails.add(compareFlightDetail);
            }
            compareFlightDetailss.add(compareFlightDetails);

            CompareScheduleDetail compareScheduleDetail = new CompareScheduleDetail();
            compareScheduleDetail.setArrivalTerminal(journey.getSegments().getLast().getLegs().getLast().getArrivalTerminal());
            compareScheduleDetail.setDepartureTerminal(journey.getSegments().getFirst().getLegs().getFirst().getDepartureTerminal());
            compareScheduleDetail.setStopoverNo(journey.getStops());
            compareScheduleDetail.setItinerarySeq(i + 1);
            String flightTimeHhmm = DateUtil.formatMinuteHHmm(totalFlightTime);
            compareScheduleDetail.setFlightHour(flightTimeHhmm.substring(0, 2));
            compareScheduleDetail.setFlightMin(flightTimeHhmm.substring(2, 4));
            String totalTimeHhmm = DateUtil.formatMinuteHHmm(totalFlightTime + totalWaitingTime);
            compareScheduleDetail.setTotalHour(totalTimeHhmm.substring(0, 2));
            compareScheduleDetail.setTotalMin(totalTimeHhmm.substring(2, 4));
            compareScheduleDetail.setSeatWaiting(false);
            compareScheduleDetails.add(compareScheduleDetail);

            totalAirFare += journey.getFares().getPaxTypeFares().getFirst().getAirFare();
            totalTax += (journey.getFares().getPaxTypeFares().getFirst().getAirTax() + journey.getFares().getPaxTypeFares().getFirst().getFuelChg());
        }
        compareAirSchedule.setFareAmount(totalAirFare);
        compareAirSchedule.setTaxAmount(totalTax);
        compareAirSchedule.setTicketAmount(totalAirFare + totalTax);

        long commissionAmount = Math.round(AirUtil.calculationTasf(company, !isOverseas, totalAirFare, 0, totalTax, compareSchedule.getJourneys().getFirst().getSegments().getFirst().getCabinClass()));
        compareAirSchedule.setTasfAmount(commissionAmount);
        compareAirSchedule.setTotalAmount(commissionAmount + totalAirFare + totalTax);
        compareAirSchedule.setLastTicketDate(new Date());
        compareAirSchedule.setLastTicketDay(0);

        this.compareAirScheduleRepo.insert(compareAirSchedule);
        for (int i = 0; i < compareScheduleDetails.size(); i++) {
            CompareScheduleDetail compareScheduleDetail = compareScheduleDetails.get(i);
            compareScheduleDetail.setCompareAirScheduleId(compareAirSchedule.getId());
            this.compareScheduleDetailRepo.insert(compareScheduleDetail);

            List<CompareFlightDetail> compareFlightDetails = compareFlightDetailss.get(i);
            for (int j = 0; j < compareFlightDetails.size(); j++) {
                CompareFlightDetail compareFlightDetail = compareFlightDetails.get(j);
                compareFlightDetail.setCompareScheduleDetailId(compareScheduleDetail.getId());
                this.compareFlightDetailRepo.insert(compareFlightDetail);
            }
        }
    }

    private boolean getIsOverseas(List<JourneyReq> journeys) {
        for (JourneyReq journey : journeys) {
            if (journey.isOverseas()) {
                return true;
            }
        }

        return false;
    }
}
