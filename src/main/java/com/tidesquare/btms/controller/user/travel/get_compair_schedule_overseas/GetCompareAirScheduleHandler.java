package com.tidesquare.btms.controller.user.travel.get_compair_schedule_overseas;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.dto.response.CompareAirScheduleRes;
import com.tidesquare.btms.entity.BookingAir;
import com.tidesquare.btms.entity.CompareAirSchedule;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.BookingAirRepo;
import com.tidesquare.btms.repository.CompareAirScheduleRepo;
import com.tidesquare.btms.service.travel.TravelService;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response.Status;

import java.util.List;

@ApplicationScoped
public class GetCompareAirScheduleHandler {

    @Inject
    private TravelService travelService;

    @Inject
    private BookingAirRepo bookingAirRepo;

    @Inject
    private CompareAirScheduleRepo compairAirScheduleRepo;

    public List<CompareAirScheduleRes> run(UserInfo userInfo, Long travelId) {
        boolean isPermission = travelService.checkUserIsReverserOrTravelerOfTravel(userInfo.getUserId(), travelId);
        if (!isPermission) {
            throw new ApiException(Status.FORBIDDEN, ErrorCode.E403_TRAVEL_FORBIDDEN);
        }

        BookingAir bookingAir = bookingAirRepo.findByTravelId(travelId);
        List<CompareAirSchedule> compareAirSchedules = compairAirScheduleRepo.findAllByBookingAirId(bookingAir.getId());

        return CompareAirScheduleRes.fromEntities(compareAirSchedules);
    }
}
