package com.tidesquare.btms.controller.user.colleague.list;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.common.ApiQueryPagination;
import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.constant.JoinStatus;
import com.tidesquare.btms.constant.SearchAuth;
import com.tidesquare.btms.dto.response.CustomerRes;
import com.tidesquare.btms.entity.Customer_;
import com.tidesquare.btms.entity.Department;
import com.tidesquare.btms.entity.Department_;
import com.tidesquare.btms.entity.Workspace;
import com.tidesquare.btms.entity.Workspace_;
import com.tidesquare.btms.repository.CustomerRepo;
import com.tidesquare.btms.repository.DepartmentRepo;
import com.tidesquare.btms.entity.Company_;
import com.tidesquare.btms.entity.Customer;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Fetch;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

@ApplicationScoped
public class UserGetListColleagueHandler {

    @Inject
    private StatelessSession statelessSession;

    @Inject
    private CustomerRepo customerRepo;

    @Inject
    private DepartmentRepo departmentRepo;

    public Map<String, Object> run(UserInfo userInfo, UserGetListColleagueQuery query, ApiQueryPagination pagination) {
        Customer user = this.customerRepo.findOne(userInfo.getUserId());

        List<Customer> customers = this.list(userInfo, user, query, pagination);
        Long count = this.count(userInfo, user, query);

        Map<String, Object> result = new HashMap<>();
        result.put("list", CustomerRes.fromCustomers(customers));
        result.put("total", count);

        return result;
    }

    private List<Customer> list(UserInfo userInfo, Customer user, UserGetListColleagueQuery query, ApiQueryPagination pagination) {
        if (user.getSearchAuth().equals(SearchAuth.AUTHA) || (user.getSearchAuth().equals(SearchAuth.AUTHB) && user.getDepartment() == null)) {
            return new ArrayList<>();
        }

        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Customer> q = builder.createQuery(Customer.class);
        Root<Customer> root = q.from(Customer.class);
        root.fetch(Customer_.department, JoinType.LEFT);
        Fetch<Customer, Workspace> workspaceFetch = root.fetch(Customer_.workspace, JoinType.INNER);
        @SuppressWarnings("unchecked")
        Join<Customer, Workspace> workspaceJoin = (Join<Customer, Workspace>) workspaceFetch;
        q.select(root);

        q.where(this.getWhere(userInfo, user, query, builder, root, workspaceJoin));
        q.orderBy(builder.desc(root.get(Customer_.id)));

        return this.statelessSession.createSelectionQuery(q).setFirstResult((pagination.getPage() - 1) * pagination.getSize()).setMaxResults(pagination.getSize()).getResultList();
    }

    private Long count(UserInfo userInfo, Customer user, UserGetListColleagueQuery query) {
        if (user.getSearchAuth().equals(SearchAuth.AUTHA) || (user.getSearchAuth().equals(SearchAuth.AUTHB) && user.getDepartment() == null)) {
            return 0L;
        }

        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Long> q = builder.createQuery(Long.class);
        Root<Customer> root = q.from(Customer.class);
        Join<Customer, Workspace> workspaceJoin = root.join(Customer_.workspace, JoinType.INNER);
        q.select(builder.count(root));

        q.where(this.getWhere(userInfo, user, query, builder, root, workspaceJoin));
        return this.statelessSession.createSelectionQuery(q).getSingleResult();
    }

    private Predicate[] getWhere(UserInfo userInfo, Customer user, UserGetListColleagueQuery query, CriteriaBuilder builder, Root<Customer> root, Join<Customer, Workspace> workspaceJoin) {
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(builder.notEqual(root.get(Customer_.joinStatus), JoinStatus.Leave));
        if (query.getName() != null) {
            predicates.add(builder.like(root.get(Customer_.name), "%" + query.getName() + "%"));
        }
        if (user.getSearchAuth().equals(SearchAuth.AUTHB)) {
            List<Department> departments = this.departmentRepo.findRecursiveByWorkspaceIdAndParentDepartmentId(user.getWorkspace().getId(), user.getDepartment().getId());
            List<Long> departmentIds = departments.stream().map(department -> department.getId()).toList();
            predicates.add(root.get(Customer_.department).get(Department_.id).in(departmentIds));
        } else if (user.getSearchAuth().equals(SearchAuth.AUTHC)) {
            predicates.add(builder.equal(root.get(Customer_.workspace).get(Workspace_.id), user.getWorkspace().getId()));
        } else if (user.getSearchAuth().equals(SearchAuth.ADMIN)) {
            predicates.add(builder.equal(workspaceJoin.get(Workspace_.company).get(Company_.id), userInfo.getCompanyId()));
        } else {
            throw new RuntimeException("Invalid search auth");
        }

        return predicates.toArray(new Predicate[0]);
    }
}
