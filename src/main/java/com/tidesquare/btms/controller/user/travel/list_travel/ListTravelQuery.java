package com.tidesquare.btms.controller.user.travel.list_travel;

import java.util.List;

import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import com.tidesquare.btms.constant.TravelStatus;
import com.tidesquare.btms.param_converter.ListTravelStatus;

import jakarta.ws.rs.QueryParam;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ListTravelQuery {
    @QueryParam("keyword")
    private String keyword;

    @QueryParam("statuses")
    @Schema(type = SchemaType.STRING, example = "Requested,Approved,Completed,Cancelled,Rejected", description = "Comma-separated list of travel statuses")
    @ListTravelStatus
    private List<TravelStatus> statuses;
}
