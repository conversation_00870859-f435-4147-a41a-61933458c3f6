package com.tidesquare.btms.controller.user.travel.list_travel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedSet;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.common.ApiQueryPagination;
import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.dto.response.TravelRes;
import com.tidesquare.btms.entity.BookingAirSchedule;
import com.tidesquare.btms.entity.BookingAirTraveler;
import com.tidesquare.btms.entity.City;
import com.tidesquare.btms.entity.Travel;
import com.tidesquare.btms.entity.TravelCity;
import com.tidesquare.btms.entity.TravelCity_;
import com.tidesquare.btms.entity.Travel_;
import com.tidesquare.btms.entity.Traveler;
import com.tidesquare.btms.entity.Traveler_;
import com.tidesquare.btms.repository.BookingAirScheduleRepo;
import com.tidesquare.btms.repository.BookingAirTravelerRepo;
import com.tidesquare.btms.service.CityService;
import com.tidesquare.btms.utils.StringUtils;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
@Startup
public class ListTravelHandler {
    @Inject
    private StatelessSession statelessSession;

    @Inject
    private CityService cityService;

    @Inject
    private BookingAirScheduleRepo bookingAirScheduleRepo;

    @Inject
    private BookingAirTravelerRepo bookingAirTravelerRepo;

    @PostConstruct
    public void init() {
    }

    public Map<String, Object> run(UserInfo userInfo, ListTravelQuery query, ApiQueryPagination pagination) {
        Long count = this.count(userInfo, query);
        if (count == 0) {
            return Map.of("list", List.of(), "total", 0L);
        }
        List<Travel> travels = this.list(userInfo, query, pagination);
        List<Long> bookingAirIds = travels.stream().map(travel -> travel.getBookingAir().getId()).toList();
        Map<Long, SortedSet<BookingAirSchedule>> bookingAirScheduleMap = this.bookingAirScheduleRepo.getBookingAirSchedulesByBookingAirIds(bookingAirIds);
        travels.forEach(travel -> {
            travel.getBookingAir().setBookingAirSchedules(bookingAirScheduleMap.get(travel.getBookingAir().getId()));
        });
        Map<Long, SortedSet<BookingAirTraveler>> bookingAirTravelerMap = this.bookingAirTravelerRepo.getBookingAirTravelersByBookingAirIds(bookingAirIds);
        travels.forEach(travel -> {
            travel.getBookingAir().setBookingAirTravelers(bookingAirTravelerMap.get(travel.getBookingAir().getId()));
        });

        Map<String, Object> result = new HashMap<>();
        result.put("list", TravelRes.fromEntities(travels));
        result.put("total", count);

        return result;
    }

    private List<Travel> list(UserInfo userInfo, ListTravelQuery query, ApiQueryPagination pagination) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Travel> q = builder.createQuery(Travel.class);
        Root<Travel> root = q.from(Travel.class);
        root.fetch(Travel_.bookingAir, JoinType.INNER);
        q.select(root);

        q.where(this.getWhere(userInfo, query, builder, root, q));
        q.orderBy(builder.desc(root.get(Travel_.id)));

        return this.statelessSession.createSelectionQuery(q).setFirstResult((pagination.getPage() - 1) * pagination.getSize()).setMaxResults(pagination.getSize()).getResultList();
    }

    private Long count(UserInfo userInfo, ListTravelQuery query) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Long> q = builder.createQuery(Long.class);
        Root<Travel> root = q.from(Travel.class);
        q.select(builder.count(root));

        q.where(this.getWhere(userInfo, query, builder, root, q));
        return this.statelessSession.createSelectionQuery(q).getSingleResult();
    }

    private Predicate[] getWhere(UserInfo userInfo, ListTravelQuery query, CriteriaBuilder builder, Root<Travel> root, CriteriaQuery<?> q) {
        List<Predicate> predicates = new ArrayList<>();

        predicates.add(builder.equal(root.get(Travel_.isDeleted), false));

        Subquery<Long> subqueryTraveler = q.subquery(Long.class);
        Root<Traveler> rootTraveler = subqueryTraveler.from(Traveler.class);
        subqueryTraveler
                .select(builder.literal(1L))
                .where(builder.equal(rootTraveler.get(Traveler_.travelerUserId), userInfo.getUserId()),
                        builder.equal(rootTraveler.get(Traveler_.travelId), root.get(Travel_.id)));
        predicates.add(builder.exists(subqueryTraveler));

        if (query.getStatuses() != null && !query.getStatuses().isEmpty()) {
            predicates.add(root.get(Travel_.status).in(query.getStatuses()));
        }
        if (!StringUtils.isNullOrEmpty(query.getKeyword())) {
            List<Predicate> predicatesOr = new ArrayList<>();

            List<City> cities = this.cityService.findByName(query.getKeyword());
            if (!cities.isEmpty()) {
                List<Long> cityIds = cities.stream().map(city -> city.getId()).toList();

                Subquery<Long> subqueryTravelCity = q.subquery(Long.class);
                Root<TravelCity> rootTravelCity = subqueryTravelCity.from(TravelCity.class);
                subqueryTravelCity
                        .select(builder.literal(1L))
                        .where(rootTravelCity.get(TravelCity_.travelCityId).in(cityIds),
                                builder.equal(rootTravelCity.get(TravelCity_.travelId), root.get(Travel_.id)));
                predicatesOr.add(builder.exists(subqueryTravelCity));
            }

            Subquery<Long> subqueryTraveler2 = q.subquery(Long.class);
            Root<Traveler> rootTraveler2 = subqueryTraveler2.from(Traveler.class);
            subqueryTraveler2
                    .select(builder.literal(1L))
                    .where(builder.equal(rootTraveler2.get(Traveler_.isReserver), false),
                            builder.or(
                                    builder.like(rootTraveler2.get(Traveler_.name), "%" + query.getKeyword() + "%"),
                                    builder.like(builder.function("concat", String.class, rootTraveler2.get(Traveler_.lastName), builder.literal(" "), rootTraveler2.get(Traveler_.firstName)), "%" + query.getKeyword() + "%")),
                            builder.equal(rootTraveler2.get(Traveler_.travelId), root.get(Travel_.id)));
            predicatesOr.add(builder.exists(subqueryTraveler2));

            if (predicatesOr.size() == 1) {
                predicates.add(predicatesOr.getFirst());
            } else {
                predicates.add(builder.or(predicatesOr.toArray(new Predicate[0])));
            }
        }

        return predicates.toArray(new Predicate[0]);
    }
}
