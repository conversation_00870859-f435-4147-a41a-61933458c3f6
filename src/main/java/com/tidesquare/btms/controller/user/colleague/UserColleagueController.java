package com.tidesquare.btms.controller.user.colleague;

import java.util.Map;

import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import com.tidesquare.btms.common.ApiQueryPagination;
import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.controller.user.colleague.list.UserGetListColleagueHandler;
import com.tidesquare.btms.controller.user.colleague.list.UserGetListColleagueQuery;
import com.tidesquare.btms.filter.UserBinding;

import io.vertx.mutiny.core.Vertx;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.BeanParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;

@Path("user/colleague")
@UserBinding
@SecurityRequirement(name = "bearer")
public class UserColleagueController {

    @Inject
    private UserGetListColleagueHandler userGetListColleagueHandler;

    @GET
    @Path("")
    @Operation(summary = "Get List Colleague")
    public ApiResponse<Map<String, Object>> getListColleague(@Valid @BeanParam UserGetListColleagueQuery query, @BeanParam ApiQueryPagination pagination) {
        UserInfo userInfo = Vertx.currentContext().getLocal("user");
        return ApiResponse.fromData(this.userGetListColleagueHandler.run(userInfo, query, pagination));
    }
}
