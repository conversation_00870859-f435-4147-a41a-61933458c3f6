package com.tidesquare.btms.controller.user.company.get_btms_manager;

import java.util.List;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.constant.ManagerType;
import com.tidesquare.btms.dto.response.BtmsManagerRes;
import com.tidesquare.btms.repository.BtmsManagerRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class UserGetBtmsManagerHandler {

    @Inject
    private BtmsManagerRepo btmsManagerRepo;

    public List<BtmsManagerRes> run(UserInfo userInfo, ManagerType managerType, Boolean isOverseas) {
        return BtmsManagerRes.fromEntities(this.btmsManagerRepo.findFetchTravelAgencyUser(userInfo.getCompanyId(), isOverseas, managerType));
    }
}
