package com.tidesquare.btms.controller.user.travel.search_travel_overseas;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.constant.GDSType;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.CompanyFare;
import com.tidesquare.btms.entity.ExcludeAirline;
import com.tidesquare.btms.entity.StellaSearchFareLog;
import com.tidesquare.btms.repository.CompanyFareRepo;
import com.tidesquare.btms.repository.CompanyRepo;
import com.tidesquare.btms.repository.ExcludeAirRepo;
import com.tidesquare.btms.repository.StellaSearchFareLogRepo;
import com.tidesquare.btms.service.AirlineService;
import com.tidesquare.btms.service.stella.StellaService;
import com.tidesquare.btms.service.stella.dto.request.JourneyReq;
import com.tidesquare.btms.service.stella.dto.response.JourneyRes;
import com.tidesquare.btms.service.stella.dto.search_fare_schedule.request.SearchFareScheduleBody;
import com.tidesquare.btms.service.stella.dto.search_fare_schedule.request.SearchFareSchedulePaxCount;
import com.tidesquare.btms.service.stella.dto.search_fare_schedule.response.SearchFareScheduleJourneyInfo;
import com.tidesquare.btms.service.stella.dto.search_fare_schedule.response.SearchFareScheduleResponse;
import com.tidesquare.btms.utils.AirUtil;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class SearchTravelOverseasHandler {

    @Inject
    private CompanyRepo companyRepo;

    @Inject
    private CompanyFareRepo companyFareRepo;

    @Inject
    private StellaService stellaService;

    @Inject
    private ExcludeAirRepo excludeAirRepo;

    @Inject
    private AirlineService airlineService;

    @Inject
    private StellaSearchFareLogRepo stellaSearchFareLogRepo;

    private ObjectMapper objectMapper = new ObjectMapper();

    public SearchFareScheduleResponse run(UserInfo userInfo, SearchTravelOverseasBody body) {
        Company company = this.companyRepo.findById(userInfo.getCompanyId());

        List<JourneyReq> journeyInfoDtos = body.getJourneys().stream()
                .map(x -> JourneyReq.builder()
                        .deptAirport(x.getDeptAirport())
                        .arrAirport(x.getArrAirport())
                        .departureDate(x.getDepartureDate())
                        .build())
                .toList();

        // with include will have copcode
        List<String> ampCopCodes = new ArrayList<>();
        List<String> bfmCopCodes = new ArrayList<>();
        List<CompanyFare> companyFares = this.companyFareRepo.findByCompanyId(company.getId());
        // First AMADEUS
        for (CompanyFare companyFare : companyFares) {
            if (companyFare.getGdsType().equals(GDSType.AMADEUS)) {
                ampCopCodes.add(companyFare.getCode());
            }
        }
        // Then SABRE
        for (CompanyFare companyFare : companyFares) {
            if (companyFare.getGdsType().equals(GDSType.SABRE)) {
                bfmCopCodes.add(companyFare.getCode());
            }
        }

        SearchFareScheduleBody searchFareScheduleBody = SearchFareScheduleBody.builder()
                .ssCode("BTMS")
                .ampCopCode(ampCopCodes)
                .bfmCopCode(bfmCopCodes)
                .airlines(new ArrayList<>())
                .cabinClass(Collections.singletonList(body.getCabinClass()))
                .journeyInfos(journeyInfoDtos)
                .paxCount(SearchFareSchedulePaxCount.builder().adultCount(body.getAdultCount()).build())
                .pairType("A")
                .provider(Arrays.asList(GDSType.AMADEUS, GDSType.SABRE))
                .recommendCount(1000L)
                .build();

        // call stella service
        SearchFareScheduleResponse searchFareScheduleResponse = this.stellaService.searchFair(searchFareScheduleBody);
        searchFareScheduleResponse = this.mapToJourneyByPairKeyAndExcludeAirline(searchFareScheduleResponse, userInfo);
        for (JourneyRes journeyRes : searchFareScheduleResponse.getJourneyInfos().getFirst().getJourneys()) {
            long totalAirFare = journeyRes.getFares().getPaxTypeFares().getFirst().getAirFare();
            long totalTax = journeyRes.getFares().getPaxTypeFares().getFirst().getAirTax() + journeyRes.getFares().getPaxTypeFares().getFirst().getFuelChg();
            double commissionAmount = AirUtil.calculationTasf(company, false, totalAirFare, 0, totalTax, journeyRes.getSegments().getFirst().getCabinClass());
            journeyRes.setTasfAmount(commissionAmount);
        }

        try {
            StellaSearchFareLog stellaSearchFareLog = new StellaSearchFareLog();
            stellaSearchFareLog.setUserId(userInfo.getUserId());
            stellaSearchFareLog.setTime(new Date());
            stellaSearchFareLog.setBody(objectMapper.writeValueAsString(searchFareScheduleBody));
            stellaSearchFareLog.setResponse(objectMapper.writeValueAsString(searchFareScheduleResponse));
            this.stellaSearchFareLogRepo.insert(stellaSearchFareLog);
        } catch (Exception e) {
            e.printStackTrace();
        }

        return searchFareScheduleResponse;
    }

    public SearchFareScheduleResponse mapToJourneyByPairKeyAndExcludeAirline(SearchFareScheduleResponse searchFareScheduleResponse, UserInfo userInfo) {
        // Get airline code exclude
        List<ExcludeAirline> excludeAirlines = this.excludeAirRepo.findByCompanyId(userInfo.getCompanyId());
        Set<String> excludeCodeAirlines = excludeAirlines.stream()
                .map(e -> this.airlineService.findById(e.getAirlineId()).getCode())
                .collect(Collectors.toSet());

        // get list journeyInfos from stella
        List<SearchFareScheduleJourneyInfo> journeyInfos = searchFareScheduleResponse.getJourneyInfos();
        if (journeyInfos == null || journeyInfos.isEmpty()) {
            return searchFareScheduleResponse;
        }

        // Handle if have 1 element in journeys array
        if (journeyInfos.size() == 1) {
            // remove journey if exclude
            journeyInfos.get(0).getJourneys()
                    .removeIf(journey -> excludeCodeAirlines.contains(journey.getAirline()));
            searchFareScheduleResponse.setJourneyInfos(journeyInfos);
            return searchFareScheduleResponse;
        }

        // sort the next element follow first element
        Map<String, Integer> referenceOrder = IntStream
                .range(0, journeyInfos.get(0).getJourneys().size())
                .boxed()
                .collect(Collectors.toMap(i -> journeyInfos.get(0).getJourneys().get(i).getPairKey(), i -> i));

        // sort journeyInfos by referenceOrder (pair key of first element)
        for (int i = 1; i < journeyInfos.size(); i++) {
            journeyInfos.get(i).getJourneys()
                    .sort(Comparator.comparingInt(
                            journey -> referenceOrder.getOrDefault(journey.getPairKey(), Integer.MAX_VALUE)));
        }

        // Remove journey of exclude air list
        for (int i = 0; i < journeyInfos.size(); i++) {
            journeyInfos.get(i).getJourneys()
                    .removeIf(journey -> excludeCodeAirlines.contains(journey.getAirline()));
        }
        searchFareScheduleResponse.setJourneyInfos(journeyInfos);

        return searchFareScheduleResponse;
    }
}
