package com.tidesquare.btms.controller.user.travel.create_travel_overseas;

import com.tidesquare.btms.constant.AttachFileType;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateTravelOverseasAttachFile {
    @NotBlank
    private String originFileName;

    @NotBlank
    private String tempFileName;

    @NotBlank
    private String fileUploadPath;

    @Min(value = 1)
    @NotNull
    private Long fileSize;

    @NotNull
    private AttachFileType attachFileType;
}
