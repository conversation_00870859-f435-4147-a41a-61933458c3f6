package com.tidesquare.btms.controller.user.travel.get_history;

import java.util.List;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.dto.response.TravelStatusHistoryRes;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.TravelStatusHistoryRepo;
import com.tidesquare.btms.service.travel.TravelService;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response.Status;

@ApplicationScoped
public class GetTravelStatusHistoryHandler {
    @Inject
    private TravelService travelService;

    @Inject
    private TravelStatusHistoryRepo travelStatusHistoryRepo;

    public List<TravelStatusHistoryRes> run(UserInfo userInfo, long travelId) {
        boolean isPermission = travelService.checkUserIsReverserOrTravelerOfTravel(userInfo.getUserId(), travelId);
        if (!isPermission) {
            throw new ApiException(Status.FORBIDDEN, ErrorCode.E403_TRAVEL_FORBIDDEN);
        }

        return TravelStatusHistoryRes.fromEntites(travelStatusHistoryRepo.findAllByTravelId(travelId));
    }
}
