package com.tidesquare.btms.controller.user.auth.login;

import java.util.Date;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.constant.JoinStatus;
import com.tidesquare.btms.constant.UserType;
import com.tidesquare.btms.entity.Customer;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.CustomerRepo;
import com.tidesquare.btms.service.jwt.JwtService;

import at.favre.lib.crypto.bcrypt.BCrypt;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response.Status;

@ApplicationScoped
public class UserLoginHandler {

    @Inject
    private CustomerRepo customerRepo;

    @Inject
    private JwtService jwtService;

    public UserLoginRes run(UserLoginBody body) {
        String email = body.getEmail();
        String password = body.getPassword();

        Customer user = this.customerRepo.findFetchWorkspaceByEmailAndJoinStatus(UserType.customer, email, JoinStatus.Approval);

        if (user == null) {
            throw new ApiException(Status.FORBIDDEN, ErrorCode.E403);
        }

        BCrypt.Result result = BCrypt.verifyer().verify(password.toCharArray(), user.getPassword());
        if (!result.verified) {
            throw new ApiException(Status.FORBIDDEN, ErrorCode.E403);
        }

        UserInfo userInfo = new UserInfo(user.getId(), user.getWorkspace().getCompany().getId(), UserType.customer, false);
        String accessToken = this.jwtService.createToken(userInfo);

        UserLoginRes res = new UserLoginRes();
        res.setAccessToken(accessToken);
        Date passwordUpdateDate = user.getPasswordUpdateDate() == null ? user.getCreateDate() : user.getPasswordUpdateDate();
        Date nextPasswordUpdateDate = user.getNextPasswordUpdateDate();

        if (passwordUpdateDate.getTime() + 6 * 30 * 24 * 60 * 60 * 1000L < System.currentTimeMillis()) {
            if (nextPasswordUpdateDate == null || nextPasswordUpdateDate.getTime() < System.currentTimeMillis()) {
                res.setNeedUpdatePassword(true);
            }
        }

        return res;
    }
}
