package com.tidesquare.btms.controller.user.travel.get_travel_overseas;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tidesquare.btms.dto.response.AmericaStayRes;
import com.tidesquare.btms.dto.response.TravelModifyRequestRes;
import com.tidesquare.btms.dto.response.TravelRes;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class GetTravelRes {
    @JsonProperty("isAmericaSchedule")
    private boolean isAmericaSchedule;
    private TravelRes travel;
    private AmericaStayRes americaStay;
    private List<TravelModifyRequestRes> travelModifyRequestRes;
}
