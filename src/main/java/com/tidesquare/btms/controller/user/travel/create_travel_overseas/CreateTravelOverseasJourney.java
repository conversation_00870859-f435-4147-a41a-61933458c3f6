package com.tidesquare.btms.controller.user.travel.create_travel_overseas;

import com.tidesquare.btms.constant.SectionType;
import com.tidesquare.btms.validator.ValidDateString;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateTravelOverseasJourney {
    @NotBlank
    private String airline;

    @ValidDateString(format = "yyyy-MM-dd'T'HH:mm:ss")
    @NotNull
    private String departureDate;

    @ValidDateString(format = "yyyy-MM-dd'T'HH:mm:ss")
    @NotNull
    private String arrivalDate;

    @NotBlank
    private String journeyKey;

    @NotBlank
    private String fareKey;

    @NotBlank
    private String deptAirport;

    @NotBlank
    private String arrAirport;

    @NotBlank
    private String pairKey;

    @NotNull
    private SectionType journeyType;
    private float airFare;
    private float airTax;
    private float fuelChg;
    private float tkFee;
}
