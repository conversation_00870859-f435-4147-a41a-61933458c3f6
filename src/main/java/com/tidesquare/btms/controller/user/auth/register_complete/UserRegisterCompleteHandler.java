package com.tidesquare.btms.controller.user.auth.register_complete;

import java.util.Date;

import com.tidesquare.btms.constant.Constants;
import com.tidesquare.btms.constant.JoinAuthType;
import com.tidesquare.btms.constant.JoinStatus;
import com.tidesquare.btms.constant.Role;
import com.tidesquare.btms.constant.UserType;
import com.tidesquare.btms.entity.Agreement;
import com.tidesquare.btms.entity.Customer;
import com.tidesquare.btms.entity.Department;
import com.tidesquare.btms.entity.IdentificationRequest;
import com.tidesquare.btms.entity.Position;
import com.tidesquare.btms.entity.Workspace;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.AgreementRepo;
import com.tidesquare.btms.repository.CustomerRepo;
import com.tidesquare.btms.repository.IdentificationRequestRepo;
import com.tidesquare.btms.repository.WorkspaceRepo;

import at.favre.lib.crypto.bcrypt.BCrypt;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response.Status;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class UserRegisterCompleteHandler {

    @Inject
    private IdentificationRequestRepo identificationRequestRepo;

    @Inject
    private CustomerRepo customerRepo;

    @Inject
    private WorkspaceRepo workspaceRepo;

    @Inject
    private AgreementRepo agreementRepo;

    @Transactional(rollbackOn = Exception.class)
    public void run(UserRegisterCompleteBody body) {
        IdentificationRequest identificationRequest = this.identificationRequestRepo.findByVerificationMeansAccount(body.getEmail());
        if (identificationRequest == null) {
            throw new ApiException(Status.BAD_REQUEST, ErrorCode.E400_INVALID_OTP);
        }

        if (!identificationRequest.getVerificationCode().equals(body.getVerificationCode())) {
            throw new ApiException(Status.BAD_REQUEST, ErrorCode.E400_INVALID_OTP);
        }

        if (identificationRequest.getRequestDate().getTime() + 10 * 60 * 1000L < System.currentTimeMillis()) { // 10 minutes
            throw new ApiException(Status.BAD_REQUEST, ErrorCode.E400_OTP_EXPIRED);
        }

        Agreement agreement = new Agreement();
        agreement.setAgreePrivacyMarketing(body.isAgreePrivacyMarketing());
        this.agreementRepo.insert(agreement);

        Customer customer = new Customer();
        customer.setUserType(UserType.customer);
        customer.setEmail(body.getEmail());
        customer.setPassword(BCrypt.withDefaults().hashToString(12, body.getPassword().toCharArray()));
        customer.setName(body.getName());
        customer.setGender(body.getGender());
        customer.setBirthday(body.getBirthday());
        customer.setCellPhoneNumber(body.getCellPhoneNumber());
        customer.setJoinAuthType(JoinAuthType.EmailAuth);
        customer.setJoinStatus(JoinStatus.Approval);
        customer.setCountryId(Constants.COUNTRY_ID_IS_KOREAN);
        customer.setRole(Role.ROLE_CUSTOMER);
        customer.setJoinDate(new Date());
        customer.setLoginId(body.getEmail().split("@")[0]);
        customer.setAgreement(agreement);
        if (body.getWorkspaceId() == null) {
            customer.setWorkspace(this.workspaceRepo.findCreatedFirstByCompanyId(body.getCompanyId()));
        } else {
            customer.setWorkspace(new Workspace(body.getWorkspaceId()));
        }
        if (body.getDepartmentId() != null) {
            customer.setDepartment(new Department(body.getDepartmentId()));
        }
        if (body.getPositionId() != null) {
            customer.setPosition(new Position(body.getPositionId()));
        }
        customer.setEmployeeNo(body.getEmployeeNo());
        customer.setPhoneNumber(body.getPhoneNumber());
        this.customerRepo.insert(customer);
    }
}
