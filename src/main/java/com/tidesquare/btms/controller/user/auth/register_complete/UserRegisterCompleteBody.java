package com.tidesquare.btms.controller.user.auth.register_complete;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tidesquare.btms.constant.Gender;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UserRegisterCompleteBody {
    @NotNull
    @Email
    private String email;

    @NotNull
    private String verificationCode;

    @NotNull
    private String password;

    @NotNull
    private String name;

    @NotNull
    private Gender gender;

    @NotNull
    private String birthday;

    @NotNull
    private String cellPhoneNumber;

    @NotNull
    private Long companyId;

    private Long workspaceId;

    private Long departmentId;

    private Long positionId;

    private String employeeNo;

    private String phoneNumber;

    @JsonProperty("isAgreePrivacyMarketing")
    private boolean isAgreePrivacyMarketing;

}
