package com.tidesquare.btms.controller.user.auth;

import org.eclipse.microprofile.openapi.annotations.Operation;

import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.controller.user.auth.login.UserLoginBody;
import com.tidesquare.btms.controller.user.auth.login.UserLoginHandler;
import com.tidesquare.btms.controller.user.auth.login.UserLoginRes;
import com.tidesquare.btms.controller.user.auth.register.UserRegisterBody;
import com.tidesquare.btms.controller.user.auth.register.UserRegisterHandler;
import com.tidesquare.btms.controller.user.auth.register_complete.UserRegisterCompleteBody;
import com.tidesquare.btms.controller.user.auth.register_complete.UserRegisterCompleteHandler;
import com.tidesquare.btms.controller.user.auth.register_verify.UserRegisterVerifyBody;
import com.tidesquare.btms.controller.user.auth.register_verify.UserRegisterVerifyHandler;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;

@Path("user/auth")
public class UserAuthController {

    @Inject
    private UserLoginHandler userLoginHandler;

    @Inject
    private UserRegisterHandler userRegisterHandler;

    @Inject
    private UserRegisterVerifyHandler userRegisterVerifyHandler;

    @Inject
    private UserRegisterCompleteHandler userRegisterCompleteHandler;

    @POST
    @Path("/login")
    @Operation(summary = "Login")
    public ApiResponse<UserLoginRes> login(@Valid UserLoginBody body) {
        return ApiResponse.fromData(this.userLoginHandler.run(body));
    }

    @POST
    @Path("/register")
    @Operation(summary = "Register")
    public ApiResponse<Void> register(@Valid UserRegisterBody body) {
        this.userRegisterHandler.run(body);
        return ApiResponse.fromData(null);
    }

    @POST
    @Path("/register-verify")
    @Operation(summary = "Verify Register")
    public ApiResponse<Void> registerVerify(@Valid UserRegisterVerifyBody body) {
        this.userRegisterVerifyHandler.run(body);
        return ApiResponse.fromData(null);
    }

    @POST
    @Path("/register-complete")
    @Operation(summary = "Complete Register")
    public ApiResponse<Void> registerComplete(@Valid UserRegisterCompleteBody body) {
        this.userRegisterCompleteHandler.run(body);
        return ApiResponse.fromData(null);
    }
}
