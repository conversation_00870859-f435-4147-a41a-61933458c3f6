package com.tidesquare.btms.controller.user.travel.create_compare_schedule;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tidesquare.btms.service.stella.dto.request.JourneyReq;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateCompareScheduleSchedule {
    private List<JourneyReq> journeys;
    @JsonProperty("isRuleViolation")
    private boolean isRuleViolation;
}
