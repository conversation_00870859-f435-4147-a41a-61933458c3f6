package com.tidesquare.btms.controller.user.travel.get_travel_rule;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.dto.response.TravelFareRuleRecordRes;
import com.tidesquare.btms.entity.TravelFareRuleRecord;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.TravelFareRuleRecordRepo;
import com.tidesquare.btms.service.travel.TravelService;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response.Status;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class GetTravelFareRuleHandler {

    @Inject
    private TravelFareRuleRecordRepo travelFareRuleRecordRepo;

    @Inject
    private TravelService travelService;

    public GetTravelFareRuleRes run(UserInfo userInfo, Long travelId) {
        boolean isPermission = travelService.checkUserIsReverserOrTravelerOfTravel(userInfo.getUserId(), travelId);
        if (!isPermission) {
            throw new ApiException(Status.FORBIDDEN, ErrorCode.E403_TRAVEL_FORBIDDEN);
        }

        ObjectMapper objectMapper = new ObjectMapper();
        List<TravelFareRuleRecord> travelFareRuleRecords = this.travelFareRuleRecordRepo.findByTravelId(travelId);

        AtomicInteger index = new AtomicInteger();
        return GetTravelFareRuleRes.builder()
                .travelRuleItems(travelFareRuleRecords.stream()
                        .map(record -> {
                            String fareRuleStr = record.getFareRuleStr();
                            if (fareRuleStr == null) {
                                log.info("fareRuleStr is null for record: {}", record);
                                return null;
                            }
                            try {
                                TravelFareRuleRecordRes travelRuleRes = objectMapper.readValue(fareRuleStr, TravelFareRuleRecordRes.class);
                                travelRuleRes.setFareComponentNo(index.getAndIncrement() + 1);
                                return travelRuleRes;
                            } catch (Exception e) {
                                log.error("Error processing JSON for record: {}", e);
                                e.printStackTrace();
                                return null;
                            }
                        })
                        .filter(Objects::nonNull)
                        .toList())
                .build();
    }
}
