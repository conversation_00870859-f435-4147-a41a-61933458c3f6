package com.tidesquare.btms.controller.user.travel.get_travel_overseas;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.constant.BookingType;
import com.tidesquare.btms.constant.TravelStatus;
import com.tidesquare.btms.constant.VoidType;
import com.tidesquare.btms.dto.response.AmericaStayRes;
import com.tidesquare.btms.dto.response.BookingAirRes;
import com.tidesquare.btms.dto.response.TravelRes;
import com.tidesquare.btms.dto.response.BookingAirTravelerRes;
import com.tidesquare.btms.dto.response.DocumentNumberRes;
import com.tidesquare.btms.dto.response.TravelModifyRequestRes;
import com.tidesquare.btms.entity.BookingAirSchedule;
import com.tidesquare.btms.entity.BookingAirTicket;
import com.tidesquare.btms.entity.Travel;
import com.tidesquare.btms.entity.Traveler;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.AmericaStayRepo;
import com.tidesquare.btms.repository.BookingAirTicketRepo;
import com.tidesquare.btms.repository.DocumentNumberRepo;
import com.tidesquare.btms.repository.TravelModifyRequestRepo;
import com.tidesquare.btms.repository.TravelRepo;
import com.tidesquare.btms.repository.TravelerRepo;
import com.tidesquare.btms.service.CommonService;
import com.tidesquare.btms.service.travel.TravelService;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response.Status;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.SortedSet;

@Slf4j
@ApplicationScoped
public class GetTravelOverseasHandler {
	@Inject
	private TravelRepo travelRepo;

	@Inject
	private TravelService travelService;

	@Inject
	private TravelerRepo travelerRepo;

	@Inject
	private BookingAirTicketRepo bookingAirTicketRepo;

	@Inject
	private DocumentNumberRepo documentNumberRepo;

	@Inject
	private AmericaStayRepo americaStayRepo;

	@Inject
	private TravelModifyRequestRepo travelModifyRequestRepo;

	@Inject
	private CommonService commonService;

	public GetTravelRes run(UserInfo userInfo, long travelId) {
		boolean isPermission = travelService.checkUserIsReverserOrTravelerOfTravel(userInfo.getUserId(), travelId);
		if (!isPermission) {
			throw new ApiException(Status.FORBIDDEN, ErrorCode.E403_TRAVEL_FORBIDDEN);
		}

		Travel travel = travelRepo.findById(travelId);
		TravelRes travelRes = TravelRes.fromEntity(travel);

		//set reserver
		Traveler traveler = travelerRepo.findByTravelIdAndIsReserverTrue(travelId);
		travelRes.setReserver(traveler);

		// set document number
		travelRes.setDocumentNumbers(DocumentNumberRes.fromEntities(documentNumberRepo.findByBookingIdAndBookingType(travel.getId(), BookingType.AIR)));

		// caculate detail info
		travelRes = reCalculateTotalAmountDetailInfo(travelRes);

		// travel to america
		SortedSet<BookingAirSchedule> bookingAirSchedules = travel.getBookingAir().getBookingAirSchedules();
		boolean isAmerica = this.commonService.isAmericaSchedules(bookingAirSchedules);
		AmericaStayRes americaStayRes = isAmerica ? AmericaStayRes.fromEntity(americaStayRepo.findByTravelId(travelId)) : null;

		// travel modify request
		List<TravelModifyRequestRes> travelModifyRequestRes = TravelModifyRequestRes.fromEntities(travelModifyRequestRepo.findByTravelId(travelId));
		List<TravelModifyRequestRes> result = new ArrayList<>();
		Map<Long, List<TravelModifyRequestRes>> childrenMap = new HashMap<>();
		List<TravelModifyRequestRes> rootNodes = new ArrayList<>();

		for (TravelModifyRequestRes item : travelModifyRequestRes) {
			if (item.getParentTravelModifyRequestId().equals(0L)) {
				rootNodes.add(item);
			} else {
				childrenMap.computeIfAbsent(item.getParentTravelModifyRequestId(), k -> new ArrayList<>()).add(item);
			}
		}
		for (TravelModifyRequestRes root : rootNodes) {
			this.buildFlatHierarchyRecursive(childrenMap, root, result);
		}

		return GetTravelRes.builder()
				.travel(travelRes)
				.isAmericaSchedule(isAmerica)
				.americaStay(americaStayRes)
				.travelModifyRequestRes(result)
				.build();
	}

	private boolean isVoidTicket(BookingAirTicket ticket) {
		return EnumSet
				.of(VoidType.SpoiledVoid, VoidType.Default, VoidType.Emd, VoidType.Exchange)
				.contains(ticket.getVoidType());
	}

	private TravelRes reCalculateTotalAmountDetailInfo(TravelRes travel) {
		BookingAirRes bookingAir = travel.getBookingAir();
		if (bookingAir == null) {
			return null;
		}
		List<BookingAirTravelerRes> bookingAirTravelerList = travel.getBookingAir().getBookingAirTravelers();
		List<BookingAirTicket> bookingAirTicketList = bookingAirTicketRepo.findAllByBookingAirId(travel.getBookingAir().getId());

		double fareAmount = 0d;
		double taxAmount = 0d;
		double fuelSurcharge = 0d;
		double commissionAmount = 0d;
		double dcAmount = 0d;
		int adultCount = (null != bookingAirTravelerList && !bookingAirTravelerList.isEmpty()) ? bookingAirTravelerList.size() : 0;

		if (TravelStatus.Completed.equals(travel.getStatus())) {
			if (null != bookingAirTicketList && !bookingAirTicketList.isEmpty()) {
				for (BookingAirTicket ticket : bookingAirTicketList) {
					if (isVoidTicket(ticket)) {
						fareAmount += Optional.ofNullable(ticket.getFareAmount()).orElse(0d);
						taxAmount += Optional.ofNullable(ticket.getTaxAmount()).orElse(0d);
						commissionAmount += Optional.ofNullable(ticket.getCommissionAmount()).orElse(0d);
						dcAmount += Optional.ofNullable(ticket.getDcAmount()).orElse(0d);
					}
				}
			}
		} else {
			if (null != bookingAirTravelerList && !bookingAirTravelerList.isEmpty()) {
				for (BookingAirTravelerRes traveler : bookingAirTravelerList) {
					fareAmount += Optional.ofNullable(traveler.getFareAmount()).orElse(0d);
					taxAmount += Optional.ofNullable(traveler.getTax()).orElse(0d);
					fuelSurcharge += Optional.ofNullable(traveler.getFuelSurcharge()).orElse(0d);
					commissionAmount += Optional.ofNullable(traveler.getCommissionAmount()).orElse(0d);
					dcAmount += Optional.ofNullable(traveler.getDcAmount()).orElse(0d);
				}
			}
		}

		double totalAmount = fareAmount - dcAmount + taxAmount + commissionAmount;
		bookingAir.setTotalAmount(totalAmount);
		bookingAir.setFareAmount(fareAmount);
		bookingAir.setTaxAmount(taxAmount);
		bookingAir.setCommissionAmount(commissionAmount);
		bookingAir.setFuelSurcharge(fuelSurcharge);
		bookingAir.setDcAmount(dcAmount);
		bookingAir.setAdultCount(adultCount);
		travel.setBookingAir(bookingAir);

		return travel;
	}

	private void buildFlatHierarchyRecursive(
			Map<Long, List<TravelModifyRequestRes>> childrenMap,
			TravelModifyRequestRes parent,
			List<TravelModifyRequestRes> result) {

		result.add(parent);
		List<TravelModifyRequestRes> children = childrenMap.get(parent.getTravelModifyRequestId());
		if (children != null) {
			for (TravelModifyRequestRes child : children) {
				buildFlatHierarchyRecursive(childrenMap, child, result);
			}
			parent.setIsLeaf("0");
		} else {
			parent.setIsLeaf("1");
		}
	}
}
