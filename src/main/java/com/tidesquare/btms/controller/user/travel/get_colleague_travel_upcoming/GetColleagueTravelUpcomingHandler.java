package com.tidesquare.btms.controller.user.travel.get_colleague_travel_upcoming;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.SortedSet;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.constant.TravelStatus;
import com.tidesquare.btms.dto.response.TravelRes;
import com.tidesquare.btms.entity.BookingAir;
import com.tidesquare.btms.entity.BookingAirSchedule;
import com.tidesquare.btms.entity.BookingAirTraveler;
import com.tidesquare.btms.entity.BookingAirTraveler_;
import com.tidesquare.btms.entity.BookingAir_;
import com.tidesquare.btms.entity.Travel;
import com.tidesquare.btms.entity.Travel_;
import com.tidesquare.btms.entity.User_;
import com.tidesquare.btms.repository.BookingAirScheduleRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Fetch;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class GetColleagueTravelUpcomingHandler {

    @Inject
    private StatelessSession statelessSession;

    @Inject
    private BookingAirScheduleRepo bookingAirScheduleRepo;

    public List<TravelRes> run(UserInfo userInfo, GetColleagueTravelUpcomingQuery query) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Travel> q = builder.createQuery(Travel.class);
        Root<Travel> root = q.from(Travel.class);
        Fetch<Travel, BookingAir> bookingAirFetch = root.fetch(Travel_.bookingAir, JoinType.INNER);
        @SuppressWarnings("unchecked")
        Join<Travel, BookingAir> bookingAirJoin = (Join<Travel, BookingAir>) bookingAirFetch;
        Join<BookingAir, BookingAirTraveler> bookingAirTravelerJoin = bookingAirJoin.join(BookingAir_.bookingAirTravelers, JoinType.INNER);
        bookingAirTravelerJoin.on(builder.equal(bookingAirTravelerJoin.get(BookingAirTraveler_.traveler).get(User_.id), query.getUserId()));
        q.select(root);

        List<Predicate> predicates = new ArrayList<>();
        predicates.add(root.get(Travel_.status).in(TravelStatus.Requested, TravelStatus.Approved, TravelStatus.Completed));
        predicates.add(builder.greaterThan(bookingAirJoin.get(BookingAir_.bookingDate), new Date(System.currentTimeMillis() - 15 * 24 * 60 * 60 * 1000L)));
        if (query.getIsOverseas() != null) {
            predicates.add(builder.equal(root.get(Travel_.isOverseas), query.getIsOverseas()));
        }
        q.where(predicates.toArray(new Predicate[0]));

        q.orderBy(builder.desc(bookingAirJoin.get(BookingAir_.bookingDate)));

        List<Travel> travels = this.statelessSession.createSelectionQuery(q).setMaxResults(3).getResultList();
        if (travels.isEmpty()) {
            return List.of();
        }

        List<Long> bookingAirIds = travels.stream().map(travel -> travel.getBookingAir().getId()).toList();
        Map<Long, SortedSet<BookingAirSchedule>> bookingAirScheduleMap = this.bookingAirScheduleRepo.getBookingAirSchedulesByBookingAirIds(bookingAirIds);
        travels.forEach(travel -> {
            travel.getBookingAir().setBookingAirSchedules(bookingAirScheduleMap.get(travel.getBookingAir().getId()));
        });

        return TravelRes.fromEntities(travels);
    }
}
