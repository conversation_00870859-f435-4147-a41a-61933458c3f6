package com.tidesquare.btms.controller.user.auth.register;

import java.util.Date;

import com.tidesquare.btms.constant.JoinStatus;
import com.tidesquare.btms.constant.UserType;
import com.tidesquare.btms.entity.Customer;
import com.tidesquare.btms.entity.IdentificationRequest;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.CustomerRepo;
import com.tidesquare.btms.repository.IdentificationRequestRepo;
import com.tidesquare.btms.service.email.EmailService;
import com.tidesquare.btms.utils.StringUtils;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response.Status;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class UserRegisterHandler {

    @Inject
    private CustomerRepo customerRepo;

    @Inject
    private IdentificationRequestRepo identificationRequestRepo;

    @Inject
    private EmailService emailService;

    public void run(UserRegisterBody body) {
        Customer existCustomer = this.customerRepo.findByEmailAndJoinStatus(UserType.customer, body.getEmail(), JoinStatus.Approval);
        if (existCustomer != null) {
            throw new ApiException(Status.BAD_REQUEST, ErrorCode.E400_EMAIL_EXISTED);
        }

        String verificationCode = StringUtils.generateRandomString(8);
        IdentificationRequest identificationRequest = new IdentificationRequest();
        identificationRequest.setVerificationCode(verificationCode);
        identificationRequest.setVerificationMeans(IdentificationRequest.Means.Email);
        identificationRequest.setVerificationMeansAccount(body.getEmail());
        identificationRequest.setRequestDate(new Date());
        this.identificationRequestRepo.insert(identificationRequest);

        this.emailService.sendVerificationCodeEmail(identificationRequest);
    }
}
