package com.tidesquare.btms.controller.user.company;

import java.util.List;

import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.constant.ManagerType;
import com.tidesquare.btms.controller.agency.company.reward_mile_setting.RewardMileSettingRes;
import com.tidesquare.btms.controller.user.company.get_btms_manager.UserGetBtmsManagerHandler;
import com.tidesquare.btms.controller.user.company.get_flight_setting.GetFlightSettingHandler;
import com.tidesquare.btms.dto.response.BtmsManagerRes;
import com.tidesquare.btms.entity.RewardMileSetting;
import com.tidesquare.btms.filter.UserBinding;
import com.tidesquare.btms.repository.RewardMileSettingRepo;

import io.vertx.mutiny.core.Vertx;
import jakarta.inject.Inject;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.QueryParam;

@Path("user/company")
@UserBinding
@SecurityRequirement(name = "bearer")
public class CompanyController {

  @Inject
  private RewardMileSettingRepo rewardMileSettingRepo;

  @Inject
  private GetFlightSettingHandler getFlightSettingHandler;

  @Inject
  private UserGetBtmsManagerHandler userGetBtmsManagerHandler;

  @GET
  @Path("/btmsManager")
  @Operation(summary = "Get Btms Managers from company")
  public ApiResponse<List<BtmsManagerRes>> geBtmsManagers(@QueryParam("managerType") ManagerType managerType, @QueryParam("isOverseas") Boolean isOverseas) {
    UserInfo userInfo = Vertx.currentContext().getLocal("user");
    List<BtmsManagerRes> data = this.userGetBtmsManagerHandler.run(userInfo, managerType, isOverseas);
    return ApiResponse.fromData(data);
  }

  @GET
  @Path("/reward-mile/setting")
  @Operation(summary = "Get setting reward mile for company")
  public ApiResponse<RewardMileSettingRes> getRewardMileSettingCompany() {
    UserInfo userInfo = Vertx.currentContext().getLocal("user");
    RewardMileSetting rewardMileSetting = rewardMileSettingRepo.get(userInfo.getCompanyId());
    RewardMileSettingRes rewardMileSettingRes = RewardMileSettingRes.builder()
        .comanyId(rewardMileSetting.getCompanyId())
        .ratio(rewardMileSetting.getRatio())
        .rewardMileType(rewardMileSetting.getRewardMileType())
        .maximumAccumulatedMiles(rewardMileSetting.getMaximumAccumulatedMiles())
        .isUse(rewardMileSetting.getIsUse())
        .build();
    return ApiResponse.fromData(rewardMileSettingRes);
  }

  @GET
  @Path("/flight-setting")
  @Operation(summary = "Get flight setting")
  public ApiResponse<String> getFlightSetting() {
    UserInfo userInfo = Vertx.currentContext().getLocal("user");
    String flightSetting = this.getFlightSettingHandler.run(userInfo);
    return ApiResponse.fromData(flightSetting);
  }

}
