package com.tidesquare.btms.controller.user.travel.get_travel_rule;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.Builder;
import lombok.AllArgsConstructor;
import java.util.List;

import com.tidesquare.btms.dto.response.TravelFareRuleRecordRes;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetTravelFareRuleRes {
    private List<TravelFareRuleRecordRes> travelRuleItems;
}
