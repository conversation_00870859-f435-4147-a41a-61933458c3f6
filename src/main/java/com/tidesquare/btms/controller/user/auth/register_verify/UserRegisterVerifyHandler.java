package com.tidesquare.btms.controller.user.auth.register_verify;

import com.tidesquare.btms.entity.IdentificationRequest;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.IdentificationRequestRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response.Status;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class UserRegisterVerifyHandler {

    @Inject
    private IdentificationRequestRepo identificationRequestRepo;

    public void run(UserRegisterVerifyBody body) {
        IdentificationRequest identificationRequest = this.identificationRequestRepo.findByVerificationMeansAccount(body.getEmail());
        if (identificationRequest == null) {
            throw new ApiException(Status.BAD_REQUEST, ErrorCode.E400_INVALID_OTP);
        }

        if (!identificationRequest.getVerificationCode().equals(body.getVerificationCode())) {
            throw new ApiException(Status.BAD_REQUEST, ErrorCode.E400_INVALID_OTP);
        }

        if (identificationRequest.getRequestDate().getTime() + 5 * 60 * 1000L < System.currentTimeMillis()) { // 5 minutes
            throw new ApiException(Status.BAD_REQUEST, ErrorCode.E400_OTP_EXPIRED);
        }
    }
}
