package com.tidesquare.btms.controller.user.travel.get_invoice_overseas;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response.Status;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.dto.response.InvoiceMasterRes;
import com.tidesquare.btms.entity.BookingAir;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.BookingAirRepo;
import com.tidesquare.btms.repository.InvoiceMasterRepo;
import com.tidesquare.btms.service.travel.TravelService;

@ApplicationScoped
public class GetInvoicesHandler {
    @Inject
    private InvoiceMasterRepo invoiceMasterRepo;

    @Inject
    private BookingAirRepo bookingAirRepo;

    @Inject
    private TravelService travelService;

    public GetInvoiceRes run(UserInfo userInfo, long travelId) {
        boolean isPermission = travelService.checkUserIsReverserOrTravelerOfTravel(userInfo.getUserId(), travelId);
        if (!isPermission) {
            throw new ApiException(Status.FORBIDDEN, ErrorCode.E403_TRAVEL_FORBIDDEN);
        }

        BookingAir bookingAir = bookingAirRepo.findByTravelId(travelId);
        var invoiceMasters = InvoiceMasterRes.fromEntities(invoiceMasterRepo.findAllByBookingAirId(bookingAir.getId()));

        return GetInvoiceRes.builder()
                .invoiceMasters(invoiceMasters)
                .build();
    }
}
