package com.tidesquare.btms.controller.user.travel.create_travel_overseas;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.google.common.collect.Lists;
import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.constant.BookingType;
import com.tidesquare.btms.constant.Constants;
import com.tidesquare.btms.constant.GDSType;
import com.tidesquare.btms.constant.InflowBookingType;
import com.tidesquare.btms.constant.ManagerType;
import com.tidesquare.btms.constant.OPType;
import com.tidesquare.btms.constant.PnrDataHistoryType;
import com.tidesquare.btms.constant.TravelBookingType;
import com.tidesquare.btms.constant.TravelStatus;
import com.tidesquare.btms.entity.Airline;
import com.tidesquare.btms.entity.AttachFile;
import com.tidesquare.btms.entity.BookingAir;
import com.tidesquare.btms.entity.BookingAirAttachFile;
import com.tidesquare.btms.entity.BookingAirTraveler;
import com.tidesquare.btms.entity.BookingAir_;
import com.tidesquare.btms.entity.BtmsManager;
import com.tidesquare.btms.entity.BusinessTrip;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.Customer;
import com.tidesquare.btms.entity.Department;
import com.tidesquare.btms.entity.DocumentNumber;
import com.tidesquare.btms.entity.PnrDataHistory;
import com.tidesquare.btms.entity.Travel;
import com.tidesquare.btms.entity.TravelAgencyUser;
import com.tidesquare.btms.entity.TravelFareRuleRecord;
import com.tidesquare.btms.entity.TravelStatusHistory;
import com.tidesquare.btms.entity.Traveler;
import com.tidesquare.btms.entity.TravelerMileageInfo;
import com.tidesquare.btms.entity.User;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.AttachFileRepo;
import com.tidesquare.btms.repository.BookingAirAttachFileRepo;
import com.tidesquare.btms.repository.BookingAirRepo;
import com.tidesquare.btms.repository.BookingAirTravelerRepo;
import com.tidesquare.btms.repository.BtmsManagerRepo;
import com.tidesquare.btms.repository.CompanyRepo;
import com.tidesquare.btms.repository.CustomerRepo;
import com.tidesquare.btms.repository.DocumentNumberRepo;
import com.tidesquare.btms.repository.PnrDataHistoryRepo;
import com.tidesquare.btms.repository.TravelFareRuleRecordRepo;
import com.tidesquare.btms.repository.TravelRepo;
import com.tidesquare.btms.repository.TravelStatusHistoryRepo;
import com.tidesquare.btms.repository.TravelerMileageInfoRepo;
import com.tidesquare.btms.repository.TravelerRepo;
import com.tidesquare.btms.repository.UserRepo;
import com.tidesquare.btms.service.AirlineService;
import com.tidesquare.btms.service.email.EmailService;
import com.tidesquare.btms.service.kakao_talk.KakaoTalkService;
import com.tidesquare.btms.service.stella.StellaService;
import com.tidesquare.btms.service.stella.dto.AgencyItem;
import com.tidesquare.btms.service.stella.dto.Contact;
import com.tidesquare.btms.service.stella.dto.FareBasisRule;
import com.tidesquare.btms.service.stella.dto.FareBasisRuleItem;
import com.tidesquare.btms.service.stella.dto.FareInfo;
import com.tidesquare.btms.service.stella.dto.Name;
import com.tidesquare.btms.service.stella.dto.Passport;
import com.tidesquare.btms.service.stella.dto.PhoneType;
import com.tidesquare.btms.service.stella.dto.cancel_prn.CancelBody;
import com.tidesquare.btms.service.stella.dto.cancel_prn.CancelInfo;
import com.tidesquare.btms.service.stella.dto.get_fare_rule.GetFareRuleBody;
import com.tidesquare.btms.service.stella.dto.get_fare_rule.GetFareRuleResponse;
import com.tidesquare.btms.service.stella.dto.pnr_create.PNRCreateBody;
import com.tidesquare.btms.service.stella.dto.pnr_create.PNRCreateResponse;
import com.tidesquare.btms.service.stella.dto.request.JourneyReq;
import com.tidesquare.btms.service.stella.dto.request.PassengerInfoReq;
import com.tidesquare.btms.service.stella.dto.response.JourneyRes;
import com.tidesquare.btms.service.stella.dto.response.PNRInfoRes;
import com.tidesquare.btms.service.stella.dto.response.PassengerInfoRes;
import com.tidesquare.btms.service.travel.TravelService;
import com.tidesquare.btms.utils.AirUtil;
import com.tidesquare.btms.utils.DateUtil;
import com.tidesquare.btms.utils.PhoneNumberUtil;
import com.tidesquare.btms.utils.StringUtils;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.context.control.RequestContextController;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response.Status;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class CreateTravelOverseasHandler {
    @Inject
    private UserRepo userRepo;

    @Inject
    private AirlineService airlineService;

    @Inject
    private BtmsManagerRepo btmsManagerRepo;

    @Inject
    private CompanyRepo companyRepo;

    @Inject
    private CustomerRepo customerRepo;

    @Inject
    private TravelRepo travelRepo;

    @Inject
    private TravelerRepo travelerRepo;

    @Inject
    private BookingAirRepo bookingAirRepo;

    @Inject
    private BookingAirTravelerRepo bookingAirTravelerRepo;

    @Inject
    private TravelStatusHistoryRepo travelStatusHistoryRepo;

    @Inject
    private TravelerMileageInfoRepo travelerMileageInfoRepo;

    @Inject
    private DocumentNumberRepo documentNumberRepo;

    @Inject
    private TravelFareRuleRecordRepo travelFareRuleRecordRepo;

    @Inject
    private AttachFileRepo attachFileRepo;

    @Inject
    private BookingAirAttachFileRepo bookingAirAttachFileRepo;

    @Inject
    private PnrDataHistoryRepo pnrDataHistoryRepo;

    @Inject
    private StellaService stellaService;

    @Inject
    private TravelService travelService;

    @Inject
    private EmailService emailService;

    @Inject
    private KakaoTalkService kakaoTalkService;

    private ObjectMapper objectMapper = new ObjectMapper();

    @Inject
    private RequestContextController requestContextController;

    public CreateTravelOverseasRes run(UserInfo userInfo, CreateTravelOverseasBody body) {
        BookingAir bookingAir = this.createTravel(userInfo, body);

        try {
            log.info("*** CreateBookingAirBody: " + this.objectMapper.writeValueAsString(body));
        } catch (Exception e) {
            // TODO: handle exception
        }

        /* #region Save fare rule */
        Thread.startVirtualThread(() -> {
            this.saveFareRule(bookingAir.getTravel().getId(), body);
        });
        /* #endregion */

        /* #region update ticket date then send email and kakao */
        Thread.startVirtualThread(() -> {
            this.updateTicketDateAndViewTicketDateAndPnrData(bookingAir);
            this.sendEmailAndKakaoTalk(userInfo.getCompanyId(), bookingAir.getTravel().getId());
        });
        /* #endregion */

        return CreateTravelOverseasRes.builder().id(bookingAir.getTravel().getId()).build();
    }

    @Transactional(rollbackOn = Exception.class)
    protected BookingAir createTravel(UserInfo userInfo, CreateTravelOverseasBody body) {
        Customer customer = this.customerRepo.findOne(userInfo.getUserId());
        Company company = this.companyRepo.findById(userInfo.getCompanyId());
        BtmsManager btmsManager = this.btmsManagerRepo.findOneAndFetchDepartmentByCompanyIdAndIsOverseasAndManagerType(company.getId(), true, ManagerType.System_Etc);

        List<Long> travelerUserIds = body.getPassengers().stream().filter(passenger -> passenger.getUserID() != 0)
                .map(passenger -> passenger.getUserID()).collect(Collectors.toList());
        Map<Long, User> travelerUsers = this.userRepo.findByIds(travelerUserIds)
                .stream().collect(Collectors.toMap(User::getId, user -> user));
        if (travelerUserIds.size() != travelerUsers.size()) {
            throw new ApiException(Status.BAD_REQUEST, ErrorCode.E400);
        }

        /* #region Build pnrCreateBody and call stella CreatePNR */
        PNRCreateBody pnrCreateBody = new PNRCreateBody();
        pnrCreateBody.setSsCode("BTMS");
        pnrCreateBody.setContacts(Lists.newArrayList(
                Contact.builder()
                        .contactType("N")
                        .email(customer.getEmail())
                        .name(Name.builder()
                                .firstName(customer.getName().substring(0, 1).toUpperCase())
                                .lastName(customer.getName().substring(1).toUpperCase())
                                .middleName("").build())
                        .phoneTypes(Lists.newArrayList(PhoneType.builder().number(PhoneNumberUtil.formatPhoneNumber(customer.getCellPhoneNumber(), "-")).type("MOBILE").build()))
                        .build()));
        pnrCreateBody.setJourneys(new ArrayList<JourneyReq>());
        for (CreateTravelOverseasJourney journey : body.getJourneys()) {
            pnrCreateBody.getJourneys().add(JourneyReq.builder()
                    .journeyKey(journey.getJourneyKey())
                    .fareKey(journey.getFareKey())
                    .pairKey(journey.getPairKey())
                    .journeyType(journey.getJourneyType())
                    .airline(journey.getAirline())
                    .departureDate(journey.getDepartureDate())
                    .deptAirport(journey.getDeptAirport())
                    .arrivalDate(journey.getArrivalDate())
                    .arrAirport(journey.getArrAirport())
                    .build());
        }

        ArrayList<PassengerInfoReq> passengerInfosReq = new ArrayList<>();
        for (int i = 0; i < body.getPassengers().size(); i++) {
            CreateTravelOverseasPassenger passenger = body.getPassengers().get(i);
            PassengerInfoReq passengerInfo = PassengerInfoReq.builder()
                    .passengerIdx(i + 1)
                    .paxType("ADT")
                    .dateOfBirth(passenger.getDateOfBirth())
                    .gender(passenger.getGender())
                    .nationality(passenger.getNationality())
                    .name(Name.builder().firstName(passenger.getEnglishFirstName().toUpperCase())
                            .lastName(passenger.getEnglishLastName().toUpperCase()).middleName("").build())
                    .build();
            if (passenger.getPassportDocumentNo() != null && !passenger.getPassportDocumentNo().isEmpty()) {
                passengerInfo.setPassport(Passport.builder()
                        .documentNo(passenger.getPassportDocumentNo())
                        .expirationDate(passenger.getPassportExpirationDate())
                        .issuedByCode(passenger.getPassportIssuedByCode())
                        .nationality(passenger.getNationality())
                        .build());
            }
            ArrayList<FareInfo> fareInfos = new ArrayList<>();
            for (CreateTravelOverseasJourney journey : body.getJourneys()) {
                FareInfo fareInfo = FareInfo.builder()
                        .airFare(journey.getAirFare())
                        .airTax(journey.getAirTax())
                        .fuelChg(journey.getFuelChg())
                        .tkFee(journey.getTkFee())
                        .journeyKey(journey.getJourneyKey())
                        .fareKey(journey.getFareKey())
                        .build();

                fareInfos.add(fareInfo);
            }
            passengerInfo.setFareInfos(fareInfos);

            passengerInfosReq.add(passengerInfo);
        }
        pnrCreateBody.setPassengerInfos(passengerInfosReq);

        PNRCreateResponse pnrCreateResponse = this.stellaService.CreatePNR(pnrCreateBody);
        try {
            List<JourneyRes> journeysRes = pnrCreateResponse.getJourneys();
            List<PassengerInfoRes> passengerInfosRes = pnrCreateResponse.getPassengerInfos();
            /* #endregion */

            // Build and save BusinessTrip
            BusinessTrip businessTrip = this.travelService.upsertBusinessTrip(null, journeysRes);

            /* #region Build and save Travel */
            Travel travel = new Travel();
            travel.setBusinessTripId(businessTrip.getId());
            travel.setIsGroup(false);
            travel.setTravelPlace(this.travelService.getTravelPlace(journeysRes));
            travel.setTravelBookingType(TravelBookingType.Air);
            travel.setWorkspaceId(customer.getWorkspace().getId());
            travel.setCompany(company);
            travel.setIsOverseas(true);
            travel.setSeatZone("all");
            travel.setViolationReason(body.getViolationReason());
            travel.setDepartYmd(pnrCreateResponse.getJourneys().getFirst().getDepartureDate().substring(0, 10).replaceAll("-", ""));
            travel.setReturnYmd(pnrCreateResponse.getJourneys().getLast().getArrivalDate().substring(0, 10).replaceAll("-", ""));
            travel.setTravelPersonnel(pnrCreateResponse.getPassengerInfos().size());
            travel.setStatus(TravelStatus.Requested);
            travel.setCreator(customer);
            travel.setModifier(customer);
            this.travelRepo.insert(travel);
            /* #endregion */

            /* #region Build and save TravelStatusHistory */
            TravelStatusHistory travelStatusHistory = new TravelStatusHistory();
            travelStatusHistory.setTravelId(travel.getId());
            travelStatusHistory.setModifier(new User(userInfo.getUserId()));
            travelStatusHistory.setTravelBookingType(travel.getTravelBookingType());
            travelStatusHistory.setStatus(travel.getStatus());
            travelStatusHistory.setModifyInfo(travel.getStatus().getModifyInfo());
            travelStatusHistory.setModifyDate(new Date());
            this.travelStatusHistoryRepo.insert(travelStatusHistory);
            /* #endregion */

            /* #region Build and save Traveler */
            Traveler reserverTraveler = new Traveler();
            reserverTraveler.setTravelId(travel.getId());
            reserverTraveler.setTravelerUserId(customer.getId());
            reserverTraveler.setAccountingCode(customer.getAccountingCode());
            reserverTraveler.setEmail(customer.getEmail());
            reserverTraveler.setName(customer.getName());
            reserverTraveler.setCellPhoneNumber(customer.getCellPhoneNumber());
            reserverTraveler.setReserver(true);
            this.travelerRepo.insert(reserverTraveler);
            for (CreateTravelOverseasPassenger passenger : body.getPassengers()) {
                Traveler traveler = new Traveler();
                traveler.setTravelId(travel.getId());
                if (passenger.getUserID() != 0 && travelerUsers.get(passenger.getUserID()) != null) {
                    traveler.setTravelerUserId(passenger.getUserID());
                    traveler.setAccountingCode(travelerUsers.get(passenger.getUserID()).getAccountingCode());
                }
                traveler.setEmail(passenger.getEmail());
                traveler.setFirstName(passenger.getEnglishFirstName().toUpperCase());
                traveler.setLastName(passenger.getEnglishLastName().toUpperCase());
                traveler.setName(passenger.getKoreanName());
                traveler.setCellPhoneNumber(passenger.getPhoneNumber().replaceAll("-", ""));
                traveler.setReserver(false);
                this.travelerRepo.insert(traveler);
            }
            /* #endregion */

            // Build and save Travel City
            this.travelService.saveTravelCities(true, travel.getId(), pnrCreateResponse.getJourneys());

            /* #region Build and save BookingAir */
            BookingAir bookingAir = new BookingAir();
            bookingAir.setPnrNo(pnrCreateResponse.getJourneys().getFirst().getPnrNumber());
            bookingAir.setOtherPnrNo(pnrCreateResponse.getJourneys().getFirst().getPnrNumber());
            bookingAir.setOrderID(pnrCreateResponse.getJourneys().getFirst().getOrderId());
            bookingAir.setOrderKey(pnrCreateResponse.getOrderKey());
            bookingAir.setInflowBookingType(InflowBookingType.BTMS);
            bookingAir.setBookingDate(DateUtil.string2Date(pnrCreateResponse.getCreateDate(), "yyyyMMddHHmmss"));
            bookingAir.setTravel(travel);
            bookingAir.setGdsType(pnrCreateResponse.getJourneys().getFirst().getProvider());
            if (pnrCreateResponse.getJourneys().getFirst().getProvider().equals(GDSType.AMADEUS)) {
                bookingAir.setOfficeId(Constants.AMADEUS_ONLINE_OID);
            } else if (pnrCreateResponse.getJourneys().getFirst().getProvider().equals(GDSType.SABRE)) {
                bookingAir.setOfficeId(Constants.SABRE_ONLINE_PCC);
            } else {
                throw new ApiException(Status.BAD_REQUEST, ErrorCode.E400);
            }
            bookingAir.setSectionType(pnrCreateResponse.getJourneys().getFirst().getJourneyType());
            bookingAir.setIsAppointReturnDay(true);
            bookingAir.setStayTerm(String.valueOf(DateUtil.diffDays(travel.getReturnYmd(), travel.getDepartYmd(), "yyyyMMdd")));
            Airline ticketAirline = this.airlineService.findOrInsertByCode(pnrCreateResponse.getJourneys().getFirst().getAirline());
            bookingAir.setTicketAirlineId(ticketAirline.getId());
            if (btmsManager != null) {
                TravelAgencyUser manager = btmsManager.getTravelAgencyUser();
                bookingAir.setManager(manager);
                Department department = manager.getDepartment();
                if (department != null) {
                    if (department.getParentId() != null) {
                        bookingAir.setManagerDepartment(department.getParentId());
                    } else {
                        bookingAir.setManagerDepartment(department.getId());
                    }
                }
            }
            List<BookingAirTraveler> bookingAirTravelers = new ArrayList<>();
            List<List<TravelerMileageInfo>> travelerMileageInfoss = new ArrayList<>();
            double totalReserveAmount = 0.0;
            for (int i = 0; i < pnrCreateResponse.getPassengerInfos().size(); i++) {
                PassengerInfoRes passengerInfo = pnrCreateResponse.getPassengerInfos().get(i);
                CreateTravelOverseasPassenger passenger = body.getPassengers().get(i);
                BookingAirTraveler bookingAirTraveler = new BookingAirTraveler();
                bookingAirTraveler.setTraveler(passenger.getUserID() != 0 ? new User(passenger.getUserID()) : null);
                bookingAirTraveler.setBirthday(passenger.getDateOfBirth().replaceAll("-", ""));
                bookingAirTraveler.setCellPhoneNumber(passenger.getPhoneNumber().replaceAll("-", ""));
                bookingAirTraveler.setEmail(passenger.getEmail());
                bookingAirTraveler.setFirstName(passenger.getEnglishFirstName());
                bookingAirTraveler.setLastName(passenger.getEnglishLastName());
                bookingAirTraveler.setTitle(passengerInfo.getName().getTitle());
                bookingAirTraveler.setTravelerName(passenger.getKoreanName());
                bookingAirTraveler.setTravelerFirstName(passenger.getEnglishFirstName());
                bookingAirTraveler.setTravelerLastName(passenger.getEnglishLastName());
                bookingAirTraveler.setGender(passenger.getGender());
                bookingAirTraveler.setNationalityCode(passenger.getNationality());
                bookingAirTraveler.setSettlementManagerEmail(passenger.getSettlementManagerEmail());
                if (passenger.getPassportDocumentNo() != null && !passenger.getPassportDocumentNo().isEmpty()) {
                    bookingAirTraveler.setPassportNumber(passenger.getPassportDocumentNo());
                    bookingAirTraveler.setPassportNation(passenger.getPassportIssuedByCode());
                    bookingAirTraveler.setPassportLimitDate(passenger.getPassportExpirationDate().replace("-", ""));
                }
                List<TravelerMileageInfo> travelerMileageInfos = new ArrayList<>();
                for (CreateTravelOverseasPassengerMileageMembership passengerMileageMembership : passenger.getMileageMemberships()) {
                    TravelerMileageInfo travelerMileageInfo = new TravelerMileageInfo();
                    travelerMileageInfo.setAirline(passengerMileageMembership.getAirline());
                    travelerMileageInfo.setMileageMemberNo(passengerMileageMembership.getMemberNo());
                    travelerMileageInfos.add(travelerMileageInfo);
                }
                travelerMileageInfoss.add(travelerMileageInfos);
                double totalAirFare = 0;
                double totalTax = 0;
                for (FareInfo fareInfo : passengerInfo.getFareInfos()) {
                    totalAirFare += fareInfo.getAirFare();
                    totalTax += (fareInfo.getAirTax() + fareInfo.getFuelChg());
                }
                double commissionAmount = AirUtil.calculationTasf(company, false, totalAirFare, 0, totalTax, pnrCreateResponse.getJourneys().getFirst().getSegments().getFirst().getCabinClass());
                double reserveAmount = totalAirFare - 0 + totalTax + commissionAmount;
                bookingAirTraveler.setFareAmount(totalAirFare);
                bookingAirTraveler.setTax(totalTax);
                bookingAirTraveler.setDcAmount(0.0); // Luôn bằng 0
                bookingAirTraveler.setFuelSurcharge(0.0); // Luôn bằng 0
                bookingAirTraveler.setCommissionAmount(commissionAmount);
                bookingAirTraveler.setReserveAmount(reserveAmount);
                totalReserveAmount += reserveAmount;
                bookingAirTravelers.add(bookingAirTraveler);
            }
            bookingAir.setReserveAmount(totalReserveAmount);
            this.bookingAirRepo.insert(bookingAir);
            /* #endregion */

            // region Build and save BookingAirSchedule
            this.travelService.saveBookingAirSchedules(true, bookingAir.getId(), journeysRes, passengerInfosRes);

            /* #region Save BookingAirTraveler */
            for (int i = 0; i < bookingAirTravelers.size(); i++) {
                BookingAirTraveler bookingAirTraveler = bookingAirTravelers.get(i);
                bookingAirTraveler.setBookingAirId(bookingAir.getId());
                this.bookingAirTravelerRepo.insert(bookingAirTraveler);
                /* #region Save TravelerMileageInfo */
                List<TravelerMileageInfo> travelerMileageInfos = travelerMileageInfoss.get(i);
                for (TravelerMileageInfo travelerMileageInfo : travelerMileageInfos) {
                    travelerMileageInfo.setBookingAirTravelerId(bookingAirTraveler.getId());
                    this.travelerMileageInfoRepo.insert(travelerMileageInfo);
                }
                /* #endregion */
            }
            /* #endregion */

            /* #region save document numbers */
            if (body.getDocumentNumbers() != null && body.getDocumentNumbers().size() > 0) {
                for (int i = 0; i < body.getDocumentNumbers().size(); i++) {
                    DocumentNumber documentNumber = new DocumentNumber();
                    documentNumber.setBookingId(travel.getId());
                    documentNumber.setBookingType(BookingType.AIR);
                    documentNumber.setDocumentNo(body.getDocumentNumbers().get(i));
                    documentNumber.setOrderNo(Long.valueOf(i + 1));
                    documentNumber.setCreatorUserId(userInfo.getUserId());

                    this.documentNumberRepo.insert(documentNumber);
                }
            }
            /* #endregion */

            /* #region Save attach files */
            if (body.getAttachFiles() != null && !body.getAttachFiles().isEmpty()) {
                for (CreateTravelOverseasAttachFile createTravelOverseasAttachFile : body.getAttachFiles()) {
                    AttachFile attachFile = new AttachFile();
                    attachFile.setAttachFileType(createTravelOverseasAttachFile.getAttachFileType());
                    attachFile.setFileSize(createTravelOverseasAttachFile.getFileSize());
                    attachFile.setFileUploadPath(createTravelOverseasAttachFile.getFileUploadPath());
                    attachFile.setOriginFileName(createTravelOverseasAttachFile.getOriginFileName());
                    attachFile.setTempFileName(createTravelOverseasAttachFile.getTempFileName());
                    this.attachFileRepo.insert(attachFile);
                    this.bookingAirAttachFileRepo.insert(new BookingAirAttachFile(bookingAir.getId(), attachFile));
                }
            }
            /* #endregion */

            return bookingAir;
        } catch (Exception e) {
            List<PassengerInfoReq> passengerInfos = new ArrayList<>();
            for (int i = 0; i < body.getPassengers().size(); i++) {
                CreateTravelOverseasPassenger passenger = body.getPassengers().get(i);
                PassengerInfoReq passengerInfo = PassengerInfoReq.builder()
                        .passengerIdx(i + 1)
                        .paxType("ADT")
                        .dateOfBirth(passenger.getDateOfBirth())
                        .name(Name.builder().firstName(passenger.getEnglishFirstName().toUpperCase())
                                .lastName(passenger.getEnglishLastName().toUpperCase()).middleName("").build())
                        .build();
                passengerInfos.add(passengerInfo);
            }
            List<CancelInfo> cancelInfos = new ArrayList<>();
            cancelInfos.add(CancelInfo.builder()
                    .orderId(pnrCreateResponse.getJourneys().getFirst().getOrderId())
                    .actionCode("PC")
                    .cancelReason("Cancel")
                    .passengerInfos(passengerInfos)
                    .build());
            CancelBody cancelBody = CancelBody.builder()
                    .ssCode("BTMS")
                    .orderKey(pnrCreateResponse.getJourneys().getFirst().getOrderId())
                    .cancelInfos(cancelInfos)
                    .build();
            this.stellaService.cancelPRN(cancelBody);
            throw e;
        }
    }

    private void saveFareRule(Long travelId, CreateTravelOverseasBody body) {
        GetFareRuleBody getFareRuleBody = new GetFareRuleBody();
        getFareRuleBody.setSsCode("BTMS");
        getFareRuleBody.setJourneys(new ArrayList<JourneyReq>());
        for (CreateTravelOverseasJourney journey : body.getJourneys()) {
            getFareRuleBody.getJourneys().add(JourneyReq.builder()
                    .journeyKey(journey.getJourneyKey())
                    .fareKey(journey.getFareKey())
                    .pairKey(journey.getPairKey())
                    .airline(journey.getAirline())
                    .deptAirport(journey.getDeptAirport())
                    .arrAirport(journey.getArrAirport())
                    .build());
        }
        GetFareRuleResponse getFareRuleResponse = this.stellaService.getFareRule(getFareRuleBody);
        if (getFareRuleResponse.getJourneys().getFirst().getFareBasisRules().isEmpty()) {
            TravelFareRuleRecord travelFareRuleRecord = new TravelFareRuleRecord();
            travelFareRuleRecord.setTravelId(travelId);

            ArrayNode fareRuleItemsJson = this.objectMapper.createArrayNode();
            for (AgencyItem agencyItem : getFareRuleResponse.getJourneys().getFirst().getAgencyItems()) {
                if (!"여행사 수수료 안내".equals(agencyItem.getName())) {
                    ObjectNode fareRuleItemJson = this.objectMapper.createObjectNode();
                    fareRuleItemJson.put("title", agencyItem.getName());
                    fareRuleItemJson.put("contents", agencyItem.getDescription());
                    fareRuleItemJson.set("categoryCode", null);
                    fareRuleItemsJson.add(fareRuleItemJson);
                }
            }
            ObjectNode fareRuleJson = this.objectMapper.createObjectNode();
            fareRuleJson.put("fareComponentNo", "1");
            fareRuleJson.set("fareRuleItems", fareRuleItemsJson);
            travelFareRuleRecord.setFareRuleStr(fareRuleJson.toString());
            this.travelFareRuleRecordRepo.insert(travelFareRuleRecord);
        } else {
            for (int i = 0; i < getFareRuleResponse.getJourneys().getFirst().getFareBasisRules().size(); i++) {
                FareBasisRule fareBasisRule = getFareRuleResponse.getJourneys().getFirst().getFareBasisRules().get(i);
                TravelFareRuleRecord travelFareRuleRecord = new TravelFareRuleRecord();
                travelFareRuleRecord.setTravelId(travelId);

                ArrayNode fareRuleItemsJson = this.objectMapper.createArrayNode();
                for (FareBasisRuleItem fareBasisRuleItem : fareBasisRule.getItems()) {

                    ObjectNode fareRuleItemJson1 = this.objectMapper.createObjectNode();
                    fareRuleItemJson1.put("title", fareBasisRuleItem.getName());
                    fareRuleItemJson1.put("contents", fareBasisRuleItem.getDescription());
                    fareRuleItemJson1.set("categoryCode", null);
                    fareRuleItemsJson.add(fareRuleItemJson1);
                    for (AgencyItem agencyItem : getFareRuleResponse.getJourneys().getFirst().getAgencyItems()) {
                        if (!"여행사 수수료 안내".equals(agencyItem.getName())) {
                            ObjectNode fareRuleItemJson2 = this.objectMapper.createObjectNode();
                            fareRuleItemJson2.put("title", agencyItem.getName());
                            fareRuleItemJson2.put("contents", agencyItem.getDescription());
                            fareRuleItemJson2.set("categoryCode", null);
                            fareRuleItemsJson.add(fareRuleItemJson2);
                        }
                    }

                }
                ObjectNode fareRuleJson = this.objectMapper.createObjectNode();
                fareRuleJson.put("fareComponentNo", i + 1 + "");
                fareRuleJson.set("fareRuleItems", fareRuleItemsJson);
                travelFareRuleRecord.setFareRuleStr(fareRuleJson.toString());
                this.travelFareRuleRecordRepo.insert(travelFareRuleRecord);
            }
        }
    }

    private void updateTicketDateAndViewTicketDateAndPnrData(BookingAir bookingAir) {
        PNRInfoRes pnrInfo = this.stellaService.GetPNRInfo(bookingAir.getPnrNo(), bookingAir.getGdsType(), bookingAir.getOrderID());
        HashMap<String, Object> updateValues = new HashMap<>();
        Date ticketDate = null;
        if (!StringUtils.isNullOrEmpty(pnrInfo.getRetrieveData().getJourneys().getFirst().getAirlineLimitDate()) && !StringUtils.isNullOrEmpty(pnrInfo.getRetrieveData().getJourneys().getFirst().getAirlineLimitTime())) {
            ticketDate = DateUtil.string2Date(pnrInfo.getRetrieveData().getJourneys().getFirst().getAirlineLimitDate() + " " + pnrInfo.getRetrieveData().getJourneys().getFirst().getAirlineLimitTime(), "yyyy-MM-dd HHmm");
        }
        Date viewTicketDate = null;
        if (!StringUtils.isNullOrEmpty(pnrInfo.getRetrieveData().getJourneys().getFirst().getFareLimitDate())) {
            viewTicketDate = DateUtil.string2Date(pnrInfo.getRetrieveData().getJourneys().getFirst().getFareLimitDate() + " 1600", "yyyy-MM-dd HHmm");
        }
        if (bookingAir.getGdsType().equals(GDSType.AMADEUS)) {
            if (ticketDate == null) {
                ticketDate = DateUtil.getAmadeusDate(pnrInfo.getRawData(), OPType.OPC, bookingAir.getBookingDate());
            }
            if (viewTicketDate == null) {
                viewTicketDate = DateUtil.getAmadeusDate(pnrInfo.getRawData(), OPType.OPW, bookingAir.getBookingDate());
            }
        } else {
            if (ticketDate != null) {
                viewTicketDate = DateUtil.string2Date(pnrInfo.getRetrieveData().getJourneys().getFirst().getAirlineLimitDate() + " 1600", "yyyy-MM-dd HHmm");
                viewTicketDate = new Date(viewTicketDate.getTime() - 24 * 60 * 60 * 1000);
            } else {
                viewTicketDate = null;
            }
        }
        updateValues.put(BookingAir_.TICKET_DATE, ticketDate);
        updateValues.put(BookingAir_.VIEW_TICKET_DATE, viewTicketDate);
        updateValues.put(BookingAir_.PNR_DATA, pnrInfo.getRawData());
        this.bookingAirRepo.update(bookingAir.getId(), updateValues);

        /* #region insert pnrDataHistory */
        PnrDataHistory pnrDataHistory = new PnrDataHistory();
        pnrDataHistory.setBookingAirId(bookingAir.getId());
        pnrDataHistory.setPnrData(pnrInfo.getRawData());
        pnrDataHistory.setPnrDataHistoryType(PnrDataHistoryType.RealTime);
        pnrDataHistory.setManager(bookingAir.getManager());
        pnrDataHistory.setCreateDate(new Date());
        this.pnrDataHistoryRepo.insert(pnrDataHistory);
        /* #endregion */
    }

    private void sendEmailAndKakaoTalk(Long companyId, Long travelId) {
        try {
            this.requestContextController.activate();

            Company company = this.companyRepo.findFetchParentById(companyId);
            if (company == null || company.getAirEmSetting() == null) {
                return;
            }
            Travel travel = this.travelRepo.findFetchBookingAirAndBookingAirScheduleAndBookingAirTravelerById(travelId);
            Traveler reserver = this.travelerRepo.findByTravelIdAndIsReserverTrue(travel.getId());

            this.emailService.sendBookingAirEmail(company, travel, reserver, false);
            this.kakaoTalkService.sendOverseasBookingComplete(company, travel, reserver);
        } finally {
            this.requestContextController.deactivate();
        }
    }
}