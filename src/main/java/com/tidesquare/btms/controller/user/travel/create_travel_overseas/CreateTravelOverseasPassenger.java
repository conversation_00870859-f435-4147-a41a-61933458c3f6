package com.tidesquare.btms.controller.user.travel.create_travel_overseas;

import java.util.List;

import com.tidesquare.btms.constant.Gender;
import com.tidesquare.btms.validator.ValidDateString;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateTravelOverseasPassenger {
    @Min(value = 0)
    private long userID;

    @ValidDateString(format = "yyyy-MM-dd")
    @NotNull
    private String dateOfBirth;

    @NotNull
    private Gender gender;

    @NotNull
    private String nationality;

    @NotNull
    private String koreanName;

    @NotNull
    private String englishFirstName;

    @NotNull
    private String englishLastName;

    @NotNull
    private String phoneNumber;

    @NotNull
    private String email;

    private String settlementManagerEmail;

    private String passportDocumentNo;

    private String passportExpirationDate;

    private String passportIssuedByCode;

    @Size(min = 0)
    @NotNull
    @Valid
    private List<CreateTravelOverseasPassengerMileageMembership> mileageMemberships;
}
