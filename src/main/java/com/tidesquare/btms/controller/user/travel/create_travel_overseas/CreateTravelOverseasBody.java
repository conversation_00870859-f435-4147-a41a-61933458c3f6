package com.tidesquare.btms.controller.user.travel.create_travel_overseas;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import java.util.List;

@Getter
@Setter
public class CreateTravelOverseasBody {
    @Size(min = 1)
    @NotNull
    @Valid
    private List<CreateTravelOverseasJourney> journeys;

    @Size(min = 1)
    @NotNull
    @Valid
    private List<CreateTravelOverseasPassenger> passengers;

    @Valid
    private List<CreateTravelOverseasAttachFile> attachFiles;

    private String violationReason;

    private List<String> documentNumbers;
}
