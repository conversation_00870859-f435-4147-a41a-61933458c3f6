package com.tidesquare.btms.controller.user.travel.get_ticket_overseas;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.dto.response.BookingAirTicketRes;
import com.tidesquare.btms.entity.BookingAir;
import com.tidesquare.btms.entity.BookingAirTicket;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.BookingAirRepo;
import com.tidesquare.btms.repository.BookingAirTicketRepo;
import com.tidesquare.btms.service.travel.TravelService;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response.Status;

import java.util.List;

@ApplicationScoped
public class GetBookingAirTicketHandler {
    @Inject
    private TravelService travelService;

    @Inject
    private BookingAirTicketRepo bookingAirTicketRepo;

    @Inject
    private BookingAirRepo bookingAirRepo;

    public List<BookingAirTicketRes> run(UserInfo userInfo, Long travelId) {
        boolean isPermission = travelService.checkUserIsReverserOrTravelerOfTravel(userInfo.getUserId(), travelId);
        if (!isPermission) {
            throw new ApiException(Status.FORBIDDEN, ErrorCode.E403_TRAVEL_FORBIDDEN);
        }

        BookingAir bookingAir = bookingAirRepo.findByTravelId(travelId);

        List<BookingAirTicket> bookingAirTicketList = bookingAirTicketRepo.findAllByBookingAirId(bookingAir.getId());
        return BookingAirTicketRes.fromEntities(bookingAirTicketList);
    }
}
