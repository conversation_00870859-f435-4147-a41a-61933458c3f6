package com.tidesquare.btms.controller.agency.company.reward_mile_setting;

import com.tidesquare.btms.entity.RewardMileSetting;
import com.tidesquare.btms.repository.RewardMileSettingRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class RewardMileSettingHandler {

  @Inject
  private RewardMileSettingRepo rewardMileSettingRepo;

  public RewardMileSettingRes getRewardMileSettingCompany(Long companyId) {
    RewardMileSetting rewardMileSetting = rewardMileSettingRepo.get(companyId);
    RewardMileSettingRes rewardMileSettingRes = RewardMileSettingRes.builder()
        .comanyId(rewardMileSetting.getCompanyId())
        .ratio(rewardMileSetting.getRatio())
        .rewardMileType(rewardMileSetting.getRewardMileType())
        .maximumAccumulatedMiles(rewardMileSetting.getMaximumAccumulatedMiles())
        .isUse(rewardMileSetting.getIsUse())
        .build();
    return rewardMileSettingRes;
  }

  @Transactional(rollbackOn = Exception.class)
  public void createRewardMileSettingCompany(RewardMileSettingReq body) {

    RewardMileSetting rewardMileSetting = RewardMileSetting.builder()
        .companyId(body.getCompanyId())
        .rewardMileType(body.getRewardMileType())
        .ratio(body.getRatio())
        .maximumAccumulatedMiles(body.getMaximumAccumulatedMiles())
        .isUse(body.getIsUse())
        .build();
    this.rewardMileSettingRepo.insertOrUpdate(rewardMileSetting);
  }
}
