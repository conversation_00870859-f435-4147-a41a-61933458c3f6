package com.tidesquare.btms.controller.agency.contact_request.get;

import com.tidesquare.btms.dto.response.ContactRequestRes;
import com.tidesquare.btms.entity.ContactRequest;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.ContactRequestRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response.Status;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class GetContactRequestHandler {

    @Inject
    private ContactRequestRepo contactRequestRepo;

    public ContactRequestRes run(Long id) {
        ContactRequest contactRequest = this.contactRequestRepo.findById(id);
        if (contactRequest == null) {
            throw new ApiException(Status.NOT_FOUND, ErrorCode.E404);
        }
        return ContactRequestRes.fromEntity(contactRequest);
    }

}
