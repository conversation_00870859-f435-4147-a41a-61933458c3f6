package com.tidesquare.btms.controller.agency.contact_request.update;

import java.util.HashMap;
import java.util.Map;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.constant.ContactRequestStatus;
import com.tidesquare.btms.entity.ContactRequest_;
import com.tidesquare.btms.entity.TravelAgencyUser;
import com.tidesquare.btms.repository.ContactRequestRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class UpdateContactRequestHandler {

    @Inject
    private ContactRequestRepo contactRequestRepo;

    public void run(UserInfo userInfo, Long id, UpdateContactRequestBody body) {
        Map<String, Object> updateValues = new HashMap<String, Object>();
        updateValues.put(ContactRequest_.NOTE, body.getNote());
        updateValues.put(ContactRequest_.STATUS, body.getStatus());
        if (body.getStatus().equals(ContactRequestStatus.ANSWERED)) {
            updateValues.put(ContactRequest_.ANSWERED_AT, new java.util.Date());
            updateValues.put(ContactRequest_.RESPONDENT, new TravelAgencyUser(userInfo.getUserId()));
        } else if (body.getStatus().equals(ContactRequestStatus.NEW)) {
            updateValues.put(ContactRequest_.ANSWERED_AT, null);
            updateValues.put(ContactRequest_.RESPONDENT, null);
        }

        this.contactRequestRepo.update(id, updateValues);
    }
}
