package com.tidesquare.btms.controller.agency.company.update;

import java.util.HashMap;
import java.util.Map;

import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.Company_;
import com.tidesquare.btms.entity.embeddable.BtmsSetting;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.CompanyRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response.Status;

@ApplicationScoped
public class UpdateHandler {
  @Inject
  private CompanyRepo companyRepo;

  public void run(Long companyId, UpdateGroupBody body) {
    Company company = this.companyRepo.findById(companyId);
    if (company == null) {
      throw new ApiException(Status.NOT_FOUND, ErrorCode.E404);
    }

    BtmsSetting btmsSetting = company.getBtmsSetting();
    btmsSetting.setUse(body.getIsUse());

    Map<String, Object> updateValues = new HashMap<>();
    updateValues.put(Company_.BTMS_SETTING, btmsSetting);
    this.companyRepo.update(companyId, updateValues);
  }
}
