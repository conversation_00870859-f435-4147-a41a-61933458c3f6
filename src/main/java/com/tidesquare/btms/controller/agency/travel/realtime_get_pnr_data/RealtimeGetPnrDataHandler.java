package com.tidesquare.btms.controller.agency.travel.realtime_get_pnr_data;

import java.util.HashMap;
import java.util.Map;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.entity.BookingAir;
import com.tidesquare.btms.entity.BookingAir_;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.BookingAirRepo;
import com.tidesquare.btms.service.stella.StellaService;
import com.tidesquare.btms.service.stella.dto.response.PNRInfoRes;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response.Status;

@ApplicationScoped
public class RealtimeGetPnrDataHandler {

    @Inject
    private BookingAirRepo bookingAirRepo;

    @Inject
    private StellaService stellaService;

    @Transactional(rollbackOn = Exception.class)
    public RealtimeGetPnrDataRes run(UserInfo userInfo, Long travelId) {
        BookingAir bookingAir = this.bookingAirRepo.findByTravelId(travelId);
        if (bookingAir == null) {
            throw new ApiException(Status.BAD_REQUEST, ErrorCode.E404_TRAVEL_NOT_FOUND);
        }

        PNRInfoRes pnrInfo = this.stellaService.GetPNRInfo(bookingAir.getPnrNo(), bookingAir.getGdsType(), bookingAir.getOrderID());

        Map<String, Object> updateValues = new HashMap<String, Object>();
        updateValues.put(BookingAir_.PNR_DATA, pnrInfo.getRawData());
        if (pnrInfo.getOrderId() != null && !pnrInfo.getOrderId().isBlank()) {
            updateValues.put(BookingAir_.ORDER_ID, pnrInfo.getOrderId());
        }

        this.bookingAirRepo.update(bookingAir.getId(), updateValues);

        return RealtimeGetPnrDataRes.builder().pnrData(pnrInfo.getRawData()).build();
    }
}
