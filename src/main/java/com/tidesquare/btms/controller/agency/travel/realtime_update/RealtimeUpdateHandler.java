package com.tidesquare.btms.controller.agency.travel.realtime_update;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.constant.GDSType;
import com.tidesquare.btms.constant.PnrDataHistoryType;
import com.tidesquare.btms.entity.BookingAir;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.BookingAirRepo;
import com.tidesquare.btms.service.stella.StellaService;
import com.tidesquare.btms.service.stella.dto.response.PNRInfoRes;
import com.tidesquare.btms.service.travel.TravelService;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response.Status;

@ApplicationScoped
public class RealtimeUpdateHandler {

    @Inject
    private TravelService travelService;

    @Inject
    private StellaService stellaService;

    @Inject
    private BookingAirRepo bookingAirRepo;

    @Transactional(rollbackOn = Exception.class)
    public void run(UserInfo userInfo, RealtimeUpdateBody body) {
        String pnrNumber = body.getPnrNumber();
        GDSType gdsType = body.getGdsType();
        String orderId = null;
        BookingAir bookingAir = null;

        if (body.getBookingAirId() != null && body.getBookingAirId() != 0) {
            bookingAir = this.bookingAirRepo.findAndFetchTravelById(body.getBookingAirId());
            if (bookingAir == null) {
                throw new ApiException(Status.NOT_FOUND, ErrorCode.E404);
            }
            pnrNumber = bookingAir.getPnrNo();
            gdsType = bookingAir.getGdsType();
            orderId = bookingAir.getOrderID();
        }

        PNRInfoRes pnrInfo = this.stellaService.GetPNRInfo(pnrNumber, gdsType, orderId);
        if (pnrInfo == null) {
            throw new ApiException(Status.NOT_FOUND, ErrorCode.E404_PNR_NOT_FOUND);
        }

        if (orderId != null) {
            this.travelService.savePnrInfo(userInfo, pnrInfo, PnrDataHistoryType.RealTime, bookingAir);
        } else {
            this.travelService.savePnrInfo(userInfo, pnrInfo, PnrDataHistoryType.RealTime);
        }
    }
}
