package com.tidesquare.btms.controller.agency.travel.remove_reserver;

import com.tidesquare.btms.repository.TravelerRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class AgencyTravelRemoveReserverHandler {

    @Inject
    private TravelerRepo travelerRepo;

    @Transactional(rollbackOn = Exception.class)
    public void run(Long travelId) {
        this.travelerRepo.removeReserver(travelId);
    }
}
