package com.tidesquare.btms.controller.agency.company.get_by_url;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.dto.response.CompanyRes;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.Company_;
import com.tidesquare.btms.entity.embeddable.BtmsSetting_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;

@ApplicationScoped
public class GetByUrlHandler {

    @Inject
    private StatelessSession statelessSession;

    public CompanyRes run(String url) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Company> q = builder.createQuery(Company.class);
        Root<Company> root = q.from(Company.class);
        q.select(root);

        q.where(builder.equal(root.get(Company_.btmsSetting).get(BtmsSetting_.url), url));

        Company company = this.statelessSession.createSelectionQuery(q).setMaxResults(1).getSingleResultOrNull();
        return CompanyRes.fromEntity(company);
    }
}
