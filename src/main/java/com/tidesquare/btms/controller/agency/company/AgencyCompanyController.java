package com.tidesquare.btms.controller.agency.company;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import com.tidesquare.btms.common.ApiQueryPagination;
import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.constant.CompanyType;
import com.tidesquare.btms.constant.RewardMileHistoryType;
import com.tidesquare.btms.controller.agency.company.auto_complete.AutoCompleteHandler;
import com.tidesquare.btms.controller.agency.company.create.CreateCompanyBody;
import com.tidesquare.btms.controller.agency.company.create.CreateCompanyRes;
import com.tidesquare.btms.controller.agency.company.create.CreateHandler;
import com.tidesquare.btms.controller.agency.company.get.GetCompanyQuery;
import com.tidesquare.btms.controller.agency.company.get.GetHandler;
import com.tidesquare.btms.controller.agency.company.get_by_url.GetByUrlHandler;
import com.tidesquare.btms.controller.agency.company.get_by_admin_url.GetByAdminUrlHandler;
import com.tidesquare.btms.controller.agency.company.get_next_group_code.GetNextGroupCodeHandler;
import com.tidesquare.btms.controller.agency.company.get_next_site_code.GetNextSiteCodeHandler;
import com.tidesquare.btms.controller.agency.company.list.GetListCompanyQuery;
import com.tidesquare.btms.controller.agency.company.list.ListHandler;
import com.tidesquare.btms.controller.agency.company.reward_mile_history.RewardMileHistoryHandler;
import com.tidesquare.btms.controller.agency.company.reward_mile_history.RewardMileHistoryReq;
import com.tidesquare.btms.controller.agency.company.reward_mile_history.RewardMileHistoryRes;
import com.tidesquare.btms.controller.agency.company.reward_mile_setting.RewardMileSettingHandler;
import com.tidesquare.btms.controller.agency.company.reward_mile_setting.RewardMileSettingReq;
import com.tidesquare.btms.controller.agency.company.reward_mile_setting.RewardMileSettingRes;
import com.tidesquare.btms.controller.agency.company.update.UpdateGroupBody;
import com.tidesquare.btms.controller.agency.company.update.UpdateHandler;
import com.tidesquare.btms.controller.agency.company.update_children.UpdateChildrenHandler;
import com.tidesquare.btms.controller.agency.company.update_children.UpdateCompanyChildrenBody;
import com.tidesquare.btms.dto.response.CompanyRes;
import com.tidesquare.btms.filter.AgencyBinding;

import io.vertx.mutiny.core.Vertx;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.BeanParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.StreamingOutput;

@Path("agency/company")
@AgencyBinding
@Valid
@SecurityRequirement(name = "bearer")
public class AgencyCompanyController {
  @Inject
  private RewardMileSettingHandler rewardMileSettingHandler;

  @Inject
  private RewardMileHistoryHandler rewardMileHistoryHandler;

  @Inject
  private GetHandler getHandler;

  @Inject
  private GetByUrlHandler getByUrlHandler;

  @Inject
  private GetByAdminUrlHandler getByAdminUrlHandler;

  @Inject
  private CreateHandler createHandler;

  @Inject
  private GetNextGroupCodeHandler getNextGroupCodeHandler;

  @Inject
  private GetNextSiteCodeHandler getNextSiteCodeHandler;

  @Inject
  private ListHandler listHandler;

  @Inject
  private AutoCompleteHandler autoCompleteHandler;

  @Inject
  private UpdateChildrenHandler updateChildrenHandler;

  @Inject
  private UpdateHandler updateHandler;

  @POST
  @Path("")
  @Operation(summary = "Create a company")
  public ApiResponse<CreateCompanyRes> create(@Valid CreateCompanyBody body) {
    UserInfo userInfo = Vertx.currentContext().getLocal("user");
    CreateCompanyRes createCompanyRes = this.createHandler.run(userInfo, body);
    return ApiResponse.fromData(createCompanyRes);
  }

  @GET
  @Path("")
  @Operation(summary = "get list company")
  public ApiResponse<Map<String, Object>> list(@BeanParam GetListCompanyQuery query, @BeanParam ApiQueryPagination pagination) {
    Map<String, Object> result = this.listHandler.run(query, pagination);
    return ApiResponse.fromData(result);
  }

  @GET
  @Path("/auto-complete")
  @Operation(summary = "auto complete company by name")
  public ApiResponse<List<CompanyRes>> autoComplete(@QueryParam("isGroup") boolean isGroup, @QueryParam("name") String name) {
    List<CompanyRes> result = this.autoCompleteHandler.run(isGroup, name);
    return ApiResponse.fromData(result);
  }

  @GET
  @Path("/next-group-code")
  @Operation(summary = "get next group code")
  public ApiResponse<String> getNextGroupCode() {
    String result = this.getNextGroupCodeHandler.run();
    return ApiResponse.fromData(result);
  }

  @GET
  @Path("/next-site-code")
  @Operation(summary = "get next site code")
  public ApiResponse<String> getNextSiteCode(@QueryParam("companyType") CompanyType companyType) {
    String result = this.getNextSiteCodeHandler.run(companyType);
    return ApiResponse.fromData(result);
  }

  @GET
  @Path("/get-by-url")
  @Operation(summary = "Get company by url")
  public ApiResponse<CompanyRes> getCompanyByUrl(@QueryParam("url") String url) {
    CompanyRes companyRes = this.getByUrlHandler.run(url);
    return ApiResponse.fromData(companyRes);
  }

  @GET
  @Path("/get-by-admin-url")
  @Operation(summary = "Get company by admin url")
  public ApiResponse<CompanyRes> getCompanyByAdminUrl(@QueryParam("url") String url) {
    CompanyRes companyRes = this.getByAdminUrlHandler.run(url);
    return ApiResponse.fromData(companyRes);
  }

  @GET
  @Path("{companyId}")
  @Operation(summary = "Get company info")
  public ApiResponse<CompanyRes> getCompany(@PathParam("companyId") Long companyId, @BeanParam GetCompanyQuery query) {
    CompanyRes companyRes = this.getHandler.run(companyId, query);
    return ApiResponse.fromData(companyRes);
  }

  @PUT
  @Path("/{companyId}/children")
  @Operation(summary = "Save list children company of group")
  public ApiResponse<Void> updateChildren(@PathParam("companyId") Long companyId, @Valid UpdateCompanyChildrenBody body) {
    this.updateChildrenHandler.run(companyId, body);
    return ApiResponse.fromData(null);
  }

  @PUT
  @Path("/{companyId}")
  @Operation(summary = "Update company")
  public ApiResponse<Void> update(@PathParam("companyId") Long companyId, @Valid UpdateGroupBody body) {
    this.updateHandler.run(companyId, body);
    return ApiResponse.fromData(null);
  }

  @GET
  @Path("/{companyId}/reward-mile/setting")
  @Operation(summary = "Get setting reward mile for company")
  public ApiResponse<RewardMileSettingRes> getRewardMileSettingCompany(@Valid @PathParam("companyId") Long companyId) {
    RewardMileSettingRes rewardMileSettingRes = this.rewardMileSettingHandler.getRewardMileSettingCompany(companyId);
    return ApiResponse.fromData(rewardMileSettingRes);
  }

  @POST
  @Path("/reward-mile/setting")
  @Operation(summary = "Add company to reward mile setting")
  public ApiResponse<String> createRewardMileSettingCompany(@Valid RewardMileSettingReq body) {
    this.rewardMileSettingHandler.createRewardMileSettingCompany(body);
    return ApiResponse.fromData("Update successfully!");
  }

  @GET
  @Path("/{companyId}/reward-mile/history")
  @Operation(summary = "Get history")
  public ApiResponse<Map<String, Object>> getRewardMileHistory(@Valid @PathParam("companyId") Long companyId, @QueryParam("type") RewardMileHistoryType type) {
    Map<String, Object> response = new HashMap<>();
    List<RewardMileHistoryRes> rewardMileHistoryRes = this.rewardMileHistoryHandler.getListHistory(companyId, type);
    int totalRewardMile = this.rewardMileHistoryHandler.getTotalMiles(companyId);

    response.put("rewardMileHistories", rewardMileHistoryRes);
    response.put("totalRewardMile", totalRewardMile);
    return ApiResponse.fromData(response);
  }

  @POST
  @Path("/reward-mile/history")
  @Operation(summary = "Add reward mile history")
  public ApiResponse<Void> addRewardMileHistory(@Valid RewardMileHistoryReq rewardMileHistoryReq) {
    this.rewardMileHistoryHandler.addRewardMileHistory(rewardMileHistoryReq);
    return ApiResponse.fromData(null);
  }

  @GET
  @Path("/{companyId}/reward-mile/export-history")
  @Operation(summary = "Export reward mile history to Excel")
  @Produces("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
  public Response exportRewardMileHistory(@Valid @PathParam("companyId") Long companyId, @Valid @QueryParam("type") RewardMileHistoryType type) {
    StreamingOutput output = outputStream -> {
      this.rewardMileHistoryHandler.exportToExcel(companyId, outputStream, type);
    };
    return Response.ok(output)
        .header("Content-Disposition", "attachment; filename=\"reward_mile_history.xlsx\"")
        .build();
  }
}
