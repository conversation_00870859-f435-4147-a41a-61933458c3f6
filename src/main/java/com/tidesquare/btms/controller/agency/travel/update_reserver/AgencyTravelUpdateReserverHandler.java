package com.tidesquare.btms.controller.agency.travel.update_reserver;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.entity.Customer;
import com.tidesquare.btms.entity.Travel;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.CustomerRepo;
import com.tidesquare.btms.repository.TravelRepo;
import com.tidesquare.btms.repository.TravelerRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response.Status;

@ApplicationScoped
public class AgencyTravelUpdateReserverHandler {

    @Inject
    private TravelerRepo travelerRepo;

    @Inject
    private TravelRepo travelRepo;

    @Inject
    private CustomerRepo customerRepo;

    @Transactional(rollbackOn = Exception.class)
    public void run(UserInfo userInfo, Long travelId, AgencyTravelUpdateReserverBody body) {
        Long userId = body.getUserId();

        Customer customer = this.customerRepo.findOneFetchWorkspace(userId);

        Travel travel = this.travelRepo.findSingleTravel(travelId);

        if (customer == null || travel == null || !customer.getWorkspace().getCompany().getId().equals(travel.getCompany().getId())) {
            throw new ApiException(Status.BAD_REQUEST, ErrorCode.E400);
        }

        this.travelerRepo.updateReserver(travelId, customer.getId(), customer.getName(),
                customer.getEmail(), customer.getAccountingCode(), customer.getCellPhoneNumber());
    }
}
