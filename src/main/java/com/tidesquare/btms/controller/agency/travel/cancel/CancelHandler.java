package com.tidesquare.btms.controller.agency.travel.cancel;

import java.util.HashMap;
import java.util.Map;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.constant.TravelStatus;
import com.tidesquare.btms.entity.BookingAir;
import com.tidesquare.btms.entity.BookingAir_;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.Travel;
import com.tidesquare.btms.entity.Travel_;
import com.tidesquare.btms.entity.Traveler;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.BookingAirRepo;
import com.tidesquare.btms.repository.CompanyRepo;
import com.tidesquare.btms.repository.TravelRepo;
import com.tidesquare.btms.repository.TravelerRepo;
import com.tidesquare.btms.service.email.EmailService;
import com.tidesquare.btms.service.kakao_talk.KakaoTalkService;
import com.tidesquare.btms.service.stella.StellaService;
import com.tidesquare.btms.service.stella.dto.response.PNRInfoRes;
import com.tidesquare.btms.service.travel.TravelService;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.context.control.RequestContextController;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response.Status;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class CancelHandler {

    @Inject
    private TravelRepo travelRepo;

    @Inject
    private BookingAirRepo bookingAirRepo;

    @Inject
    private StellaService stellaService;

    @Inject
    private TravelService travelService;

    @Inject
    private EmailService emailService;

    @Inject
    private KakaoTalkService kakaoTalkService;

    @Inject
    private RequestContextController requestContextController;

    @Inject
    private CompanyRepo companyRepo;

    @Inject
    private TravelerRepo travelerRepo;

    @Transactional(rollbackOn = Exception.class)
    public void run(UserInfo userInfo, Long travelId) {

        Travel travel = travelRepo.findFetchBookingAirAndBookingAirTicketById(travelId);

        if (travel == null) {
            throw new ApiException(Status.BAD_REQUEST, ErrorCode.E404_TRAVEL_NOT_FOUND);
        }
        if (TravelStatus.Completed.equals(travel.getStatus()) || TravelStatus.Cancelled.equals(travel.getStatus())) {
            throw new ApiException(Status.CONFLICT, ErrorCode.E409_STATUS_CANNOT_BE_CANCELLED);
        }

        BookingAir bookingAir = travel.getBookingAir();

        if (bookingAir.getBookingAirTickets() != null && !bookingAir.getBookingAirTickets().isEmpty()) {
            throw new ApiException(Status.CONFLICT, ErrorCode.E409_TICKET_RESERVATION_CANNOT_CANCEL);
        }

        PNRInfoRes pnrInfoRes = this.stellaService.GetPNRInfo(bookingAir.getPnrNo(), bookingAir.getGdsType(), bookingAir.getOrderID());
        Map<String, Object> updateValues = new HashMap<String, Object>();
        updateValues.put(BookingAir_.PNR_DATA, pnrInfoRes.getRawData());
        if (pnrInfoRes.getOrderId() != null && !pnrInfoRes.getOrderId().isBlank()) {
            updateValues.put(BookingAir_.ORDER_ID, pnrInfoRes.getOrderId());
        }
        bookingAirRepo.update(bookingAir.getId(), updateValues);

        String originValue = travel.getStatus().getText();
        travel.setStatus(TravelStatus.Cancelled);

        travelRepo.update(travel.getId(), new HashMap<String, Object>() {
            {
                put(Travel_.STATUS, travel.getStatus());
            }
        });

        travelService.insertTravelStatusHistory(userInfo, travel);

        String changeValue = TravelStatus.Cancelled.getText();
        travelService.saveBookingHistory(travelId, bookingAir.getId(), userInfo.getUserId(), "StatusCode", "출장상태", originValue, changeValue);

        Thread.startVirtualThread(() -> {
            this.sendEmailAndKakaoTalk(travelId);
        });
    }

    private void sendEmailAndKakaoTalk(Long travelId) {
        try {
            this.requestContextController.activate();

            Travel travel = this.travelRepo.findFetchBookingAirAndBookingAirScheduleAndBookingAirTravelerById(travelId);
            if (travel.getCompany() == null) {
                return;
            }
            Company company = this.companyRepo.findFetchParentById(travel.getCompany().getId());

            if (company == null) {
                return;
            }
            Traveler reserver = this.travelerRepo.findByTravelIdAndIsReserverTrue(travel.getId());

            this.emailService.sendAirCancelEmail(company, travel, reserver);
            this.kakaoTalkService.sendAirCancel(company, travel, reserver);
        } finally {
            this.requestContextController.deactivate();
        }
    }
}
