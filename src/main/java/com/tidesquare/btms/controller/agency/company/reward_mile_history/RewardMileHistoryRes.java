package com.tidesquare.btms.controller.agency.company.reward_mile_history;

import java.util.Date;

import com.tidesquare.btms.entity.RewardMileHistory;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@RegisterForReflection
public class RewardMileHistoryRes {
  private Date usedDate;
  private Integer totalMiles;
  private Integer usedMiles;
  private String reason;
  private Long userId;
  private String userName;

  public static RewardMileHistoryRes convert(RewardMileHistory rewardMileHistory) {
    return RewardMileHistoryRes.builder()
        .usedDate(rewardMileHistory.getUsedDate())
        .totalMiles(rewardMileHistory.getTotalMiles())
        .usedMiles(rewardMileHistory.getUsedMiles())
        .reason(rewardMileHistory.getReason())
        .userId(rewardMileHistory.getUser() != null? rewardMileHistory.getUser().getId() : null)
        .userName(rewardMileHistory.getUser() != null ? rewardMileHistory.getUser().getName() : null)
        .build();
  }
}
