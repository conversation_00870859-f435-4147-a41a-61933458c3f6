package com.tidesquare.btms.controller.agency.company.list;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.common.ApiQueryPagination;
import com.tidesquare.btms.dto.response.CompanyRes;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.Company_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class ListHandler {

    @Inject
    private StatelessSession statelessSession;

    public Map<String, Object> run(GetListCompanyQuery query, ApiQueryPagination pagination) {
        List<Company> companies = this.list(query, pagination);
        Long count = this.count(query);

        Map<String, Object> result = new HashMap<>();
        result.put("list", CompanyRes.fromEntities(companies));
        result.put("total", count);

        return result;
    }

    private List<Company> list(GetListCompanyQuery query, ApiQueryPagination pagination) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Company> q = builder.createQuery(Company.class);
        Root<Company> root = q.from(Company.class);
        root.fetch(Company_.creator, JoinType.LEFT);
        q.select(root);

        q.where(this.getWhere(query, builder, root));
        q.orderBy(builder.desc(root.get(Company_.id)));

        return this.statelessSession.createSelectionQuery(q).setFirstResult((pagination.getPage() - 1) * pagination.getSize()).setMaxResults(pagination.getSize()).getResultList();
    }

    private Long count(GetListCompanyQuery query) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Long> q = builder.createQuery(Long.class);
        Root<Company> root = q.from(Company.class);
        q.select(builder.count(root));

        q.where(this.getWhere(query, builder, root));
        return this.statelessSession.createSelectionQuery(q).getSingleResult();
    }

    private Predicate[] getWhere(GetListCompanyQuery query, CriteriaBuilder builder, Root<Company> root) {
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(builder.equal(root.get(Company_.isGroup), query.isGroup()));
        if (query.getName() != null) {
            predicates.add(builder.like(root.get(Company_.name), "%" + query.getName() + "%"));
        }
        if (query.getId() != null) {
            predicates.add(builder.equal(root.get(Company_.id), query.getId()));
        }
        if (query.getParentId() != null) {
            predicates.add(builder.equal(root.get(Company_.parent), new Company(query.getParentId())));
        }

        return predicates.toArray(new Predicate[0]);
    }
}
