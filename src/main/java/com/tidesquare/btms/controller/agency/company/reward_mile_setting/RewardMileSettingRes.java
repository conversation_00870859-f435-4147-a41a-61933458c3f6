package com.tidesquare.btms.controller.agency.company.reward_mile_setting;

import com.tidesquare.btms.constant.RewardMileType;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
@RegisterForReflection
public class RewardMileSettingRes {
  private Long comanyId;
  private float ratio;
  private RewardMileType rewardMileType;
  private Integer maximumAccumulatedMiles;
  private Boolean isUse;
}
