package com.tidesquare.btms.controller.agency.company.get_next_group_code;

import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.repository.CompanyRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class GetNextGroupCodeHandler {
    @Inject
    private CompanyRepo companyRepo;

    public String run() {
        Company newestGroup = this.companyRepo.getNewestGroup();
        if (newestGroup == null) {
            return "G_A01";
        }

        String prevGroupCode = newestGroup.getGroupCode();
        String prevSymbol = prevGroupCode.substring(2, 3);
        int prevIndex = Integer.parseInt(prevGroupCode.substring(3));

        int nextIndex = prevIndex + 1;
        String nextSymbol = prevSymbol;
        if (nextIndex == 100) {
            nextIndex = 01;
            nextSymbol = String.valueOf((char) (prevSymbol.charAt(0) + 1));
        }

        return "G_" + nextSymbol + String.format("%02d", nextIndex);
    }
}
