package com.tidesquare.btms.controller.agency.company.get;

import com.tidesquare.btms.dto.response.CompanyRes;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.repository.CompanyRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class GetHandler {

    @Inject
    private CompanyRepo companyRepo;

    public CompanyRes run(Long id, GetCompanyQuery query) {
        Company company = this.companyRepo.findById(id, query.fields);
        return CompanyRes.fromEntity(company);
    }
}
