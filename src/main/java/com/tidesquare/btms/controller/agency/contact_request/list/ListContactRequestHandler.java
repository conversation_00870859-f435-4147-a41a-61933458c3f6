package com.tidesquare.btms.controller.agency.contact_request.list;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.common.ApiQueryPagination;
import com.tidesquare.btms.constant.ContactRequestStatus;
import com.tidesquare.btms.dto.response.ContactRequestRes;
import com.tidesquare.btms.entity.ContactRequest;
import com.tidesquare.btms.entity.ContactRequest_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class ListContactRequestHandler {

    @Inject
    private StatelessSession statelessSession;

    public Map<String, Object> run(ListContactRequestQuery query, ApiQueryPagination pagination) {
        List<ContactRequest> companies = this.list(query, pagination);
        Long count = this.count(query);

        Map<String, Object> result = new HashMap<>();
        result.put("list", ContactRequestRes.fromEntities(companies));
        result.put("total", count);

        return result;
    }

    public List<ContactRequest> list(ListContactRequestQuery query, ApiQueryPagination pagination) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<ContactRequest> q = builder.createQuery(ContactRequest.class);
        Root<ContactRequest> root = q.from(ContactRequest.class);
        root.fetch(ContactRequest_.respondent, JoinType.LEFT);
        q.select(root);

        if (query.getStatus() == null) {
            q.where(builder.or(
                    builder.equal(root.get(ContactRequest_.status), ContactRequestStatus.NEW),
                    builder.and(
                            builder.equal(root.get(ContactRequest_.status), ContactRequestStatus.ANSWERED),
                            builder.greaterThan(root.get(ContactRequest_.answeredAt), new Date(System.currentTimeMillis() - 1 * 30 * 24 * 60 * 60 * 1000L)))));
        } else if (query.getStatus().equals(ContactRequestStatus.NEW)) {
            q.where(builder.equal(root.get(ContactRequest_.status), ContactRequestStatus.NEW));
        } else if (query.getStatus().equals(ContactRequestStatus.ANSWERED)) {
            q.where(builder.equal(root.get(ContactRequest_.status), ContactRequestStatus.ANSWERED),
                    builder.greaterThan(root.get(ContactRequest_.answeredAt), new Date(System.currentTimeMillis() - 1 * 30 * 24 * 60 * 60 * 1000L)));
        }

        q.orderBy(builder.desc(root.get(ContactRequest_.id)));

        return this.statelessSession.createSelectionQuery(q).setFirstResult((pagination.getPage() - 1) * pagination.getSize()).setMaxResults(pagination.getSize()).getResultList();
    }

    public Long count(ListContactRequestQuery query) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Long> q = builder.createQuery(Long.class);
        Root<ContactRequest> root = q.from(ContactRequest.class);
        q.select(builder.count(root));

        if (query.getStatus() == null) {
            q.where(builder.or(
                    builder.equal(root.get(ContactRequest_.status), ContactRequestStatus.NEW),
                    builder.and(
                            builder.equal(root.get(ContactRequest_.status), ContactRequestStatus.ANSWERED),
                            builder.greaterThan(root.get(ContactRequest_.answeredAt), new Date(System.currentTimeMillis() - 1 * 30 * 24 * 60 * 60 * 1000L)))));
        } else if (query.getStatus().equals(ContactRequestStatus.NEW)) {
            q.where(builder.equal(root.get(ContactRequest_.status), ContactRequestStatus.NEW));
        } else if (query.getStatus().equals(ContactRequestStatus.ANSWERED)) {
            q.where(builder.equal(root.get(ContactRequest_.status), ContactRequestStatus.ANSWERED),
                    builder.greaterThan(root.get(ContactRequest_.answeredAt), new Date(System.currentTimeMillis() - 1 * 30 * 24 * 60 * 60 * 1000L)));
        }

        return this.statelessSession.createSelectionQuery(q).getSingleResult();
    }
}
