package com.tidesquare.btms.controller.agency.hotel.list_payment;

import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.QueryParam;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ListPaymentQuery {

    @NotNull
    @QueryParam("startTime")
    private Long startTime;

    @NotNull
    @QueryParam("endTime")
    private Long endTime;

    @QueryParam("managerId")
    private Long managerId;

    @QueryParam("managerDepartmentId")
    private Long managerDepartmentId;

    @QueryParam("companyId")
    private Long companyId;

    @QueryParam("paymentType")
    private PaymentType paymentType;

    @QueryParam("keywordType")
    private KeywordType keywordType;

    @QueryParam("keyword")
    private String keyword;
}
