package com.tidesquare.btms.controller.agency.company.reward_mile_setting;

import com.tidesquare.btms.constant.RewardMileType;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RewardMileSettingReq {
  private Long companyId;

  @Max(100)
  @Min(0)
  private float ratio;

  @Nullable
  private RewardMileType rewardMileType;

  @Nullable
  private Integer maximumAccumulatedMiles;

  private Boolean isUse;
}
