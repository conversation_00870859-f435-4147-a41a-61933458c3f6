package com.tidesquare.btms.controller.agency.company.reward_mile_history;

import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import com.tidesquare.btms.constant.RewardMileHistoryType;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.RewardMileHistory;
import com.tidesquare.btms.entity.User;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.CompanyRepo;
import com.tidesquare.btms.repository.RewardMileHistoryRepo;
import com.tidesquare.btms.repository.UserRepo;
import com.tidesquare.btms.utils.ExcelUtil;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response.Status;

@ApplicationScoped
public class RewardMileHistoryHandler {
  @Inject
  private RewardMileHistoryRepo rewardMileHistoryRepo;

  @Inject
  private UserRepo userRepo;

  @Inject
  private CompanyRepo companyRepo;

  @Transactional(rollbackOn = Exception.class)
  public void addRewardMileHistory(RewardMileHistoryReq body) {
    User user = this.userRepo.findById(body.getUserId());
    Company company = this.companyRepo.findById(body.getCompanyId());

    if (user == null) {
      throw new ApiException(Status.NOT_FOUND, ErrorCode.E404_USER_NOT_FOUND);
    }

    if (company == null) {
      throw new ApiException(Status.NOT_FOUND, ErrorCode.E404_COMPANY_NOT_FOUND);
    }

    RewardMileHistory lastRewardMileHistory = this.rewardMileHistoryRepo.findLastRewardMileHistory(body.getCompanyId());
    int totalMiles = lastRewardMileHistory == null ? 0 : lastRewardMileHistory.getTotalMiles();
    if (totalMiles + body.getUsedMiles() < 0) {
      throw new ApiException(Status.CONFLICT, ErrorCode.E409_TOTALMILES_CANNOT_LESS_THAN_ZERO);
    }

    RewardMileHistory rewardMileHistory = RewardMileHistory.builder()
        .company(company)
        .user(user)
        .totalMiles(totalMiles + body.getUsedMiles())
        .usedMiles(body.getUsedMiles())
        .reason(body.getReason())
        .usedDate(body.getUsedDate())
        .build();

    this.rewardMileHistoryRepo.createRewardMileHistory(rewardMileHistory);
  }

  public List<RewardMileHistoryRes> getListHistory(Long companyId, RewardMileHistoryType type) {
    List<RewardMileHistory> listRewardMileHistories = this.rewardMileHistoryRepo.getListRewardHistories(companyId,
        type);
    if (listRewardMileHistories == null)
      return null;
    List<RewardMileHistoryRes> res = new ArrayList<>();
    for (RewardMileHistory rewardMileHistory : listRewardMileHistories) {
      res.add(RewardMileHistoryRes.convert(rewardMileHistory));
    }
    return res;
  }

  public int getTotalMiles(Long companyId) {
    List<RewardMileHistory> listRewardMileHistories = this.rewardMileHistoryRepo.getListRewardHistories(companyId,
        RewardMileHistoryType.ALL);
    if (listRewardMileHistories != null && listRewardMileHistories.size() > 0) {
      return listRewardMileHistories.get(0).getTotalMiles().intValue();
    } else {
      return 0;
    }
  }

  public void exportToExcel(Long companyId, OutputStream outputStream, RewardMileHistoryType type) {
    try (SXSSFWorkbook workbook = new SXSSFWorkbook(100)) {
      Sheet sheet = workbook.createSheet("Reward Mile History");
      int rowNum = 0;

      List<String> headerList = List.of("날짜", "총 마일리지", "내역", "사유", "등록자");

      ExcelUtil.addTitleCellList(workbook, sheet.createRow(rowNum), headerList);
      int headerCount = headerList.size();

      List<RewardMileHistory> listRewardMileHistory = this.rewardMileHistoryRepo.getListRewardHistories(companyId,
          type);
      // Tạo định dạng số cho cột Used Miles
      CellStyle integerPositiveStyle = workbook.createCellStyle();
      integerPositiveStyle.setDataFormat(workbook.createDataFormat().getFormat("+#,##0"));

      CellStyle integerStyle = workbook.createCellStyle();
      integerStyle.setDataFormat(workbook.createDataFormat().getFormat("#,##0"));

      for (int i = 0; i < listRewardMileHistory.size(); i++) {
        List<Object> cellList = new ArrayList<>(headerCount);
        RewardMileHistory rewardMileHistory = listRewardMileHistory.get(i);
        cellList.add(rewardMileHistory.getUsedDate());
        cellList.add(rewardMileHistory.getTotalMiles());
        cellList.add(rewardMileHistory.getUsedMiles());
        cellList.add(rewardMileHistory.getReason());
        cellList.add(rewardMileHistory.getUser().getName());
        Row dataRow = sheet.createRow(++rowNum);
        ExcelUtil.addCellList2(workbook, dataRow, cellList);

        Cell usedMilesCell = dataRow.getCell(2);
        if (usedMilesCell != null) {
          if (rewardMileHistory.getUsedMiles() > 0) {
            usedMilesCell.setCellStyle(integerPositiveStyle);
          } else {
            usedMilesCell.setCellStyle(integerStyle);
          }
        }

        if (rowNum % 100 == 0) {
          ((SXSSFSheet) sheet).flushRows(100);
        }
      }

      workbook.write(outputStream);
      workbook.close();

    } catch (Exception e) {
      throw new RuntimeException("Failed to generate Excel file", e);
    }
  }
}
