package com.tidesquare.btms.controller.agency.company.update_children;

import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.HomepageSetting;
import com.tidesquare.btms.repository.CompanyRepo;
import com.tidesquare.btms.repository.HomepageSettingRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class UpdateChildrenHandler {

    @Inject
    private CompanyRepo companyRepo;

    @Inject
    private HomepageSettingRepo homepageSettingRepo;

    @Transactional(rollbackOn = Exception.class)
    public void run(Long companyId, UpdateCompanyChildrenBody body) {
        Company parentCompany = this.companyRepo.findById(companyId);
        if (!parentCompany.isGroup()) {
            return;
        }

        this.companyRepo.deleteAllChildren(companyId);
        this.companyRepo.updateParentId(companyId, body.getChildrenIds());

        //#region Sync HomepageSetting
        HomepageSetting homepageSetting = this.homepageSettingRepo.findByCompanyId(companyId);
        for (Long childId : body.getChildrenIds()) {
            HomepageSetting homepageSettingToInsert = new HomepageSetting();
            homepageSettingToInsert.setCompanyId(childId);
            homepageSettingToInsert.setUseDefaultLogo(homepageSetting.isUseDefaultLogo());
            homepageSettingToInsert.setLoginLogoAttachFile(homepageSetting.getLoginLogoAttachFile());
            homepageSettingToInsert.setGnbLogoAttachFile(homepageSetting.getGnbLogoAttachFile());
            this.homepageSettingRepo.insertOrUpdate(homepageSettingToInsert);
        }
        /* #endregion */
    }
}
