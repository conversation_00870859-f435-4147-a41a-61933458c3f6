package com.tidesquare.btms.controller.agency.travel.emergency_register;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.constant.PnrDataHistoryType;

import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;

import com.tidesquare.btms.service.stella.StellaService;
import com.tidesquare.btms.service.stella.dto.response.PNRInfoRes;
import com.tidesquare.btms.service.travel.TravelService;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response.Status;

@ApplicationScoped
public class EmergencyRegisterHandler {

    @Inject
    private TravelService travelService;

    @Inject
    private StellaService stellaService;

    @Transactional(rollbackOn = Exception.class)
    public void run(UserInfo userInfo, EmergencyRegisterBody body) {
        PNRInfoRes pnrInfo = this.stellaService.GetPNRInfo(body.getPnrNumber(), body.getGdsType(), null);
        if (pnrInfo == null) {
            throw new ApiException(Status.NOT_FOUND, ErrorCode.E404_PNR_NOT_FOUND);
        }

        this.travelService.savePnrInfo(userInfo, pnrInfo, PnrDataHistoryType.Emergency);
    }
}
