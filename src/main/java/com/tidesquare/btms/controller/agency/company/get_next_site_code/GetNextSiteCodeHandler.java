package com.tidesquare.btms.controller.agency.company.get_next_site_code;

import com.tidesquare.btms.constant.CompanyType;
import com.tidesquare.btms.repository.CompanyRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class GetNextSiteCodeHandler {
    @Inject
    private CompanyRepo companyRepo;

    public String run(CompanyType companyType) {
        return this.companyRepo.getNextSiteCode(companyType);
    }
}
