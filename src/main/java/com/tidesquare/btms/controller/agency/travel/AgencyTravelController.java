package com.tidesquare.btms.controller.agency.travel;

import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.controller.agency.travel.cancel.CancelHandler;
import com.tidesquare.btms.controller.agency.travel.emergency_register.EmergencyRegisterBody;
import com.tidesquare.btms.controller.agency.travel.emergency_register.EmergencyRegisterHandler;
import com.tidesquare.btms.controller.agency.travel.realtime_get_pnr_data.RealtimeGetPnrDataHandler;
import com.tidesquare.btms.controller.agency.travel.realtime_get_pnr_data.RealtimeGetPnrDataRes;
import com.tidesquare.btms.controller.agency.travel.realtime_update.RealtimeUpdateBody;
import com.tidesquare.btms.controller.agency.travel.realtime_update.RealtimeUpdateHandler;
import com.tidesquare.btms.controller.agency.travel.remove_reserver.AgencyTravelRemoveReserverHandler;
import com.tidesquare.btms.controller.agency.travel.update_reserver.AgencyTravelUpdateReserverBody;
import com.tidesquare.btms.controller.agency.travel.update_reserver.AgencyTravelUpdateReserverHandler;
import com.tidesquare.btms.filter.AgencyBinding;

import io.vertx.mutiny.core.Vertx;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;

@Path("agency/travel")
@AgencyBinding
@SecurityRequirement(name = "bearer")
public class AgencyTravelController {

    @Inject
    private EmergencyRegisterHandler emergencyRegisterHandler;

    @Inject
    private RealtimeUpdateHandler realtimeUpdateHandler;

    @Inject
    private CancelHandler cancelHandler;

    @Inject
    private RealtimeGetPnrDataHandler realtimeGetPnrDataHandler;

    @Inject
    private AgencyTravelRemoveReserverHandler removeReserverHandler;

    @Inject
    private AgencyTravelUpdateReserverHandler updateReserverHandler;

    @POST
    @Path("/emergency-register")
    @Operation(summary = "Emergency Register PNR")
    public ApiResponse<Void> emegencyRegister(@Valid EmergencyRegisterBody body) {
        UserInfo userInfo = Vertx.currentContext().getLocal("user");
        this.emergencyRegisterHandler.run(userInfo, body);
        return ApiResponse.fromData(null);
    }

    @POST
    @Path("/realtime-update")
    @Operation(summary = "Realtime Update PNR")
    public ApiResponse<Void> realtimeUpdate(@Valid RealtimeUpdateBody body) {
        UserInfo userInfo = Vertx.currentContext().getLocal("user");
        this.realtimeUpdateHandler.run(userInfo, body);
        return ApiResponse.fromData(null);
    }

    @POST
    @Path("{travelId}/cancel")
    @Operation(summary = "Cancel PNR")
    public ApiResponse<Void> cancelTravel(@PathParam("travelId") Long travelId) {
        UserInfo userInfo = Vertx.currentContext().getLocal("user");
        this.cancelHandler.run(userInfo, travelId);
        return ApiResponse.fromData(null);
    }

    @GET
    @Path("/{travelId}/realtime-get-pnr-data")
    @Operation(summary = "Realtime Update PNR")
    public ApiResponse<RealtimeGetPnrDataRes> realtimeGetPnrData(@Valid @PathParam("travelId") Long travelId) {
        UserInfo userInfo = Vertx.currentContext().getLocal("user");
        RealtimeGetPnrDataRes res = this.realtimeGetPnrDataHandler.run(userInfo, travelId);
        return ApiResponse.fromData(res);
    }

    @POST
    @Path("/{travelId}/remove-reserver")
    @Operation(summary = "Remove reserver")
    public ApiResponse<Void> removeReserver(@Valid @PathParam("travelId") Long travelId) {
        this.removeReserverHandler.run(travelId);
        return ApiResponse.fromData(null);
    }

    @POST
    @Path("/{travelId}/update-reserver")
    @Operation(summary = "Update reserver")
    public ApiResponse<Void> updateReserver(@Valid @PathParam("travelId") Long travelId, @Valid AgencyTravelUpdateReserverBody body) {
        UserInfo userInfo = Vertx.currentContext().getLocal("user");
        this.updateReserverHandler.run(userInfo, travelId, body);
        return ApiResponse.fromData(null);
    }
}
