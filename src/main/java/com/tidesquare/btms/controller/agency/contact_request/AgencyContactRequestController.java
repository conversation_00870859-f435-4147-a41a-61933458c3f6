package com.tidesquare.btms.controller.agency.contact_request;

import java.util.Map;

import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import com.tidesquare.btms.common.ApiQueryPagination;
import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.controller.agency.contact_request.get.GetContactRequestHandler;
import com.tidesquare.btms.controller.agency.contact_request.list.ListContactRequestHandler;
import com.tidesquare.btms.controller.agency.contact_request.list.ListContactRequestQuery;
import com.tidesquare.btms.controller.agency.contact_request.update.UpdateContactRequestBody;
import com.tidesquare.btms.controller.agency.contact_request.update.UpdateContactRequestHandler;
import com.tidesquare.btms.dto.response.ContactRequestRes;
import com.tidesquare.btms.filter.AgencyBinding;

import io.vertx.mutiny.core.Vertx;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.BeanParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;

@Path("agency/contact-request")
@AgencyBinding
@SecurityRequirement(name = "bearer")
public class AgencyContactRequestController {

    @Inject
    private ListContactRequestHandler listContactRequestHandler;

    @Inject
    private GetContactRequestHandler getContactRequestHandler;

    @Inject
    private UpdateContactRequestHandler updateContactRequestHandler;

    @GET
    @Path("")
    @Operation(summary = "Get all contact requests")
    public ApiResponse<Map<String, Object>> list(@BeanParam ListContactRequestQuery query, @BeanParam ApiQueryPagination pagination) {
        Map<String, Object> result = this.listContactRequestHandler.run(query, pagination);
        return ApiResponse.fromData(result);
    }

    @GET
    @Path("/{id}")
    @Operation(summary = "Get contact request by ID")
    public ApiResponse<ContactRequestRes> get(@PathParam("id") Long id) {
        return ApiResponse.fromData(this.getContactRequestHandler.run(id));
    }

    @PUT
    @Path("/{id}")
    @Operation(summary = "Update contact request")
    public ApiResponse<Void> update(@PathParam("id") Long id, @Valid UpdateContactRequestBody body) {
        UserInfo userInfo = Vertx.currentContext().getLocal("user");
        this.updateContactRequestHandler.run(userInfo, id, body);
        return ApiResponse.fromData(null);
    }
}
