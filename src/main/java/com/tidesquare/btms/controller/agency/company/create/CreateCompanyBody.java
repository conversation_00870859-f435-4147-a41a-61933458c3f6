package com.tidesquare.btms.controller.agency.company.create;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tidesquare.btms.constant.CompanyType;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateCompanyBody {
    @NotBlank
    private String name;

    @NotNull
    private String siteCode;

    @NotNull
    private String erpSiteCode;

    // @NotBlank
    private String url;

    // @NotBlank
    private String adminUrl;

    @JsonProperty(value = "isGroup")
    private boolean isGroup;

    @NotNull
    private CompanyType companyType;

    @NotNull
    private String groupCode;

    private String[] emailDomains;
}
