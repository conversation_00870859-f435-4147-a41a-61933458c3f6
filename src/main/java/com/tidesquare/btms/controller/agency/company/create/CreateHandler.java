package com.tidesquare.btms.controller.agency.company.create;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.TravelAgencyUser;
import com.tidesquare.btms.entity.embeddable.BtmsSetting;
import com.tidesquare.btms.repository.CompanyRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class CreateHandler {

    @Inject
    private CompanyRepo companyRepo;

    @Transactional(rollbackOn = Exception.class)
    public CreateCompanyRes run(UserInfo userInfo, CreateCompanyBody body) {
        Company company = new Company();
        company.setCompanyType(body.getCompanyType());
        company.setName(body.getName());
        company.setSiteCode(body.getSiteCode());
        company.setErpSiteCode(body.getErpSiteCode());
        company.setGroup(body.isGroup());
        company.setGroupCode(body.getGroupCode());
        company.setCreator(new TravelAgencyUser(userInfo.getUserId()));

        BtmsSetting btmsSetting = new BtmsSetting();
        btmsSetting.setUrl(body.getUrl());
        btmsSetting.setAdminUrl(body.getAdminUrl());
        btmsSetting.setApplyStartYmd("20190101");
        btmsSetting.setApplyEndYmd("20301231");
        if (body.getEmailDomains() != null && body.getEmailDomains().length > 0) {
            btmsSetting.setEmailDomainList(body.getEmailDomains());
        } else {
            btmsSetting.setEmailDomainList(new String[] { "tidesquare.com" });
        }
        company.setBtmsSetting(btmsSetting);

        this.companyRepo.insert(company);

        CreateCompanyRes createCompanyRes = new CreateCompanyRes();
        createCompanyRes.setId(company.getId());
        return createCompanyRes;
    }
}
