package com.tidesquare.btms.controller.agency.company.get;

import java.util.List;

import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import com.tidesquare.btms.param_converter.ListString;

import jakarta.ws.rs.QueryParam;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GetCompanyQuery {
    @QueryParam("fields")
    @ListString
    @Schema(type = SchemaType.STRING, example = "excludeAirlines", description = "Comma-separated list of fields to include")
    public List<String> fields;
}
