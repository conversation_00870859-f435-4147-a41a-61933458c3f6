package com.tidesquare.btms.controller.agency.company.auto_complete;

import java.util.List;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.dto.response.CompanyRes;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.Company_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class AutoCompleteHandler {

    @Inject
    private StatelessSession statelessSession;

    public List<CompanyRes> run(boolean isGroup, String name) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Company> q = builder.createQuery(Company.class);
        Root<Company> root = q.from(Company.class);
        q.select(root);

        q.where(builder.equal(root.get(Company_.isGroup), isGroup), builder.like(root.get(Company_.name), "%" + name + "%"));
        q.orderBy(builder.asc(root.get(Company_.id)));

        List<Company> companies = this.statelessSession.createSelectionQuery(q).setMaxResults(10).getResultList();
        return CompanyRes.fromEntities(companies);
    }

}
