package com.tidesquare.btms.controller.agency.hotel;

import java.util.Map;

import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import com.tidesquare.btms.common.ApiQueryPagination;
import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.controller.agency.hotel.list_payment.ListPaymentHandler;
import com.tidesquare.btms.controller.agency.hotel.list_payment.ListPaymentQuery;
import com.tidesquare.btms.filter.AgencyBinding;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.BeanParam;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.StreamingOutput;

@Path("agency/hotel")
@AgencyBinding
@SecurityRequirement(name = "bearer")
public class AgencyHotelController {

    @Inject
    private ListPaymentHandler listPaymentHandler;

    @GET
    @Path("/payment")
    @Operation(summary = "Get List Hotel Payment")
    public ApiResponse<Map<String, Object>> getListHotelPayment(@Valid @BeanParam ListPaymentQuery query, @BeanParam ApiQueryPagination pagination) throws Exception {
        return ApiResponse.fromData(this.listPaymentHandler.run(query, pagination));
    }

    @GET
    @Path("/payment/export")
    @Operation(summary = "Export hotel payment to Excel")
    @Produces("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    public Response exportHotelPayment(@Valid @BeanParam ListPaymentQuery query) throws Exception {
        StreamingOutput stream = outputStream -> {
            this.listPaymentHandler.exportToExcel(query, outputStream);
        };
        return Response.ok(stream)
                .header("Content-Disposition", "attachment; filename=\"hotel_payment.xlsx\"")
                .build();
    }
}
