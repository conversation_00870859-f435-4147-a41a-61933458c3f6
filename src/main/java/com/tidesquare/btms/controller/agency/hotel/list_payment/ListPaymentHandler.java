package com.tidesquare.btms.controller.agency.hotel.list_payment;

import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.hibernate.StatelessSession;
import org.hibernate.query.SelectionQuery;

import com.tidesquare.btms.common.ApiQueryPagination;
import com.tidesquare.btms.constant.BookingType;
import com.tidesquare.btms.constant.GubunType;
import com.tidesquare.btms.constant.PaymentMethod;
import com.tidesquare.btms.constant.PaymentStatus;
import com.tidesquare.btms.dto.response.PaymentRes;
import com.tidesquare.btms.entity.BookingHotel;
import com.tidesquare.btms.entity.BookingHotelGuest;
import com.tidesquare.btms.entity.BookingHotelGuest_;
import com.tidesquare.btms.entity.BookingHotel_;
import com.tidesquare.btms.entity.Company_;
import com.tidesquare.btms.entity.Department_;
import com.tidesquare.btms.entity.DocumentNumber;
import com.tidesquare.btms.entity.DocumentNumber_;
import com.tidesquare.btms.entity.HotelPaymentMapping;
import com.tidesquare.btms.entity.HotelPaymentMapping_;
import com.tidesquare.btms.entity.Payment;
import com.tidesquare.btms.entity.PaymentCard;
import com.tidesquare.btms.entity.PaymentCard_;
import com.tidesquare.btms.entity.PaymentCash;
import com.tidesquare.btms.entity.PaymentCash_;
import com.tidesquare.btms.entity.Payment_;
import com.tidesquare.btms.entity.TravelAgencyUser;
import com.tidesquare.btms.entity.TravelAgencyUser_;
import com.tidesquare.btms.repository.BookingHotelGuestRepo;
import com.tidesquare.btms.repository.DocumentNumberRepo;
import com.tidesquare.btms.repository.PaymentCashRepo;
import com.tidesquare.btms.utils.DateUtil;
import com.tidesquare.btms.utils.ExcelUtil;
import com.tidesquare.btms.utils.StringUtils;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.context.control.RequestContextController;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Fetch;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.persistence.criteria.Subquery;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class ListPaymentHandler {

    @Inject
    private StatelessSession statelessSession;

    @Inject
    private BookingHotelGuestRepo bookingHotelGuestRepo;

    @Inject
    private PaymentCashRepo paymentCashRepo;

    @Inject
    private DocumentNumberRepo documentNumberRepo;

    @Inject
    private RequestContextController requestContextController;

    public Map<String, Object> run(ListPaymentQuery query, ApiQueryPagination pagination) throws Exception {
        Map<String, Object> result = new HashMap<>();
        try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
            Future<List<Payment>> paymentsFuture = executor.submit(() -> {
                try {
                    this.requestContextController.activate();
                    return this.list(query, pagination);
                } finally {
                    this.requestContextController.deactivate();
                }
            });
            Future<Long> totalFuture = executor.submit(() -> {
                try {
                    this.requestContextController.activate();
                    return this.count(query);
                } finally {
                    this.requestContextController.deactivate();
                }
            });

            result.put("list", PaymentRes.fromEntities(paymentsFuture.get()));
            result.put("total", totalFuture.get());
        }

        return result;
    }

    public void exportToExcel(ListPaymentQuery query, OutputStream outputStream) {
        try (SXSSFWorkbook workbook = new SXSSFWorkbook(100)) {
            List<Payment> payments = this.list(query, null);
            Sheet sheet = workbook.createSheet("Hotel Payment History");

            List<String> headerList = new ArrayList<>();
            headerList.add("No.");
            headerList.add("고객사명");
            headerList.add("TCode");
            headerList.add("호텔명");
            headerList.add("국가");
            headerList.add("도시");
            headerList.add("입금일/환불일");
            headerList.add("예약자");
            headerList.add("투숙객");
            headerList.add("체크인");
            headerList.add("체크아웃");
            headerList.add("박수");
            headerList.add("객실수");
            headerList.add("입금구분");
            headerList.add("PG구분");
            headerList.add("MID");
            headerList.add("세부구분");
            headerList.add("계좌/카드");
            headerList.add("승인번호");
            headerList.add("금액");
            headerList.add("입금고객");
            headerList.add("문서번호");
            headerList.add("처리사원");

            ExcelUtil.addTitleCellList(workbook, sheet.createRow(0), headerList);
            int headerCount = headerList.size();

            CellStyle integerPositiveStyle = workbook.createCellStyle();
            integerPositiveStyle.setDataFormat(workbook.createDataFormat().getFormat("+#,##0"));

            CellStyle integerStyle = workbook.createCellStyle();
            integerStyle.setDataFormat(workbook.createDataFormat().getFormat("#,##0"));

            for (int i = 0; i < payments.size(); i++) {
                Payment payment = payments.get(i);
                List<Object> cellList = new ArrayList<>(headerCount);
                String bankCard = "";
                String approvalNumber = "";
                Double payAmount = 0d;
                String customer = "-";
                String depositYmd = "";
                String pgType = "";
                String mid = "";
                String cardNumber = "";
                if (payment.getPaymentMethod().equals(PaymentMethod.CARD)) {
                    if (payment.getPaymentCard().getCardCompanyCode() != null) {
                        bankCard = payment.getPaymentCard().getCardCompanyCode().getText();
                    }
                    approvalNumber = payment.getPaymentCard().getApprovalNumber();
                    payAmount = payment.getPaymentAmount();
                    if (!StringUtils.isNullOrEmpty(payment.getPaymentCard().getCustomerName())) {
                        customer = payment.getPaymentCard().getCustomerName();
                    }
                    depositYmd = DateUtil.date2String(payment.getCreateDate(), "yyyy-MM-dd");
                    pgType = "카드(PG)";
                    mid = payment.getPaymentCard().getMid();
                    cardNumber = payment.getPaymentCard().getCardNumber();
                } else if (payment.getPaymentMethod().equals(PaymentMethod.CASH)) {
                    bankCard = payment.getPaymentCashs().first().getAccountManageName();
                    approvalNumber = payment.getPaymentCashs().first().getCollectNumber();
                    payAmount = payment.getPaymentCashs().first().getReceiptAmount();
                    if (!StringUtils.isNullOrEmpty(payment.getPaymentCashs().first().getDepCustName())) {
                        customer = payment.getPaymentCashs().first().getDepCustName();
                    }
                    depositYmd = DateUtil.date2String(DateUtil.string2Date(payment.getPaymentCashs().first().getMatchDate(), "yyyyMMdd"), "yyyy-MM-dd");
                    pgType = "보통예금";
                }
                if (payment.getGubun().equals(GubunType.HOTEL_REFUND)) {
                    payAmount = -payAmount;
                }
                cellList.add(i + 1);
                if (payment.getHotelPaymentMapping().getBookingHotel().getCompany() == null) {
                    cellList.add("-");
                } else {
                    cellList.add(payment.getHotelPaymentMapping().getBookingHotel().getCompany().getName());
                }
                cellList.add(payment.getHotelPaymentMapping().getBookingHotel().getBookingCode());
                cellList.add(payment.getHotelPaymentMapping().getBookingHotel().getHotelNameKr());
                cellList.add(payment.getHotelPaymentMapping().getBookingHotel().getNatNm());
                cellList.add(payment.getHotelPaymentMapping().getBookingHotel().getEndCityNm());
                cellList.add(depositYmd);
                cellList.add(payment.getHotelPaymentMapping().getBookingHotel().getReserverName());
                cellList.add(String.join(", ", payment.getHotelPaymentMapping().getBookingHotel().getBookingHotelGuests().stream().map(bookingHotelGuest -> bookingHotelGuest.getGuestName()).toList()));
                cellList.add(DateUtil.date2String(DateUtil.string2Date(payment.getHotelPaymentMapping().getBookingHotel().getCheckInYmd(), "yyyyMMdd"), "yyyy-MM-dd"));
                cellList.add(DateUtil.date2String(DateUtil.string2Date(payment.getHotelPaymentMapping().getBookingHotel().getCheckOutYmd(), "yyyyMMdd"), "yyyy-MM-dd"));
                cellList.add(DateUtil.diffDays(payment.getHotelPaymentMapping().getBookingHotel().getCheckOutYmd(), payment.getHotelPaymentMapping().getBookingHotel().getCheckInYmd(), "yyyyMMdd"));
                cellList.add(payment.getHotelPaymentMapping().getBookingHotel().getRoomCnt());
                cellList.add(payment.getGubun().getText());
                cellList.add(pgType);
                cellList.add(mid);
                cellList.add(bankCard);
                cellList.add(cardNumber);
                cellList.add(approvalNumber);
                cellList.add(payAmount);
                cellList.add(customer);
                cellList.add(String.join(", ", payment.getHotelPaymentMapping().getBookingHotel().getDocumentNumbers().stream().map(documentNumber -> documentNumber.getDocumentNo()).toList()));
                if (payment.getHandlerUser() == null) {
                    cellList.add("-");
                } else {
                    cellList.add(payment.getHandlerUser().getName());
                }

                Row dataRow = sheet.createRow(i + 1);
                ExcelUtil.addCellList2(workbook, dataRow, cellList);

                Cell payAmountCell = dataRow.getCell(20);
                if (payAmount > 0) {
                    payAmountCell.setCellStyle(integerPositiveStyle);
                } else {
                    payAmountCell.setCellStyle(integerStyle);
                }

                if ((i + 1) % 100 == 0) {
                    ((SXSSFSheet) sheet).flushRows(100);
                }
            }
            workbook.write(outputStream);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate Excel file", e);
        }

    }

    private List<Payment> list(ListPaymentQuery query, ApiQueryPagination pagination) throws Exception {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Payment> q = builder.createQuery(Payment.class);
        Root<Payment> root = q.from(Payment.class);
        Join<Payment, PaymentCash> paymentCashJoin = root.join(Payment_.paymentCashs, JoinType.LEFT); // payment for hotel only has one payment cash
        Fetch<Payment, HotelPaymentMapping> hotelPaymentMappingFetch = root.fetch(Payment_.hotelPaymentMapping, JoinType.INNER);
        Join<Payment, HotelPaymentMapping> hotelPaymentMappingJoin = (Join<Payment, HotelPaymentMapping>) hotelPaymentMappingFetch;
        Fetch<HotelPaymentMapping, BookingHotel> bookingHotelFetch = hotelPaymentMappingJoin.fetch(HotelPaymentMapping_.bookingHotel, JoinType.INNER);
        Join<HotelPaymentMapping, BookingHotel> bookingHotelJoin = (Join<HotelPaymentMapping, BookingHotel>) bookingHotelFetch;
        Fetch<BookingHotel, TravelAgencyUser> managerFetch = bookingHotelFetch.fetch(BookingHotel_.manager, JoinType.LEFT);
        Join<BookingHotel, TravelAgencyUser> managerJoin = (Join<BookingHotel, TravelAgencyUser>) managerFetch;
        Fetch<Payment, PaymentCard> paymentCardFetch = root.fetch(Payment_.paymentCard, JoinType.LEFT);
        Join<Payment, PaymentCard> paymentCardJoin = (Join<Payment, PaymentCard>) paymentCardFetch;
        bookingHotelFetch.fetch(BookingHotel_.company, JoinType.LEFT);
        root.fetch(Payment_.handlerUser, JoinType.LEFT);
        q.select(root);

        q.where(this.getWhere(query, builder, root, q, bookingHotelJoin, managerJoin, paymentCardJoin, paymentCashJoin));
        q.orderBy(builder.desc(root.get(Payment_.paymentId)));

        SelectionQuery<Payment> selectionQuery = this.statelessSession.createSelectionQuery(q);
        if (pagination != null) {
            selectionQuery.setFirstResult((pagination.getPage() - 1) * pagination.getSize()).setMaxResults(pagination.getSize());
        }
        List<Payment> payments = selectionQuery.getResultList();

        try (ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor()) {
            List<Long> paymentIds = payments.stream().filter(payment -> payment.getPaymentMethod().equals(PaymentMethod.CASH)).map(payment -> payment.getPaymentId()).toList();
            List<Long> bookingHotelIds = payments.stream().map(payment -> payment.getHotelPaymentMapping().getBookingHotel().getId()).toList();

            Future<Map<Long, SortedSet<PaymentCash>>> paymentCashMapFuture = null;
            if (!paymentIds.isEmpty()) {
                paymentCashMapFuture = executor.submit(() -> {
                    try {
                        this.requestContextController.activate();
                        return this.paymentCashRepo.getPaymentCashByPaymentIds(paymentIds);
                    } finally {
                        this.requestContextController.deactivate();
                    }
                });
            }

            Future<Map<Long, SortedSet<BookingHotelGuest>>> bookingHotelGuestMapFuture = executor.submit(() -> {
                try {
                    this.requestContextController.activate();
                    return this.bookingHotelGuestRepo.getMapByBookingHotelIds(bookingHotelIds);
                } finally {
                    this.requestContextController.deactivate();
                }
            });

            Future<Map<Long, SortedSet<DocumentNumber>>> documentNumberMapFuture = executor.submit(() -> {
                try {
                    this.requestContextController.activate();
                    return this.documentNumberRepo.getMapByBookingIdsAndBookingType(bookingHotelIds, BookingType.HOTEL);
                } finally {
                    this.requestContextController.deactivate();
                }
            });

            Map<Long, SortedSet<PaymentCash>> paymentCashMap = paymentCashMapFuture != null ? paymentCashMapFuture.get() : new HashMap<>();
            Map<Long, SortedSet<BookingHotelGuest>> bookingHotelGuestMap = bookingHotelGuestMapFuture.get();
            Map<Long, SortedSet<DocumentNumber>> documentNumberMap = documentNumberMapFuture.get();

            for (Payment payment : payments) {
                if (paymentCashMap.containsKey(payment.getPaymentId())) {
                    payment.setPaymentCashs(paymentCashMap.get(payment.getPaymentId()));
                } else {
                    payment.setPaymentCashs(new TreeSet<>());
                }
                payment.getHotelPaymentMapping().getBookingHotel().setBookingHotelGuests(bookingHotelGuestMap.get(payment.getHotelPaymentMapping().getBookingHotel().getId()));
                if (documentNumberMap.containsKey(payment.getHotelPaymentMapping().getBookingHotel().getId())) {
                    payment.getHotelPaymentMapping().getBookingHotel().setDocumentNumbers(documentNumberMap.get(payment.getHotelPaymentMapping().getBookingHotel().getId()));
                } else {
                    payment.getHotelPaymentMapping().getBookingHotel().setDocumentNumbers(new TreeSet<>());
                }
            }
        }

        return payments;
    }

    private Long count(ListPaymentQuery query) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Long> q = builder.createQuery(Long.class);
        Root<Payment> root = q.from(Payment.class);
        Join<Payment, PaymentCash> paymentCashJoin = root.join(Payment_.paymentCashs, JoinType.LEFT); // payment for hotel only has one payment cash
        Join<Payment, HotelPaymentMapping> hotelPaymentMappingJoin = root.join(Payment_.hotelPaymentMapping, JoinType.INNER);
        Join<HotelPaymentMapping, BookingHotel> bookingHotelJoin = null;
        Join<BookingHotel, TravelAgencyUser> managerJoin = null;
        Join<Payment, PaymentCard> paymentCardJoin = null;

        if (query.getManagerId() != null
                || query.getManagerDepartmentId() != null
                || (!StringUtils.isNullOrEmpty(query.getKeyword())
                        && (query.getKeywordType().equals(KeywordType.BOOKING_ID)
                                || query.getKeywordType().equals(KeywordType.BOOKING_CODE)
                                || query.getKeywordType().equals(KeywordType.RESERVER_NAME)
                                || query.getKeywordType().equals(KeywordType.GUEST_NAME)
                                || query.getKeywordType().equals(KeywordType.DOCUMENT_NUMBER)))) {
            bookingHotelJoin = bookingHotelJoin == null ? hotelPaymentMappingJoin.join(HotelPaymentMapping_.bookingHotel, JoinType.INNER) : bookingHotelJoin;
        }
        if (query.getManagerDepartmentId() != null && query.getManagerId() == null) {
            managerJoin = bookingHotelJoin.join(BookingHotel_.manager, JoinType.LEFT);
        }
        if (!StringUtils.isNullOrEmpty(query.getKeyword()) && query.getKeywordType().equals(KeywordType.APPROVAL_NUMBER)) {
            paymentCardJoin = root.join(Payment_.paymentCard, JoinType.LEFT);
        }

        q.select(builder.count(root));

        q.where(this.getWhere(query, builder, root, q, bookingHotelJoin, managerJoin, paymentCardJoin, paymentCashJoin));
        return this.statelessSession.createSelectionQuery(q).getSingleResult();
    }

    private Predicate[] getWhere(ListPaymentQuery query, CriteriaBuilder builder, Root<Payment> root, CriteriaQuery<?> q,
            Join<?, BookingHotel> bookingHotelJoin, Join<?, TravelAgencyUser> managerJoin,
            Join<?, PaymentCard> paymentCardJoin, Join<?, PaymentCash> paymentCashJoin) {
        List<Predicate> predicates = new ArrayList<>();

        predicates.add(
                builder.or(
                        builder.and(
                                builder.equal(root.get(Payment_.paymentMethod), PaymentMethod.CARD),
                                builder.greaterThanOrEqualTo(root.get(Payment_.createDate), new Date(query.getStartTime())),
                                builder.lessThanOrEqualTo(root.get(Payment_.createDate), new Date(query.getEndTime()))),
                        builder.and(
                                builder.equal(root.get(Payment_.paymentMethod), PaymentMethod.CASH),
                                builder.greaterThanOrEqualTo(paymentCashJoin.get(PaymentCash_.matchDate), DateUtil.date2String(new Date(query.getStartTime()), "yyyyMMdd")),
                                builder.lessThanOrEqualTo(paymentCashJoin.get(PaymentCash_.matchDate), DateUtil.date2String(new Date(query.getEndTime()), "yyyyMMdd")))));
        if (query.getCompanyId() != null) {
            predicates.add(builder.equal(root.get(Payment_.company).get(Company_.id), query.getCompanyId()));
        }
        if (query.getManagerId() != null) {
            predicates.add(builder.equal(bookingHotelJoin.get(BookingHotel_.manager).get(TravelAgencyUser_.id), query.getManagerId()));
        } else if (query.getManagerDepartmentId() != null) {
            predicates.add(builder.equal(managerJoin.get(TravelAgencyUser_.department).get(Department_.id), query.getManagerDepartmentId()));
        }
        if (query.getPaymentType() != null) {
            if (query.getPaymentType().equals(PaymentType.DEPOSIT)) {
                predicates.add(builder.equal(root.get(Payment_.gubun), GubunType.HOTEL_DEPOSIT));

                Subquery<Long> subqueryPaymentCancel = q.subquery(Long.class);
                Root<Payment> rootPaymentCancel = subqueryPaymentCancel.from(Payment.class);
                subqueryPaymentCancel
                        .select(builder.literal(1L))
                        .where(builder.equal(rootPaymentCancel.get(Payment_.parentPaymentId), root.get(Payment_.paymentId)),
                                builder.equal(rootPaymentCancel.get(Payment_.paymentStatus), PaymentStatus.CANCEL));
                predicates.add(builder.not(builder.exists(subqueryPaymentCancel)));
            } else if (query.getPaymentType().equals(PaymentType.REFUND)) {
                predicates.add(builder.equal(root.get(Payment_.gubun), GubunType.HOTEL_REFUND));
            }
        } else {
            Subquery<Long> subqueryPaymentCancel = q.subquery(Long.class);
            Root<Payment> rootPaymentCancel = subqueryPaymentCancel.from(Payment.class);
            subqueryPaymentCancel
                    .select(builder.literal(1L))
                    .where(builder.equal(rootPaymentCancel.get(Payment_.parentPaymentId), root.get(Payment_.paymentId)),
                            builder.equal(rootPaymentCancel.get(Payment_.paymentStatus), PaymentStatus.CANCEL));
            predicates.add(builder.or(
                    builder.equal(root.get(Payment_.gubun), GubunType.HOTEL_REFUND),
                    builder.and(builder.equal(root.get(Payment_.gubun), GubunType.HOTEL_DEPOSIT),
                            builder.not(builder.exists(subqueryPaymentCancel)))));
        }
        if (!StringUtils.isNullOrEmpty(query.getKeyword())) {
            if (query.getKeywordType().equals(KeywordType.BOOKING_ID)) {
                predicates.add(builder.equal(bookingHotelJoin.get(BookingHotel_.bookingId), Long.parseLong(query.getKeyword())));
            } else if (query.getKeywordType().equals(KeywordType.APPROVAL_NUMBER)) {
                predicates.add(
                        builder.or(
                                builder.and(
                                        builder.equal(root.get(Payment_.paymentMethod), PaymentMethod.CARD),
                                        builder.equal(paymentCardJoin.get(PaymentCard_.approvalNumber), query.getKeyword())),
                                builder.and(
                                        builder.equal(root.get(Payment_.paymentMethod), PaymentMethod.CASH),
                                        builder.equal(paymentCashJoin.get(PaymentCash_.collectNumber), query.getKeyword()))));
            } else if (query.getKeywordType().equals(KeywordType.BOOKING_CODE)) {
                predicates.add(builder.equal(bookingHotelJoin.get(BookingHotel_.bookingCode), query.getKeyword()));
            } else if (query.getKeywordType().equals(KeywordType.RESERVER_NAME)) {
                predicates.add(builder.like(bookingHotelJoin.get(BookingHotel_.reserverName), "%" + query.getKeyword() + "%"));
            } else if (query.getKeywordType().equals(KeywordType.GUEST_NAME)) {
                Subquery<Long> subqueryBookingHotelGuest = q.subquery(Long.class);
                Root<BookingHotelGuest> rootBookingHotelGuest = subqueryBookingHotelGuest.from(BookingHotelGuest.class);
                subqueryBookingHotelGuest
                        .select(builder.literal(1L))
                        .where(builder.equal(rootBookingHotelGuest.get(BookingHotelGuest_.bookingHotelId), bookingHotelJoin.get(BookingHotel_.id)),
                                builder.or(
                                        builder.like(rootBookingHotelGuest.get(BookingHotelGuest_.guestName), "%" + query.getKeyword() + "%"),
                                        builder.like(rootBookingHotelGuest.get(BookingHotelGuest_.firstName), "%" + query.getKeyword() + "%"),
                                        builder.like(rootBookingHotelGuest.get(BookingHotelGuest_.lastName), "%" + query.getKeyword() + "%")));
                predicates.add(builder.exists(subqueryBookingHotelGuest));
            } else if (query.getKeywordType().equals(KeywordType.DOCUMENT_NUMBER)) {
                Subquery<Long> subqueryDocumentNumber = q.subquery(Long.class);
                Root<DocumentNumber> rootDocumentNumber = subqueryDocumentNumber.from(DocumentNumber.class);
                subqueryDocumentNumber
                        .select(builder.literal(1L))
                        .where(builder.equal(rootDocumentNumber.get(DocumentNumber_.bookingId), bookingHotelJoin.get(BookingHotel_.id)),
                                builder.equal(rootDocumentNumber.get(DocumentNumber_.bookingType), BookingType.HOTEL),
                                builder.equal(rootDocumentNumber.get(DocumentNumber_.documentNo), query.getKeyword()));
            } else if (query.getKeywordType().equals(KeywordType.AMOUNT)) {
                String[] amountRange = query.getKeyword().split("~");
                Double minAmount = amountRange.length == 0 || amountRange[0].equals("") ? null : Double.parseDouble(amountRange[0]);
                Double maxAmount = amountRange.length <= 1 || amountRange[1].equals("") ? null : Double.parseDouble(amountRange[1]);
                if (minAmount != null || maxAmount != null) {
                    List<Predicate> predicatesCardDeposit = new ArrayList<>();
                    predicatesCardDeposit.add(builder.equal(root.get(Payment_.gubun), GubunType.HOTEL_DEPOSIT));
                    predicatesCardDeposit.add(builder.equal(root.get(Payment_.paymentMethod), PaymentMethod.CARD));
                    List<Predicate> predicatesCashDeposit = new ArrayList<>();
                    predicatesCashDeposit.add(builder.equal(root.get(Payment_.gubun), GubunType.HOTEL_DEPOSIT));
                    predicatesCashDeposit.add(builder.equal(root.get(Payment_.paymentMethod), PaymentMethod.CASH));
                    List<Predicate> predicatesCardRefund = new ArrayList<>();
                    predicatesCardRefund.add(builder.equal(root.get(Payment_.gubun), GubunType.HOTEL_REFUND));
                    predicatesCardRefund.add(builder.equal(root.get(Payment_.paymentMethod), PaymentMethod.CARD));
                    List<Predicate> predicatesCashRefund = new ArrayList<>();
                    predicatesCashRefund.add(builder.equal(root.get(Payment_.gubun), GubunType.HOTEL_REFUND));
                    predicatesCashRefund.add(builder.equal(root.get(Payment_.paymentMethod), PaymentMethod.CASH));
                    if (minAmount != null) {
                        predicatesCardDeposit.add(builder.greaterThanOrEqualTo(root.get(Payment_.paymentAmount), minAmount));
                        predicatesCashDeposit.add(builder.greaterThanOrEqualTo(paymentCashJoin.get(PaymentCash_.receiptAmount), minAmount));
                        predicatesCardRefund.add(builder.lessThanOrEqualTo(root.get(Payment_.paymentAmount), -minAmount));
                        predicatesCashRefund.add(builder.lessThanOrEqualTo(paymentCashJoin.get(PaymentCash_.receiptAmount), -minAmount));
                    }
                    if (maxAmount != null) {
                        predicatesCardDeposit.add(builder.lessThanOrEqualTo(root.get(Payment_.paymentAmount), maxAmount));
                        predicatesCashDeposit.add(builder.lessThanOrEqualTo(paymentCashJoin.get(PaymentCash_.receiptAmount), maxAmount));
                        predicatesCardRefund.add(builder.greaterThanOrEqualTo(root.get(Payment_.paymentAmount), -maxAmount));
                        predicatesCashRefund.add(builder.greaterThanOrEqualTo(paymentCashJoin.get(PaymentCash_.receiptAmount), -maxAmount));
                    }

                    List<Predicate> predicatesOr = new ArrayList<>();
                    if (query.getPaymentType() == null) {
                        predicatesOr.add(builder.and(predicatesCardDeposit.toArray(new Predicate[0])));
                        predicatesOr.add(builder.and(predicatesCashDeposit.toArray(new Predicate[0])));
                        predicatesOr.add(builder.and(predicatesCardRefund.toArray(new Predicate[0])));
                        predicatesOr.add(builder.and(predicatesCashRefund.toArray(new Predicate[0])));
                    } else if (query.getPaymentType().equals(PaymentType.DEPOSIT)) {
                        predicatesOr.add(builder.and(predicatesCardDeposit.toArray(new Predicate[0])));
                        predicatesOr.add(builder.and(predicatesCashDeposit.toArray(new Predicate[0])));
                    } else if (query.getPaymentType().equals(PaymentType.REFUND)) {
                        predicatesOr.add(builder.and(predicatesCardRefund.toArray(new Predicate[0])));
                        predicatesOr.add(builder.and(predicatesCashRefund.toArray(new Predicate[0])));
                    }

                    predicates.add(builder.or(predicatesOr.toArray(new Predicate[0])));
                }

            }
        }

        return predicates.toArray(new Predicate[0]);
    }
}
