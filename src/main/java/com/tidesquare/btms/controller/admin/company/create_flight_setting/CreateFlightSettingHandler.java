package com.tidesquare.btms.controller.admin.company.create_flight_setting;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.FlightSettingRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response.Status;

@ApplicationScoped
public class CreateFlightSettingHandler {

  @Inject
  private FlightSettingRepo flightSettingRepo;

  public void run(UserInfo userInfo, String content) {

    ObjectMapper objectMapper = new ObjectMapper();
    try {
      JsonNode jsonNode = objectMapper.readTree(content);
      if (jsonNode.isArray() && jsonNode.size() > 5) {
        throw new ApiException(Status.BAD_REQUEST, ErrorCode.E400_FLIGHT_SETTING_LIMIT_REACHED);
      }
    } catch (JsonProcessingException e) {
      throw new ApiException(Status.BAD_REQUEST, ErrorCode.E400_INVALID_JSON_FORMAT);
    }

    this.flightSettingRepo.insertOrUpdate(userInfo.getCompanyId(), content);
  }

}
