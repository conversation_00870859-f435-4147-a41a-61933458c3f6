package com.tidesquare.btms.controller.admin.travel;

import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.controller.admin.travel.cancel_travel_overseas.CancelTravelOverseasHandler;
import com.tidesquare.btms.controller.admin.travel.cancel_travel_overseas.CancelTravelOverseasBody;
import com.tidesquare.btms.filter.AdminBinding;

import io.vertx.mutiny.core.Vertx;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;

@Path("admin/travel")
@AdminBinding
@SecurityRequirement(name = "bearer")
public class AdminTravelController {

  @Inject
  private CancelTravelOverseasHandler cancelPRNHandler;

  @POST
  @Path("/overseas/{travelId}/cancel")
  @Operation(summary = "Cancel PRN")
  public ApiResponse<Void> cancelTravelOverseas(@Valid CancelTravelOverseasBody body, @PathParam("travelId") Long travelId) {
    UserInfo userInfo = Vertx.currentContext().getLocal("user");
    this.cancelPRNHandler.run(userInfo, body, travelId);
    return ApiResponse.fromData(null);
  }
}
