package com.tidesquare.btms.controller.admin.company.get;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.dto.response.CompanyRes;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.repository.CompanyRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class GetHandler {

  @Inject
  private CompanyRepo companyRepo;

  public CompanyRes run(UserInfo userInfo) {
    Company company = this.companyRepo.findById(userInfo.getCompanyId());
    return CompanyRes.fromEntity(company);
  }
}
