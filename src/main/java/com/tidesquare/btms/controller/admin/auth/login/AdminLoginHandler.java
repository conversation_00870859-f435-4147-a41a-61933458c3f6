package com.tidesquare.btms.controller.admin.auth.login;

import java.util.Date;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.constant.JoinStatus;
import com.tidesquare.btms.constant.UserType;
import com.tidesquare.btms.entity.Customer;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;
import com.tidesquare.btms.repository.CustomerRepo;
import com.tidesquare.btms.service.jwt.JwtService;

import at.favre.lib.crypto.bcrypt.BCrypt;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response.Status;

@ApplicationScoped
public class AdminLoginHandler {

    @Inject
    private CustomerRepo customerRepo;

    @Inject
    private JwtService jwtService;

    public AdminLoginRes run(AdminLoginBody body) {
        String email = body.getEmail();
        String password = body.getPassword();

        Customer admin = this.customerRepo.findFetchWorkspaceByEmailAndJoinStatus(UserType.customer, email, JoinStatus.Approval);

        if (admin == null || !admin.isAdmin()) {
            throw new ApiException(Status.FORBIDDEN, ErrorCode.E403);
        }

        BCrypt.Result result = BCrypt.verifyer().verify(password.toCharArray(), admin.getPassword());
        if (!result.verified) {
            throw new ApiException(Status.FORBIDDEN, ErrorCode.E403);
        }

        UserInfo userInfo = new UserInfo(admin.getId(), admin.getWorkspace().getCompany().getId(), UserType.customer, true);
        String accessToken = this.jwtService.createToken(userInfo);

        AdminLoginRes res = new AdminLoginRes();
        res.setAccessToken(accessToken);
        Date passwordUpdateDate = admin.getPasswordUpdateDate() == null ? admin.getCreateDate() : admin.getPasswordUpdateDate();
        Date nextPasswordUpdateDate = admin.getNextPasswordUpdateDate();

        if (passwordUpdateDate.getTime() + 6 * 30 * 24 * 60 * 60 * 1000L < System.currentTimeMillis()) {
            if (nextPasswordUpdateDate == null || nextPasswordUpdateDate.getTime() < System.currentTimeMillis()) {
                res.setNeedUpdatePassword(true);
            }
        }

        return res;
    }
}
