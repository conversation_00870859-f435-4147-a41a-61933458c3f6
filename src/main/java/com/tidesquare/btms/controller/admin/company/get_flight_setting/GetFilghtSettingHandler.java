package com.tidesquare.btms.controller.admin.company.get_flight_setting;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.repository.FlightSettingRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class GetFilghtSettingHandler {
  @Inject
  private FlightSettingRepo flightSettingRepo;

  public String run(UserInfo userInfo) {
    String flightSetting = this.flightSettingRepo.findByCompanyId(userInfo.getCompanyId());
    return flightSetting;
  }
}
