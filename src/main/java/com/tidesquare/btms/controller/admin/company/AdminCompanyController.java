package com.tidesquare.btms.controller.admin.company;

import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.controller.admin.company.create_flight_setting.CreateFlightSettingHandler;
import com.tidesquare.btms.controller.admin.company.get.GetHandler;
import com.tidesquare.btms.controller.admin.company.get_flight_setting.GetFilghtSettingHandler;
import com.tidesquare.btms.dto.response.CompanyRes;
import com.tidesquare.btms.filter.AdminBinding;

import io.vertx.mutiny.core.Vertx;
import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;

@Path("admin/company")
@Valid
@SecurityRequirement(name = "bearer")
@AdminBinding
public class AdminCompanyController {
  @Inject
  private GetFilghtSettingHandler getFilghtSettingHandler;

  @Inject
  private CreateFlightSettingHandler createFilghtSettingHandler;

  @Inject
  private GetHandler getHandler;

  @GET
  @Path("/flight-setting")
  @Operation(summary = "Get flight setting")
  public ApiResponse<String> getFilghtSetting() {
    UserInfo userInfo = Vertx.currentContext().getLocal("user");
    return ApiResponse.fromData(this.getFilghtSettingHandler.run(userInfo));
  }

  @POST
  @Path("/flight-setting")
  @Operation(summary = "Create flight setting")
  public ApiResponse<Void> createFilghtSetting(@Valid String content) {
    UserInfo userInfo = Vertx.currentContext().getLocal("user");
    this.createFilghtSettingHandler.run(userInfo, content);
    return ApiResponse.fromData(null);
  }

  @GET
  @Path("")
  @Operation(summary = "Get company")
  public ApiResponse<CompanyRes> getCompany() {
    UserInfo userInfo = Vertx.currentContext().getLocal("user");
    return ApiResponse.fromData(this.getHandler.run(userInfo));
  }
}
