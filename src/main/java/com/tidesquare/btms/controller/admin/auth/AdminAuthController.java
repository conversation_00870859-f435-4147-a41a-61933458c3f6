package com.tidesquare.btms.controller.admin.auth;

import org.eclipse.microprofile.openapi.annotations.Operation;

import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.controller.admin.auth.login.AdminLoginBody;
import com.tidesquare.btms.controller.admin.auth.login.AdminLoginHandler;
import com.tidesquare.btms.controller.admin.auth.login.AdminLoginRes;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;

@Path("admin/auth")
public class AdminAuthController {

    @Inject
    private AdminLoginHandler adminLoginHandler;

    @POST
    @Path("/login")
    @Operation(summary = "Login")
    public ApiResponse<AdminLoginRes> login(@Valid AdminLoginBody body) {
        return ApiResponse.fromData(this.adminLoginHandler.run(body));
    }
}
