package com.tidesquare.btms.controller.public_api.department.get_list_department;

import java.util.List;

import com.tidesquare.btms.entity.Department;
import com.tidesquare.btms.repository.DepartmentRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class GetListDepartmentHandler {

    @Inject
    private DepartmentRepo departmentRepo;

    public List<GetListDepartmentRes> run(Long workspaceId, Long parentDepartmentId) {
        List<Department> departments = parentDepartmentId == null ? this.departmentRepo.findByWorkspaceId(workspaceId) : this.departmentRepo.findByWorkspaceIdAndParentDepartmentId(workspaceId, parentDepartmentId);

        return departments.stream().map(department -> {
            GetListDepartmentRes res = new GetListDepartmentRes();
            res.setId(department.getId());
            res.setName(department.getName());
            return res;
        }).toList();
    }
}
