package com.tidesquare.btms.controller.public_api.company.get_by_domain;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.dto.response.HomepageSettingRes;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.Company_;
import com.tidesquare.btms.entity.HomepageSetting;
import com.tidesquare.btms.entity.HomepageSetting_;
import com.tidesquare.btms.entity.embeddable.BtmsSetting_;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Fetch;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Root;
import jakarta.ws.rs.core.Response.Status;

@ApplicationScoped
public class GetCompanyByDomainHandler {
    @Inject
    private StatelessSession statelessSession;

    public GetCompanyByDomainRes run(GetCompanyByDomainQuery query) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Company> q = builder.createQuery(Company.class);
        Root<Company> root = q.from(Company.class);
        Fetch<Company, HomepageSetting> homepageSettingFecth = root.fetch(Company_.homepageSetting, JoinType.LEFT);
        homepageSettingFecth.fetch(HomepageSetting_.gnbLogoAttachFile, JoinType.LEFT);
        homepageSettingFecth.fetch(HomepageSetting_.loginLogoAttachFile, JoinType.LEFT);
        q.select(root);

        if (query.getAdminDomain() != null) {
            q.where(builder.equal(root.get(Company_.btmsSetting).get(BtmsSetting_.adminUrl), query.getAdminDomain()));
        } else if (query.getDomain() != null) {
            q.where(builder.equal(root.get(Company_.btmsSetting).get(BtmsSetting_.url), query.getDomain()));
        } else {
            throw new ApiException(Status.NOT_FOUND, ErrorCode.E404);
        }

        Company company = this.statelessSession.createSelectionQuery(q).setMaxResults(1).getSingleResultOrNull();

        if (company == null || !company.getBtmsSetting().isUse()) {
            throw new ApiException(Status.NOT_FOUND, ErrorCode.E404);
        }

        GetCompanyByDomainRes res = new GetCompanyByDomainRes();
        res.setId(company.getId());
        res.setHomepageSetting(HomepageSettingRes.fromEntity(company.getHomepageSetting()));
        res.setGroup(company.isGroup());
        res.setName(company.getName());
        res.setEmailDomains(company.getBtmsSetting().getEmailDomainList());
        return res;
    }
}
