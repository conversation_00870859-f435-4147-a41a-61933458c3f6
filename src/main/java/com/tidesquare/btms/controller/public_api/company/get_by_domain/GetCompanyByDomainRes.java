package com.tidesquare.btms.controller.public_api.company.get_by_domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tidesquare.btms.dto.response.HomepageSettingRes;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class GetCompanyByDomainRes {
    private Long id;
    @JsonProperty("isGroup")
    private boolean isGroup;
    private HomepageSettingRes homepageSetting;
    private String name;
    private String[] emailDomains;
}
