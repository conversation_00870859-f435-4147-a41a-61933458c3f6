package com.tidesquare.btms.controller.public_api.department;

import java.util.List;

import org.eclipse.microprofile.openapi.annotations.Operation;

import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.controller.public_api.department.get_list_department.GetListDepartmentHandler;
import com.tidesquare.btms.controller.public_api.department.get_list_department.GetListDepartmentRes;

import jakarta.inject.Inject;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.QueryParam;

@Path("public/department")
public class PublicDepartmentController {

    @Inject
    private GetListDepartmentHandler getListDepartmentHandler;

    @GET
    @Path("")
    @Operation(summary = "Get List Department")
    public ApiResponse<List<GetListDepartmentRes>> getListDepartment(@NotNull @QueryParam("workspaceId") Long workspaceId, @QueryParam("parentDepartmentId") Long parentDepartmentId) {
        return ApiResponse.fromData(this.getListDepartmentHandler.run(workspaceId, parentDepartmentId));
    }
}
