package com.tidesquare.btms.controller.public_api.company.get_children;

import java.util.List;

import com.tidesquare.btms.repository.CompanyRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class GetGroupChildrenHandler {

    @Inject
    private CompanyRepo companyRepo;

    public List<GetGroupChildrenRes> run(Long companyId) {
        return this.companyRepo.findByParentId(companyId).stream().map(company -> {
            GetGroupChildrenRes res = new GetGroupChildrenRes();
            res.setId(company.getId());
            res.setName(company.getName());
            res.setEmailDomains(company.getBtmsSetting().getEmailDomainList());
            return res;
        }).toList();
    }
}
