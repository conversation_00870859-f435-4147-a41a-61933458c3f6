package com.tidesquare.btms.controller.public_api.company.get_by_site_code;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.Company_;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import jakarta.ws.rs.core.Response.Status;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class GetCompanyBySiteCodeHandler {

    @Inject
    private StatelessSession statelessSession;

    public GetCompanyBySiteCodeRes run(GetCompanyBySiteCodeQuery query) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Company> q = builder.createQuery(Company.class);
        Root<Company> root = q.from(Company.class);
        q.select(root);

        q.where(builder.equal(root.get(Company_.siteCode), query.getSiteCode()));

        Company company = this.statelessSession.createSelectionQuery(q).setMaxResults(1).getSingleResultOrNull();
        if (company == null || !company.getBtmsSetting().isUse()) {
            throw new ApiException(Status.NOT_FOUND, ErrorCode.E404);
        }

        GetCompanyBySiteCodeRes res = new GetCompanyBySiteCodeRes();
        res.setId(company.getId());
        res.setName(company.getName());
        res.setEmailDomains(company.getBtmsSetting().getEmailDomainList());
        return res;
    }
}
