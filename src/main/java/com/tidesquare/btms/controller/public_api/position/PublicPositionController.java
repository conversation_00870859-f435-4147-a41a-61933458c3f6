package com.tidesquare.btms.controller.public_api.position;

import java.util.List;

import org.eclipse.microprofile.openapi.annotations.Operation;

import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.controller.public_api.position.get_list_position.GetListPositionHandler;
import com.tidesquare.btms.controller.public_api.position.get_list_position.GetListPositionRes;

import jakarta.inject.Inject;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.QueryParam;

@Path("public/position")
public class PublicPositionController {

    @Inject
    private GetListPositionHandler getListPositionHandler;

    @GET
    @Path("")
    @Operation(summary = "Get List Position")
    public ApiResponse<List<GetListPositionRes>> getListPosition(@NotNull @QueryParam("companyId") Long companyId) {
        return ApiResponse.fromData(this.getListPositionHandler.run(companyId));
    }
}
