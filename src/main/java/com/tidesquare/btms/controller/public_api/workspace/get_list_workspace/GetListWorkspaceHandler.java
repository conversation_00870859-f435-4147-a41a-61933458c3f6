package com.tidesquare.btms.controller.public_api.workspace.get_list_workspace;

import java.util.List;

import com.tidesquare.btms.entity.Workspace;
import com.tidesquare.btms.repository.WorkspaceRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class GetListWorkspaceHandler {

    @Inject
    private WorkspaceRepo workspaceRepo;

    public List<GetListWorkspaceRes> run(Long companyId) {
        List<Workspace> workspaces = this.workspaceRepo.findByCompanyIds(List.of(companyId));

        return workspaces.stream().map(workspace -> {
            GetListWorkspaceRes res = new GetListWorkspaceRes();
            res.setId(workspace.getId());
            res.setName(workspace.getName());
            return res;
        }).toList();
    }
}
