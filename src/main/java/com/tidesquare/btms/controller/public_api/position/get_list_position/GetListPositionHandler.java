package com.tidesquare.btms.controller.public_api.position.get_list_position;

import java.util.List;

import com.tidesquare.btms.entity.Position;
import com.tidesquare.btms.repository.PositionRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class GetListPositionHandler {

    @Inject
    private PositionRepo positionRepo;

    public List<GetListPositionRes> run(Long companyId) {
        List<Position> positions = this.positionRepo.findByCompanyId(companyId);

        return positions.stream().map(position -> {
            GetListPositionRes res = new GetListPositionRes();
            res.setId(position.getId());
            res.setName(position.getName());
            return res;
        }).toList();
    }
}
