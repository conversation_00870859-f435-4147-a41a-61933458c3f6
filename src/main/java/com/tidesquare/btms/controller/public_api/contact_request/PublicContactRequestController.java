package com.tidesquare.btms.controller.public_api.contact_request;

import com.tidesquare.btms.controller.public_api.contact_request.create.CreateContactRequestBody;
import com.tidesquare.btms.controller.public_api.contact_request.create.CreateContactRequestHandler;

import org.eclipse.microprofile.openapi.annotations.Operation;

import com.tidesquare.btms.common.ApiResponse;

import jakarta.inject.Inject;
import jakarta.validation.Valid;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;

@Path("public/contact-request")
public class PublicContactRequestController {

    @Inject
    private CreateContactRequestHandler createContactRequestHandler;

    @POST
    @Path("")
    @Operation(summary = "Submit a contact request")
    public ApiResponse<Void> createContactRequest(@Valid CreateContactRequestBody body) {
        this.createContactRequestHandler.run(body);
        return ApiResponse.fromData(null);
    }
}
