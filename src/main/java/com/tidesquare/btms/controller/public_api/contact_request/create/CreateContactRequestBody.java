package com.tidesquare.btms.controller.public_api.contact_request.create;

import com.tidesquare.btms.constant.ContactRequestType;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CreateContactRequestBody {
    @NotNull
    private ContactRequestType requestType;

    @NotBlank
    @Size(max = 100)
    private String name;

    @NotBlank
    @Size(max = 200)
    private String businessName;

    @NotBlank
    @Size(max = 40)
    private String email;

    @NotBlank
    @Size(max = 12)
    private String phoneNumber;

    @NotBlank
    @Size(max = 2000)
    private String message;
}
