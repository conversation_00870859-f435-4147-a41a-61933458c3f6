package com.tidesquare.btms.controller.public_api.company.get_travel_rule_air;

import com.tidesquare.btms.constant.TravelRuleType;
import com.tidesquare.btms.dto.response.TravelRuleAirRes;
import com.tidesquare.btms.entity.Workspace;
import com.tidesquare.btms.repository.TravelRuleRepo;
import com.tidesquare.btms.repository.WorkspaceRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class GetTravelRuleAirHandler {

    @Inject
    private WorkspaceRepo workspaceRepo;

    @Inject
    private TravelRuleRepo travelRuleRepo;

    public TravelRuleAirRes run(Long companyId) {
        Workspace workspace = this.workspaceRepo.findCreatedFirstByCompanyId(companyId);
        this.travelRuleRepo.findByWorkspaceIdAndTravelRuleType(workspace.getId(), TravelRuleType.OVERSEA_AIR);
        return null;
    }
}
