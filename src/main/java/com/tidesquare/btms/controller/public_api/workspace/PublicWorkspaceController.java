package com.tidesquare.btms.controller.public_api.workspace;

import java.util.List;

import org.eclipse.microprofile.openapi.annotations.Operation;

import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.controller.public_api.workspace.get_list_workspace.GetListWorkspaceHandler;
import com.tidesquare.btms.controller.public_api.workspace.get_list_workspace.GetListWorkspaceRes;

import jakarta.inject.Inject;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.QueryParam;

@Path("public/workspace")
public class PublicWorkspaceController {

    @Inject
    private GetListWorkspaceHandler getListWorkspaceHandler;

    @GET
    @Path("")
    @Operation(summary = "Get List Workspace")
    public ApiResponse<List<GetListWorkspaceRes>> getListWorkspace(@NotNull @QueryParam("companyId") Long companyId) {
        return ApiResponse.fromData(this.getListWorkspaceHandler.run(companyId));
    }
}
