package com.tidesquare.btms.controller.public_api.contact_request.create;

import com.tidesquare.btms.entity.ContactRequest;
import com.tidesquare.btms.repository.ContactRequestRepo;
import com.tidesquare.btms.service.email.EmailService;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class CreateContactRequestHandler {

    @Inject
    private ContactRequestRepo contactRequestRepo;

    @Inject
    private EmailService emailService;

    @Transactional(rollbackOn = Exception.class)
    public void run(CreateContactRequestBody body) {
        ContactRequest contactRequest = new ContactRequest();
        contactRequest.setRequestType(body.getRequestType());
        contactRequest.setName(body.getName());
        contactRequest.setBusinessName(body.getBusinessName());
        contactRequest.setEmail(body.getEmail());
        contactRequest.setPhoneNumber(body.getPhoneNumber());
        contactRequest.setMessage(body.getMessage().replaceAll("\n", "<br/>"));

        this.contactRequestRepo.insert(contactRequest);

        Thread.startVirtualThread(() -> {
            this.emailService.sendContactRequestEmail(contactRequest);
        });
    }
}
