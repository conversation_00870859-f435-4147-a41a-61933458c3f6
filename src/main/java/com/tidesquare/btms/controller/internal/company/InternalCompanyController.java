package com.tidesquare.btms.controller.internal.company;

import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.controller.internal.company.sync_group_setting.SyncGroupSettingHandler;
import com.tidesquare.btms.filter.InternalBinding;

import jakarta.inject.Inject;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;

@Path("internal/company")
@InternalBinding
@SecurityRequirement(name = "apiKey")
public class InternalCompanyController {

    @Inject
    private SyncGroupSettingHandler syncGroupSettingHandler;

    @POST
    @Path("/{companyId}/sync-group-setting")
    @Operation(summary = "Sync Group Setting")
    public ApiResponse<Void> emegencyRegister(@PathParam("companyId") Long companyId) {
        this.syncGroupSettingHandler.run(companyId);
        return ApiResponse.fromData(null);
    }
}
