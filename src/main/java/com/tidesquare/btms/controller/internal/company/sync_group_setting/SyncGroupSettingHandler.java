package com.tidesquare.btms.controller.internal.company.sync_group_setting;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tidesquare.btms.entity.BtmsManager;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.CompanyAccount;
import com.tidesquare.btms.entity.CompanyContactPoint;
import com.tidesquare.btms.entity.CompanyFare;
import com.tidesquare.btms.entity.CompanyMemo;
import com.tidesquare.btms.entity.Company_;
import com.tidesquare.btms.entity.HomepageSetting;
import com.tidesquare.btms.entity.RewardMileSetting;
import com.tidesquare.btms.entity.TravelRule;
import com.tidesquare.btms.entity.Workspace;
import com.tidesquare.btms.repository.BtmsManagerRepo;
import com.tidesquare.btms.repository.CompanyAccountRepo;
import com.tidesquare.btms.repository.CompanyContactPointRepo;
import com.tidesquare.btms.repository.CompanyFareRepo;
import com.tidesquare.btms.repository.CompanyMemoRepo;
import com.tidesquare.btms.repository.CompanyRepo;
import com.tidesquare.btms.repository.FlightSettingRepo;
import com.tidesquare.btms.repository.HomepageSettingRepo;
import com.tidesquare.btms.repository.RewardMileSettingRepo;
import com.tidesquare.btms.repository.TravelRuleRepo;
import com.tidesquare.btms.repository.WorkspaceRepo;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class SyncGroupSettingHandler {

    @Inject
    private CompanyRepo companyRepo;

    @Inject
    private CompanyMemoRepo companyMemoRepo;

    @Inject
    private CompanyFareRepo companyFareRepo;

    @Inject
    private CompanyContactPointRepo companyContactPointRepo;

    @Inject
    private BtmsManagerRepo btmsManagerRepo;

    @Inject
    private CompanyAccountRepo companyAccountRepo;

    @Inject
    private RewardMileSettingRepo rewardMileSettingRepo;

    @Inject
    private FlightSettingRepo flightSettingRepo;

    @Inject
    private HomepageSettingRepo homepageSettingRepo;

    @Inject
    private WorkspaceRepo workspaceRepo;

    @Inject
    private TravelRuleRepo travelRuleRepo;

    @Transactional(rollbackOn = Exception.class)
    public void run(Long parentId) {
        Company parent = this.companyRepo.findById(parentId);
        if (parent == null || !parent.isGroup()) {
            return;
        }

        List<Company> children = this.companyRepo.findByParentId(parentId);
        if (children.isEmpty()) {
            return;
        }
        List<Long> childrenIds = children.stream().map(e -> e.getId()).toList();

        List<Workspace> workspaces = this.workspaceRepo.findByCompanyIds(childrenIds);
        List<Long> workspaceIds = workspaces.stream().map(e -> e.getId()).toList();

        /* #region Company */
        Map<String, Object> updateValues = new HashMap<>();
        parent.getBtmsSetting().setUrl(null);
        parent.getBtmsSetting().setAdminUrl(null);
        updateValues.put(Company_.COMPANY_TYPE, parent.getCompanyType());
        updateValues.put(Company_.IS_SAMSUNG_PG, parent.isSamsungPG());
        updateValues.put(Company_.BUSINESS_NUM, parent.getBusinessNum());
        updateValues.put(Company_.REPRESENTATIVE_NAME, parent.getRepresentativeName());
        updateValues.put(Company_.COMPANY_TEL_NO, parent.getCompanyTelNo());
        updateValues.put(Company_.COMPANY_FAX_NO, parent.getCompanyFaxNo());
        updateValues.put(Company_.BTMS_SETTING, parent.getBtmsSetting());
        updateValues.put(Company_.AIR_EM_SETTING, parent.getAirEmSetting());
        updateValues.put(Company_.IS_EXCLUDE_AIR_USE, parent.isExcludeAirUse());
        updateValues.put(Company_.ECONOMY_AIR_COM_RATE, parent.getEconomyAirComRate());
        updateValues.put(Company_.BUSINESS_AIR_COM_RATE, parent.getBusinessAirComRate());
        updateValues.put(Company_.DOMESTIC_AIR_COM_RATE, parent.getDomesticAirComRate());
        updateValues.put(Company_.DOMESTIC_SALE_COM_RATE, parent.getDomesticSaleComRate());
        updateValues.put(Company_.DOMESTIC_SALE_COM_RATE_TYPE, parent.getDomesticSaleComRateType());
        updateValues.put(Company_.IS_MONTH_END_PAYMENT, parent.isMonthEndPayment());
        updateValues.put(Company_.IS_TAX_INVOICE_VAT, parent.isTaxInvoiceVat());
        updateValues.put(Company_.AIR_COM_MATH_TYPE, parent.getAirComMathType());
        updateValues.put(Company_.COM_CUTTING_UNIT, parent.getComCuttingUnit());
        updateValues.put(Company_.AIR_COM_VAT_MATH_TYPE, parent.getAirComVatMathType());
        updateValues.put(Company_.COM_VAT_CUTTING_UNIT, parent.getComVatCuttingUnit());
        updateValues.put(Company_.COMMISSION_CALCULATION_TYPE, parent.getCommissionCalculationType());
        updateValues.put(Company_.IS_DELETED, parent.isDeleted());
        updateValues.put(Company_.ADDRESS, parent.getAddress());
        updateValues.put(Company_.IS_AGENCY, parent.isAgency());
        updateValues.put(Company_.AIR_YEARLY_VOLUME, parent.getAirYearlyVolume());
        updateValues.put(Company_.HOTEL_YEARLY_VOLUME, parent.getHotelYearlyVolume());
        updateValues.put(Company_.COMPANY_TRAVEL_INFO, parent.getCompanyTravelInfo());
        updateValues.put(Company_.COMPANY_BUSINESS_INFO, parent.getCompanyBusinessInfo());
        updateValues.put(Company_.INVOICE_FORM_ID, parent.getInvoiceFormId());
        updateValues.put(Company_.INVOICE_UNIT_TEMPLATE_ID, parent.getInvoiceUnitTemplateId());
        updateValues.put(Company_.INVOICE_BUNDLE_TEMPLATE_ID, parent.getInvoiceBundleTemplateId());
        updateValues.put(Company_.REFUND_INVOICE_UNIT_TEMPLATE_ID, parent.getRefundInvoiceUnitTemplateId());
        updateValues.put(Company_.REFUND_INVOICE_BUNDLE_TEMPLATE_ID, parent.getRefundInvoiceBundleTemplateId());
        updateValues.put(Company_.HOTEL_INVOICE_UNIT_TEMPLATE_ID, parent.getHotelInvoiceUnitTemplateId());
        updateValues.put(Company_.HOTEL_INVOICE_BUNDLE_TEMPLATE_ID, parent.getHotelInvoiceBundleTemplateId());
        updateValues.put(Company_.IS_CUSTOM, parent.isCustom());
        this.companyRepo.update(childrenIds, updateValues);
        //#endregion

        //#region CompanyMemo 
        this.companyMemoRepo.deleteAllByCompanyIds(childrenIds);
        List<CompanyMemo> companyMemos = this.companyMemoRepo.findByCompanyId(parentId);
        for (Long childId : childrenIds) {
            for (CompanyMemo companyMemo : companyMemos) {
                CompanyMemo companyMemoToInsert = new CompanyMemo();
                companyMemoToInsert.setCompanyId(childId);
                companyMemoToInsert.setMeetingDate(companyMemo.getMeetingDate());
                companyMemoToInsert.setMemo(companyMemo.getMemo());
                this.companyMemoRepo.insert(companyMemoToInsert);
            }
        }
        //#endregion

        //#region CompanyFare 
        this.companyFareRepo.deleteAllByCompanyIds(childrenIds);
        List<CompanyFare> companyFares = this.companyFareRepo.findByCompanyId(parentId);
        for (Long childId : childrenIds) {
            for (CompanyFare companyFare : companyFares) {
                CompanyFare companyFareToInsert = new CompanyFare();
                companyFareToInsert.setCompanyId(childId);
                companyFareToInsert.setAirlineId(companyFare.getAirlineId());
                companyFareToInsert.setCode(companyFare.getCode());
                companyFareToInsert.setGdsType(companyFare.getGdsType());
                this.companyFareRepo.insert(companyFareToInsert);
            }
        }
        //#endregion

        //#region CompanyContactPoint 
        this.companyContactPointRepo.deleteAllByCompanyIds(childrenIds);
        List<CompanyContactPoint> companyContactPoints = this.companyContactPointRepo.findByCompanyId(parentId);
        for (Long childId : childrenIds) {
            for (CompanyContactPoint companyContactPoint : companyContactPoints) {
                CompanyContactPoint companyContactPointToInsert = new CompanyContactPoint();
                companyContactPointToInsert.setCompanyId(childId);
                companyContactPointToInsert.setManagerCellPhoneNumber(companyContactPoint.getManagerCellPhoneNumber());
                companyContactPointToInsert.setManagerDeptName(companyContactPoint.getManagerDeptName());
                companyContactPointToInsert.setManagerEmail(companyContactPoint.getManagerEmail());
                companyContactPointToInsert.setManagerName(companyContactPoint.getManagerName());
                companyContactPointToInsert.setManagerPhoneNumber(companyContactPoint.getManagerPhoneNumber());
                companyContactPointToInsert.setManagerPositionName(companyContactPoint.getManagerPositionName());
                this.companyContactPointRepo.insert(companyContactPointToInsert);
            }
        }
        //#endregion

        //#region BtmsManager 
        this.btmsManagerRepo.deleteAllByCompanyIds(childrenIds);
        List<BtmsManager> btmsManagers = this.btmsManagerRepo.findByCompanyId(parentId);
        for (Long childId : childrenIds) {
            for (BtmsManager btmsManager : btmsManagers) {
                BtmsManager btmsManagerToInsert = new BtmsManager();
                btmsManagerToInsert.setCompanyId(childId);
                btmsManagerToInsert.setDisplayOrder(btmsManager.getDisplayOrder());
                btmsManagerToInsert.setIsOverseas(btmsManager.getIsOverseas());
                btmsManagerToInsert.setManagerType(btmsManager.getManagerType());
                btmsManagerToInsert.setTravelAgencyUser(btmsManager.getTravelAgencyUser());
                this.btmsManagerRepo.insert(btmsManagerToInsert);
            }
        }
        //#endregion

        //#region CompanyAccount 
        this.companyAccountRepo.deleteAllByCompanyIds(childrenIds);
        List<CompanyAccount> companyAccounts = this.companyAccountRepo.findByCompanyId(parentId);
        for (Long childId : childrenIds) {
            for (CompanyAccount companyAccount : companyAccounts) {
                CompanyAccount companyAccountToInsert = new CompanyAccount();
                companyAccountToInsert.setCompanyId(childId);
                companyAccountToInsert.setCreator(companyAccount.getCreator());
                companyAccountToInsert.setCustomType(companyAccount.getCustomType());
                companyAccountToInsert.setFBankAccnt(companyAccount.getFBankAccnt());
                companyAccountToInsert.setFBankCd(companyAccount.getFBankCd());
                companyAccountToInsert.setFBankDepositors(companyAccount.getFBankDepositors());
                companyAccountToInsert.setFBankNm(companyAccount.getFBankNm());
                companyAccountToInsert.setSBankAccnt(companyAccount.getSBankAccnt());
                companyAccountToInsert.setSBankCd(companyAccount.getSBankCd());
                companyAccountToInsert.setSDepositors(companyAccount.getSDepositors());
                companyAccountToInsert.setSBankNm(companyAccount.getSBankNm());
                this.companyAccountRepo.insert(companyAccountToInsert);
            }
        }
        //#endregion

        //#region RewardMileSetting 
        RewardMileSetting rewardMileSetting = this.rewardMileSettingRepo.get(parentId);
        for (Long childId : childrenIds) {
            RewardMileSetting rewardMileSettingToInsert = new RewardMileSetting();
            rewardMileSettingToInsert.setCompanyId(childId);
            rewardMileSettingToInsert.setIsUse(rewardMileSetting.getIsUse());
            rewardMileSettingToInsert.setMaximumAccumulatedMiles(rewardMileSetting.getMaximumAccumulatedMiles());
            rewardMileSettingToInsert.setRatio(rewardMileSetting.getRatio());
            rewardMileSettingToInsert.setRewardMileType(rewardMileSetting.getRewardMileType());
            this.rewardMileSettingRepo.insertOrUpdate(rewardMileSettingToInsert);
        }
        //#endregion

        //#region FlightSetting 
        String flightSettingContent = this.flightSettingRepo.findByCompanyId(parentId);
        for (Long childId : childrenIds) {
            this.flightSettingRepo.insertOrUpdate(childId, flightSettingContent);
        }
        //#endregion

        //#region HomepageSetting
        HomepageSetting homepageSetting = this.homepageSettingRepo.findByCompanyId(parentId);
        for (Long childId : childrenIds) {
            HomepageSetting homepageSettingToInsert = new HomepageSetting();
            homepageSettingToInsert.setCompanyId(childId);
            homepageSettingToInsert.setUseDefaultLogo(homepageSetting.isUseDefaultLogo());
            homepageSettingToInsert.setLoginLogoAttachFile(homepageSetting.getLoginLogoAttachFile());
            homepageSettingToInsert.setGnbLogoAttachFile(homepageSetting.getGnbLogoAttachFile());
            this.homepageSettingRepo.insertOrUpdate(homepageSettingToInsert);
        }
        /* #endregion */

        /* #region TravelRule */
        List<TravelRule> travelRules = this.travelRuleRepo.findByWorkspaceIds(workspaceIds);
        if (!travelRules.isEmpty()) {
            List<Long> travelRuleIds = travelRules.stream().map(e -> e.getTravelRuleId()).toList();
            this.travelRuleRepo.deleteByTravelRuleIds(travelRuleIds);
        }
        /* #endregion */
    }
}
