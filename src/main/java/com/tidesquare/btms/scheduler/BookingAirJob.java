package com.tidesquare.btms.scheduler;

import java.util.List;
import java.util.concurrent.CountDownLatch;

import com.tidesquare.btms.constant.GDSType;
import com.tidesquare.btms.constant.PnrDataHistoryType;
import com.tidesquare.btms.service.stella.StellaService;
import com.tidesquare.btms.service.stella.dto.response.PNRInfoRes;
import com.tidesquare.btms.service.travel.TravelService;

import io.quarkus.runtime.ShutdownEvent;
import io.quarkus.scheduler.Scheduled;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.event.Observes;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@Slf4j(topic = "BookingAirJob")
@ApplicationScoped
public class BookingAirJob {

    @Inject
    private TravelService travelService;

    @Inject
    private StellaService stellaService;

    private CountDownLatch latch;

    // @Scheduled(every = "5m") // To test faster no need wait 1 minute
    @Scheduled(every = "5m", delayed = "1m")
    void run() {
        log.info("Start BookingAirJob");
        if (latch != null) {
            log.info("Previous job is running");
            return;
        }
        latch = new CountDownLatch(2);

        Thread.startVirtualThread(() -> {
            try {
                this.run(GDSType.AMADEUS);
            } catch (Exception e) {
                log.error(e.getMessage());
            } finally {
                latch.countDown();
            }
        });

        Thread.startVirtualThread(() -> {
            try {
                this.run(GDSType.SABRE);
            } catch (Exception e) {
                log.error(e.getMessage());
            } finally {
                latch.countDown();
            }
        });

        try {
            latch.await(); // Đợi cho đến khi cả hai thread hoàn thành
        } catch (Exception e) {
            log.error("Error latch.await() " + e.getMessage());
        } finally {
            latch = null;
        }

        log.info("Finish BookingAirJob");
    }

    void onStop(@Observes ShutdownEvent ev) {
        if (latch == null) {
            return;
        }
        try {
            latch.await();
        } catch (Exception e) {
            log.error("Error latch.await() " + e.getMessage());
        }
    }

    private void run(GDSType gdsType) {
        log.info("Start BookingAirJob with " + gdsType.name());
        List<PNRInfoRes> pnrInfos = this.stellaService.GetOfflinePNRs(gdsType);
        if (pnrInfos.isEmpty()) {
            log.info("queue " + gdsType.name() + " is empty");
            return;
        }

        log.info("queue " + gdsType.name() + " has " + pnrInfos.size() + " items");
        for (PNRInfoRes pnrInfo : pnrInfos) {
            try {
                this.travelService.savePnrInfo(null, pnrInfo, PnrDataHistoryType.Q);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        }
        log.info("Finish BookingAirJob with " + gdsType.name());
    }
}
