package com.tidesquare.btms.scheduler;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.tidesquare.btms.constant.CancelBookingIndexType;
import com.tidesquare.btms.entity.BookingAir;
import com.tidesquare.btms.entity.BookingAirTraveler;
import com.tidesquare.btms.entity.CancelBookingIndex;
import com.tidesquare.btms.repository.BookingAirRepo;
import com.tidesquare.btms.repository.BookingAirTravelerRepo;
import com.tidesquare.btms.repository.CancelBookingIndexRepo;
import com.tidesquare.btms.repository.TravelerMileageInfoRepo;

import io.quarkus.scheduler.Scheduled;
import io.quarkus.scheduler.Scheduled.ConcurrentExecution;
import io.smallrye.common.annotation.RunOnVirtualThread;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@Slf4j(topic = "CancelBookingAirPersonInfoJob")
@ApplicationScoped
public class CancelBookingAirPersonInfoJob {
  @Inject
  private CancelBookingIndexRepo cancelBookingIndexRepo;

  @Inject
  private BookingAirRepo bookingAirRepo;

  @Inject
  private BookingAirTravelerRepo bookingAirTravelerRepo;

  @Inject
  private TravelerMileageInfoRepo travelerMileageInfoRepo;

  @Scheduled(cron = "0 0 8 * * ?", concurrentExecution = ConcurrentExecution.SKIP)
  @RunOnVirtualThread
  void run() {
    log.info("Start CancelBookingAirPersonInfoJob");
    Long lastId = this.cancelBookingIndexRepo.getIndexValue(CancelBookingIndexType.BOOKING_AIR);
    this.run(lastId);
    log.info("End CancelBookingAirPersonInfoJob");
  }

  void run(Long startId) {
    final int LIMIT = 50;
    if (startId == null) {
      startId = 0L;
    }

    Long lastId = startId;

    while (true) {
      List<BookingAir> bookingAirs = this.bookingAirRepo.getBookingAirsWithLimitLessThanFiveYears(lastId, LIMIT);
      if (bookingAirs.isEmpty()) {
        return;
      }

      List<BookingAirTraveler> bookingAirTravelers = this.bookingAirTravelerRepo
          .findByBookingAirIds(bookingAirs.stream().map(BookingAir::getId).collect(Collectors.toList()));
      List<Long> bookingAirTravelerIds = bookingAirTravelers.stream().map(BookingAirTraveler::getId)
          .collect(Collectors.toList());

      Map<String, Object> updateValue = new HashMap<>();
      updateValue.put("passportNumber", null); // passport number
      updateValue.put("passportLimitDate", null); // passport limit date
      updateValue.put("passportNation", null); // passport nation
      updateValue.put("nationalityCode", null); // nationality code

      this.bookingAirTravelerRepo.update(bookingAirTravelerIds, updateValue); // update traveler info
      this.travelerMileageInfoRepo.deleteByBookingAirTravelerIds(bookingAirTravelerIds);// delete traveler mileage info
      lastId = bookingAirs.get(bookingAirs.size() - 1).getId();
      this.cancelBookingIndexRepo.insertOrUpdate(
          CancelBookingIndex.builder()
              .indexValue(lastId)
              .type(CancelBookingIndexType.BOOKING_AIR)
              .build());
      if (bookingAirs.size() < LIMIT) {
        break;
      }
    }
  }
}
