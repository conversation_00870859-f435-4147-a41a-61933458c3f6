package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.constant.GDSType;
import com.tidesquare.btms.constant.InflowBookingType;
import com.tidesquare.btms.constant.SectionType;
import com.tidesquare.btms.entity.BookingAir;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.ArrayList;

@Builder
@Getter
@Setter
public class BookingAirRes {
    private Long id;

    private InflowBookingType inflowBookingType;

    private TravelRes travel;

    private SectionType sectionType;

    private Long bookingDate;

    private String pnrNo;

    private String otherPnrNo;

    private Long ticketDate;

    private Long viewTicketDate;

    private GDSType gdsType;

    private List<BookingAirTravelerRes> bookingAirTravelers;

    private List<BookingAirScheduleRes> bookingAirSchedules;

    private BookingAirLowestRes bookingAirLowest;

    private Long createDate;

    private Long modifyDate;

    private String ticketRequest;

    private Boolean isApisUpdateFailed;

    private Boolean isDomesticAutomaticTicket;

    private Boolean viewTicketDateModified;

    /* start thêm các total result */
    double totalAmount;

    double fareAmount;

    double taxAmount;

    double fuelSurcharge;

    double commissionAmount;

    double dcAmount;

    long adultCount;
    /* end */

    String orderID;

    String orderKey;

    public static BookingAirRes fromEntity(BookingAir entity) {
        if (entity == null)
            return null;
        return BookingAirRes.builder().inflowBookingType(entity.getInflowBookingType())
                .id(entity.getId())
                .sectionType(entity.getSectionType())
                .pnrNo(entity.getPnrNo())
                .otherPnrNo(entity.getOtherPnrNo())
                .ticketDate(Optional.ofNullable(entity.getTicketDate()).map(Date::getTime).orElse(null))
                .viewTicketDate(Optional.ofNullable(entity.getViewTicketDate()).map(Date::getTime).orElse(null))
                .gdsType(entity.getGdsType())
                .ticketRequest(entity.getTicketRequest())
                .isApisUpdateFailed(entity.getIsApisUpdateFailed())
                .isDomesticAutomaticTicket(entity.getIsDomesticAutomaticTicket())
                .orderKey(entity.getOrderKey())
                .orderID(entity.getOrderID())
                .bookingAirTravelers((entity.getBookingAirTravelers() != null) ? BookingAirTravelerRes.fromEntities(new ArrayList<>(entity.getBookingAirTravelers())) : null)
                .bookingAirSchedules((entity.getBookingAirSchedules() != null) ? BookingAirScheduleRes.fromEntities(new ArrayList<>(entity.getBookingAirSchedules())) : null)
                .bookingAirLowest(BookingAirLowestRes.fromEntity(entity.getBookingAirLowest()))
                .bookingDate(Optional.ofNullable(entity.getBookingDate()).map(Date::getTime).orElse(null))
                .createDate(Optional.ofNullable(entity.getCreateDate()).map(Date::getTime).orElse(null))
                .modifyDate(Optional.ofNullable(entity.getModifyDate()).map(Date::getTime).orElse(null))
                .viewTicketDateModified(entity.getViewTicketDateModified())
                .build();
    }
}
