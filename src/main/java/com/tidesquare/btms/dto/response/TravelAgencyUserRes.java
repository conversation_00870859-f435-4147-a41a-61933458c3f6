package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.entity.TravelAgencyUser;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
public class TravelAgencyUserRes extends CustomerRes {
    public static TravelAgencyUserRes fromEntity(TravelAgencyUser entity) {
        if (entity == null) {
            return null;
        }

        return TravelAgencyUserRes.builder()
                .id(entity.getId())
                .loginId(entity.getLoginId())
                .role(entity.getRole())
                .name(entity.getName())
                .gender(entity.getGender())
                .cellPhoneNumber(entity.getCellPhoneNumber())
                .accountingCode(entity.getAccountingCode())
                .birthday(entity.getBirthday())
                .phoneNumber(entity.getPhoneNumber())
                .employeeNo(entity.getEmployeeNo())
                .email(entity.getEmail())
                .joinAuthType(entity.getJoinAuthType())
                .joinDate(entity.getJoinDate())
                .joinStatus(entity.getJoinStatus())
                .leaveDate(entity.getLeaveDate())
                .leaveProcessUserId(entity.getLeaveProcessUserId())
                .incorrectPasswordCount(entity.getIncorrectPasswordCount())
                .passwordUpdateDate(entity.getPasswordUpdateDate())
                .nextPasswordUpdateDate(entity.getNextPasswordUpdateDate())
                .accountingCode(entity.getAccountingCode())
                .skypassMemberLastNm(entity.getSkypassMemberLastNm())
                .skypassMemberNo(entity.getSkypassMemberNo())
                .skypassUse(entity.getSkypassUse())
                .workspace(WorkspaceRes.fromEntity(entity.getWorkspace()))
                .department(DepartmentRes.fromEntity(entity.getDepartment()))
                .build();
    }

}
