package com.tidesquare.btms.dto.response;

import java.util.List;

import com.tidesquare.btms.constant.BookingType;
import com.tidesquare.btms.entity.DocumentNumber;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
@RegisterForReflection
public class DocumentNumberRes {
    private Long id;

    private String documentNo;

    private Long creatorUserId;

    private Long orderNo;

    private BookingType bookingType;

    private Long bookingId;

    public static DocumentNumberRes fromEntity(DocumentNumber entity) {
        if (entity == null) {
            return null;
        }
        return DocumentNumberRes.builder()
                .id(entity.getId())
                .documentNo(entity.getDocumentNo())
                .creatorUserId(entity.getCreatorUserId())
                .orderNo(entity.getOrderNo())
                .bookingType(entity.getBookingType())
                .bookingId(entity.getBookingId())
                .build();
    }

    public static List<DocumentNumberRes> fromEntities(List<DocumentNumber> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(e -> fromEntity(e))
                .toList();
    }
}
