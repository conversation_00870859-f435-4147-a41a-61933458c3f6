package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.constant.BookingAirPaymentStatus;
import com.tidesquare.btms.constant.BookingAirTicketStatus;
import com.tidesquare.btms.constant.TravelBookingType;
import com.tidesquare.btms.constant.TravelStatus;
import com.tidesquare.btms.entity.Travel;
import com.tidesquare.btms.entity.Traveler;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Getter
@Setter
@Builder
public class TravelRes {
    private Long id;

    private TravelBookingType travelBookingType;

    private Long workspaceId;

    private Boolean isOverseas;

    private int travelPersonnel;

    private String departYmd;

    private String returnYmd;

    private TravelStatus status;

    private Long modifyDate;

    private Long createDate;

    private String addRequest;

    private String seatZone;

    private String violationReason;

    private String overseasDocumentNo;

    private String approvalMemo;

    private List<CityRes> travelCities;

    private Traveler reserver;

    private CompanyRes company;

    private Long cancelDate;

    private BookingAirRes bookingAir; // 국제선항공예약

    private Long businessTripId;

    private Boolean isApproval;

    private Boolean isGroup; // 단체여부

    private String groupNumber; // 단체번호

    private Boolean isDeleted;

    private Long parentTravelId; // 국내선 가는편 출장아이디

    private String travelPlace; // 출장지

    private BookingAirPaymentStatus paymentStatus; // 결제상태

    private BookingAirTicketStatus ticketStatus;

    private List<DocumentNumberRes> documentNumbers;

    public static TravelRes fromEntity(Travel entity) {
        if (entity == null) {
            return null;
        }

        return TravelRes.builder().travelBookingType(entity.getTravelBookingType())
                .id(entity.getId())
                .isGroup(entity.getIsGroup())
                .seatZone(entity.getSeatZone())
                .departYmd(entity.getDepartYmd())
                .returnYmd(entity.getReturnYmd())
                .isDeleted(entity.getIsDeleted())
                .isOverseas(entity.getIsOverseas())
                .isApproval(entity.getIsApproval())
                .addRequest(entity.getAddRequest())
                .workspaceId(entity.getWorkspaceId())
                .groupNumber(entity.getGroupNumber())
                .travelPlace(entity.getTravelPlace())
                .approvalMemo(entity.getApprovalMemo())
                .ticketStatus(entity.getTicketStatus())
                .status(entity.getStatus())
                .paymentStatus(entity.getPaymentStatus())
                .businessTripId(entity.getBusinessTripId())
                .parentTravelId(entity.getParentTravelId())
                .violationReason(entity.getViolationReason())
                .travelPersonnel(entity.getTravelPersonnel())
                .overseasDocumentNo(entity.getOverseasDocumentNo())
                .modifyDate(Optional.ofNullable(entity.getModifyDate()).map(Date::getTime).orElse(null))
                .createDate(Optional.ofNullable(entity.getCreateDate()).map(Date::getTime).orElse(null))
                .bookingAir(BookingAirRes.fromEntity(entity.getBookingAir()))
                .cancelDate(Optional.ofNullable(entity.getCancelDate()).map(Date::getTime).orElse(null))
                .build();
    }

    public static List<TravelRes> fromEntities(List<Travel> entities) {
        if (entities == null) {
            return null;
        }

        return entities.stream().map(TravelRes::fromEntity).toList();
    }
}
