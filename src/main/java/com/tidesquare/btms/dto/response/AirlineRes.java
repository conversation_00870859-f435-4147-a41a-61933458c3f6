package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.entity.Airline;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class AirlineRes {
    private Long id;
    private String code;
    private String codeNo;
    private String name;
    private String nameEng;
    private String note;
    private String alliance;
    private Boolean isUse;
    private Boolean isCommercial;
    private Boolean isCorporation;

    public static AirlineRes fromEntity(Airline entity) {
        if (entity == null) {
            return null;
        }
        return AirlineRes.builder()
                .id(entity.getId())
                .code(entity.getCode())
                .codeNo(entity.getCodeNo())
                .name(entity.getName())
                .nameEng(entity.getNameEng())
                .note(entity.getNote())
                .alliance(entity.getAlliance())
                .isUse(entity.getIsUse())
                .isCommercial(entity.getIsCommercial())
                .isCorporation(entity.getIsCorporation())
                .build();
    }
}
