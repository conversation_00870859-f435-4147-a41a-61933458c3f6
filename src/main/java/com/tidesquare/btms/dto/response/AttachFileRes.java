package com.tidesquare.btms.dto.response;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import com.tidesquare.btms.constant.AttachFileType;
import com.tidesquare.btms.entity.AttachFile;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class AttachFileRes {
    private Long id;

    private AttachFileType attachFileType;

    private String fileUploadPath;

    private String originFileName;

    private String tempFileName;

    private Long fileSize;

    private Long createDate;

    public static AttachFileRes fromEntity(AttachFile entity) {
        if (entity == null) {
            return null;
        }

        return AttachFileRes.builder()
                .id(entity.getId())
                .attachFileType(entity.getAttachFileType())
                .fileUploadPath(entity.getFileUploadPath())
                .originFileName(entity.getOriginFileName())
                .tempFileName(entity.getTempFileName())
                .fileSize(entity.getFileSize())
                .createDate(Optional.ofNullable(entity.getCreateDate()).map(Date::getTime).orElse(null))
                .build();
    }

    public static List<AttachFileRes> fromEntities(List<AttachFile> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(AttachFileRes::fromEntity)
                .toList();
    }
}
