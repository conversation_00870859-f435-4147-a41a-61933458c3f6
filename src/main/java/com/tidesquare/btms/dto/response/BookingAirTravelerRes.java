package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.constant.Gender;
import com.tidesquare.btms.entity.BookingAirTraveler;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.ArrayList;

@Builder
@Getter
@Setter
public class BookingAirTravelerRes {
    private Long id;

    private String travelerName;

    private String travelerLastName;

    private String travelerFirstName;

    private String lastName;

    private String firstName;

    private Double reserveAmount;

    private Double tax;

    private Double airportTax;

    private Double fuelSurcharge;

    private Double dcAmount;

    private Double commissionAmount;

    private Double fareAmount;

    private Gender gender;

    private String birthday;

    private String email;

    private String cellPhoneNumber;

    private String nationalityCode; // 국적코드

    private String passportNation; // 여권발행국

    private String passportLimitDate; // 여권만료일자

    private String passportNumber; // 여권번호

    private String discountCode; // 국내선신분할인코드

    private String discountName; // 국내선신분할인명

    private String settlementManagerEmail;

    private List<TravelerMileageInfoRes> travelerMileageInfos;

    public static BookingAirTravelerRes fromEntity(BookingAirTraveler entity) {
        if (entity == null)
            return null;
        return BookingAirTravelerRes.builder()
                .id(entity.getId())
                .travelerName(entity.getTravelerName())
                .travelerLastName(entity.getTravelerLastName())
                .travelerFirstName(entity.getTravelerFirstName())
                .lastName(entity.getLastName())
                .firstName(entity.getFirstName())
                .reserveAmount(entity.getReserveAmount())
                .tax(entity.getTax())
                .airportTax(entity.getAirportTax())
                .fuelSurcharge(entity.getFuelSurcharge())
                .dcAmount(entity.getDcAmount())
                .commissionAmount(entity.getCommissionAmount())
                .fareAmount(entity.getFareAmount())
                .gender(entity.getGender())
                .birthday(entity.getBirthday())
                .email(entity.getEmail())
                .cellPhoneNumber(entity.getCellPhoneNumber())
                .nationalityCode(entity.getNationalityCode())
                .passportNation(entity.getPassportNation())
                .passportLimitDate(entity.getPassportLimitDate())
                .passportNumber(entity.getPassportNumber())
                .discountCode(entity.getDiscountCode())
                .discountName(entity.getDiscountName())
                .settlementManagerEmail(entity.getSettlementManagerEmail())
                .travelerMileageInfos((entity.getTravelerMileageInfos() != null) ? TravelerMileageInfoRes.fromEntities(new ArrayList<>(entity.getTravelerMileageInfos())) : null)
                .build();
    }

    public static List<BookingAirTravelerRes> fromEntities(List<BookingAirTraveler> entities) {
        if (entities == null)
            return null;
        return entities.stream()
                .map(e -> fromEntity(e))
                .toList();
    }

}
