package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.constant.SectionType;
import com.tidesquare.btms.entity.BookingAirLowest;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Optional;

@Builder
@Getter
@Setter
public class BookingAirLowestRes {
    private Long id;

    private Long lastTicketDate;

    private Integer lastTicketDay;

    private Long totalAmount;

    private Long ticketAmount;

    private Long fareAmount;

    private Long taxAmount;

    private Long tasfAmount;

    private Boolean isCorporateFare;

    private Boolean isRuleViolation;

    private SectionType sectionType;

    private String representTravelIndex;

    private String baggageAllowance;

    private Long createDate;

    public static BookingAirLowestRes fromEntity(BookingAirLowest entity) {
        if (entity == null) {
            return null;
        }
        return BookingAirLowestRes.builder()
                .id(entity.getId())
                .createDate(Optional.ofNullable(entity.getCreateDate()).map(Date::getTime).orElse(null))
                .lastTicketDate(Optional.ofNullable(entity.getLastTicketDate()).map(Date::getTime).orElse(null))
                .totalAmount(entity.getTotalAmount())
                .ticketAmount(entity.getTicketAmount())
                .fareAmount(entity.getFareAmount())
                .ticketAmount(entity.getTicketAmount())
                .fareAmount(entity.getFareAmount())
                .taxAmount(entity.getTaxAmount())
                .tasfAmount(entity.getTasfAmount())
                .isCorporateFare(entity.getIsCorporateFare())
                .isRuleViolation(entity.getIsRuleViolation())
                .sectionType(entity.getSectionType())
                .representTravelIndex(entity.getRepresentTravelIndex())
                .baggageAllowance(entity.getBaggageAllowance())
                .build();
    }
}
