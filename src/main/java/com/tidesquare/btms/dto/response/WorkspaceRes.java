package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.entity.Workspace;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class WorkspaceRes {
    private Long id;

    private String name;

    private String phoneNumber;

    private CompanyRes company;

    private Boolean isUse;

    public static WorkspaceRes fromEntity(Workspace entity) {
        if (entity == null)
            return null;
        return WorkspaceRes.builder()
                .id(entity.getId())
                .name(entity.getName())
                .phoneNumber(entity.getPhoneNumber())
                .isUse(entity.getIsUse())
                .build();
    }
}
