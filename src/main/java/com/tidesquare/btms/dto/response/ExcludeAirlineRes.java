package com.tidesquare.btms.dto.response;

import java.util.List;

import com.tidesquare.btms.entity.ExcludeAirline;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class ExcludeAirlineRes {
    private AirlineRes airline;

    public static ExcludeAirlineRes fromEntity(ExcludeAirline entity) {
        if (entity == null) {
            return null;
        }

        return ExcludeAirlineRes.builder()
                .airline(AirlineRes.fromEntity(entity.getAirline()))
                .build();
    }

    public static List<ExcludeAirlineRes> fromEntities(List<ExcludeAirline> entities) {
        if (entities == null) {
            return null;
        }

        return entities.stream()
                .map(ExcludeAirlineRes::fromEntity)
                .toList();
    }
}
