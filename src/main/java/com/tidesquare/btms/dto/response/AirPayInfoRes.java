package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.entity.AirPayInfo;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Builder
@Getter
@Setter
public class AirPayInfoRes {
    private Long id;

    private Boolean isAmountView;

    private Boolean isIssueFeeView;

    private Boolean isTaxAndAmountView;

    private Boolean isTaxView;

    private Boolean isInvoiceView;

    private Boolean isETicketView;

    private Boolean isUse;

    private Long travelId;

    private Long bookingAirTicketId;

    private String ticketNo;

    private Boolean isAmadeusView;

    private Double cardAmount;

    private String airFareTypeValue;

    private Long createDate;

    private Long modifyDate;

    public static AirPayInfoRes fromEntity(AirPayInfo entity) {
        if (entity == null) {
            return null;
        }
        return AirPayInfoRes.builder()
                .id(entity.getId())
                .isAmountView(entity.getIsAmountView())
                .isIssueFeeView(entity.getIsIssueFeeView())
                .isTaxAndAmountView(entity.getIsTaxAndAmountView())
                .isTaxView(entity.getIsTaxView())
                .isInvoiceView(entity.getIsInvoiceView())
                .isETicketView(entity.getIsETicketView())
                .isUse(entity.getIsUse())
                .travelId(entity.getTravelId())
                .bookingAirTicketId(entity.getBookingAirTicketId())
                .ticketNo(entity.getTicketNo())
                .isAmadeusView(entity.getIsAmadeusView())
                .cardAmount(entity.getCardAmount())
                .airFareTypeValue(entity.getAirFareTypeValue())
                .createDate(Optional.ofNullable(entity.getCreateDate()).map(Date::getTime).orElse(null))
                .modifyDate(Optional.ofNullable(entity.getModifyDate()).map(Date::getTime).orElse(null))
                .build();
    }

    public static List<AirPayInfoRes> fromEntities(List<AirPayInfo> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(AirPayInfoRes::fromEntity)
                .toList();
    }
}
