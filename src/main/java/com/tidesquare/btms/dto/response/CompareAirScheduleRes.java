package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.constant.SectionType;
import com.tidesquare.btms.entity.CompareAirSchedule;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Builder
@Getter
@Setter
public class CompareAirScheduleRes {
    private Long id;

    private List<CompareScheduleDetailRes> compareScheduleDetails;

    private Boolean isMasterSchedule;

    private Long lastTicketDate;

    private Integer lastTicketDay;

    private Long totalAmount;

    private Long ticketAmount;

    private Long fareAmount;

    private Long taxAmount;

    private Long tasfAmount;

    private Long compareAirGroupId;

    private Boolean isCorporateFare;

    private Boolean isRuleViolation;

    private SectionType sectionType;

    private String representTravelIndex;

    private String baggageAllowance;

    private Long createDate;

    private long bookingAirId;

    public static CompareAirScheduleRes fromEntity(CompareAirSchedule entity) {
        if (entity == null) {
            return null;
        }

        return CompareAirScheduleRes.builder()
                .id(entity.getId())
                .isMasterSchedule(entity.getIsMasterSchedule())
                .lastTicketDate(Optional.ofNullable(entity.getLastTicketDate()).map(Date::getTime).orElse(null))
                .lastTicketDay(entity.getLastTicketDay())
                .totalAmount(entity.getTotalAmount())
                .ticketAmount(entity.getTicketAmount())
                .fareAmount(entity.getFareAmount())
                .taxAmount(entity.getTaxAmount())
                .tasfAmount(entity.getTasfAmount())
                .compareAirGroupId(entity.getCompareAirGroupId())
                .isCorporateFare(entity.getIsCorporateFare())
                .isRuleViolation(entity.getIsRuleViolation())
                .sectionType(entity.getSectionType())
                .representTravelIndex(entity.getRepresentTravelIndex())
                .baggageAllowance(entity.getBaggageAllowance())
                .createDate(Optional.ofNullable(entity.getCreateDate()).map(Date::getTime).orElse(null))
                .bookingAirId(entity.getBookingAirId())
                .compareScheduleDetails((entity.getCompareScheduleDetails() != null) ? CompareScheduleDetailRes.fromEntities(new ArrayList<>(entity.getCompareScheduleDetails())) : null)
                .build();
    }

    public static List<CompareAirScheduleRes> fromEntities(List<CompareAirSchedule> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream().map(CompareAirScheduleRes::fromEntity).toList();
    }
}
