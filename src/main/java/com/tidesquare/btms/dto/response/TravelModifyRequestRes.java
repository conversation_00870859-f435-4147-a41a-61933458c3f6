package com.tidesquare.btms.dto.response;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import com.tidesquare.btms.entity.TravelModifyRequest;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class TravelModifyRequestRes {
    private Long travelModifyRequestId;

    private Long parentTravelModifyRequestId;

    private String requestInfo;

    private String answer;

    private Long creatorUserId;

    private Long createDate;

    private Long modifierUserId;

    private Long modifyDate;

    private Boolean isNew;

    private String isLeaf;

    public static TravelModifyRequestRes fromEntity(TravelModifyRequest entity) {
        if (entity == null) {
            return null;
        }
        return TravelModifyRequestRes.builder()
                .travelModifyRequestId(entity.getTravelModifyRequestId())
                .parentTravelModifyRequestId(entity.getParentTravelModifyRequestId())
                .requestInfo(entity.getRequestInfo())
                .answer(entity.getAnswer())
                .creatorUserId(entity.getCreatorUserId())
                .createDate(Optional.ofNullable(entity.getCreateDate()).map(Date::getTime).orElse(null))
                .modifyDate(Optional.ofNullable(entity.getModifyDate()).map(Date::getTime).orElse(null))
                .modifierUserId(entity.getModifierUserId())
                .isNew(entity.getIsNew())
                .build();
    }

    public static List<TravelModifyRequestRes> fromEntities(List<TravelModifyRequest> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(TravelModifyRequestRes::fromEntity)
                .toList();
    }
}
