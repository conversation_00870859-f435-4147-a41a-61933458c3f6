package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.entity.AirportMain;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class AirportMainRes {
    private String airportSection;
    private String code;
    private String name;

    public static AirportMainRes fromEntity(AirportMain entity) {
        if (entity == null) {
            return null;
        }

        AirportMainResBuilder builder = AirportMainRes.builder()
                .airportSection(entity.getAirportSection())
                .code(entity.getCode())
                .name(entity.getName());

        return builder.build();
    }
}
