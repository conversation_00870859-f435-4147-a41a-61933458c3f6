package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.constant.ChargeType;
import com.tidesquare.btms.entity.PaymentCharge;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Builder
@RegisterForReflection
public class PaymentChargeRes {
    private Long paymentId; // 결제번호

    private ChargeType chargeType; //요금구분

    private Double amount; // 요금

    private Long bookingAirId;

    private String ticketNumber;

    public static PaymentChargeRes fromEntity(PaymentCharge entity) {
        if (entity == null) {
            return null;
        }
        return PaymentChargeRes.builder()
                .paymentId(entity.getPaymentId())
                .chargeType(entity.getChargeType())
                .amount(entity.getAmount())
                .bookingAirId(entity.getBookingAirId())
                .ticketNumber(entity.getTicketNumber())
                .build();
    }

    public static List<PaymentChargeRes> fromEntities(List<PaymentCharge> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(PaymentChargeRes::fromEntity)
                .toList();
    }
}
