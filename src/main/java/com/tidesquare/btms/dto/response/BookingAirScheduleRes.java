package com.tidesquare.btms.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tidesquare.btms.constant.SeatClass;
import com.tidesquare.btms.entity.BookingAirSchedule;
import com.tidesquare.btms.service.AirlineService;
import com.tidesquare.btms.service.AirportService;
import com.tidesquare.btms.utils.DateUtil;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Builder
@Getter
@Setter
public class BookingAirScheduleRes {
    private Long id;

    private int scheduleSeqNo;

    private AirlineRes airline;

    private String airlineFlightNo;

    private Date reserveDate;

    @JsonProperty("isVia")
    private boolean isVia;

    private AirlineRes opAirline;

    private String opAirlineText;

    private String fromDate;

    private AirportRes fromAirport;

    private String toDate;

    private AirportRes toAirport;

    private String bookingClassCode;

    private String gdsBookingStatusCode;

    private String leadTime; // HHMM형식

    private String groundTime; // HHMM형식

    private int flightMile;

    private int seatCountAdult;

    private int seatCountYoung;

    private int seatCountBaby;

    private Long modifyDate;

    private String baggageAllow;

    private String departureTerminal;

    private String arrivalTerminal;

    private SeatClass seatType;

    public static BookingAirScheduleRes fromEntity(BookingAirSchedule entity) {
        if (entity == null) {
            return null;
        }
        return BookingAirScheduleRes.builder()
                .id(entity.getId())
                .scheduleSeqNo(entity.getScheduleSeqNo())
                .airlineFlightNo(entity.getAirlineFlightNo())
                .reserveDate(entity.getReserveDate())
                .isVia(entity.isVia())
                .opAirlineText(entity.getOpAirlineText())
                .fromDate(entity.getFromDate() != null ? DateUtil.date2String(entity.getFromDate()) : null)
                .toDate(entity.getToDate() != null ? DateUtil.date2String(entity.getToDate()) : null)
                .bookingClassCode(entity.getBookingClassCode())
                .gdsBookingStatusCode(entity.getGdsBookingStatusCode())
                .leadTime(entity.getLeadTime())
                .groundTime(entity.getGroundTime())
                .flightMile(entity.getFlightMile())
                .seatCountAdult(entity.getSeatCountAdult())
                .seatCountYoung(entity.getSeatCountYoung())
                .seatCountBaby(entity.getSeatCountBaby())
                .modifyDate(Optional.ofNullable(entity.getModifyDate()).map(Date::getTime).orElse(null))
                .baggageAllow(entity.getBaggageAllow())
                .departureTerminal(entity.getDepartureTerminal())
                .arrivalTerminal(entity.getArrivalTerminal())
                .seatType(entity.getSeatType())
                .airline(AirlineRes.fromEntity(AirlineService.findByIdStatic(entity.getAirlineId())))
                .fromAirport(AirportRes.fromEntity(AirportService.findByIdStatic(entity.getFromAirportId())))
                .toAirport(AirportRes.fromEntity(AirportService.findByIdStatic(entity.getToAirportId())))
                .opAirline(AirlineRes.fromEntity(AirlineService.findByIdStatic(entity.getOpAirlineId())))
                .build();
    }

    public static List<BookingAirScheduleRes> fromEntities(List<BookingAirSchedule> entites) {
        return entites.stream()
                .map(e -> fromEntity(e))
                .toList();
    }
}
