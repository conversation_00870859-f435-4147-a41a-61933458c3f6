package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.entity.TravelerMileageInfo;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Builder
@Getter
@Setter
public class TravelerMileageInfoRes {
    private Long travelerMileageInfoId;

    private Long bookingAirTravelerId;

    private String airline;

    private String mileageMemberNo;

    public static TravelerMileageInfoRes fromEntity(TravelerMileageInfo entity) {
        if (entity == null)
            return null;
        return TravelerMileageInfoRes.builder()
                .travelerMileageInfoId(entity.getId())
                .bookingAirTravelerId(entity.getBookingAirTravelerId())
                .airline(entity.getAirline())
                .mileageMemberNo(entity.getMileageMemberNo())
                .build();
    }

    public static List<TravelerMileageInfoRes> fromEntities(List<TravelerMileageInfo> entities) {
        if (entities == null)
            return null;
        return entities.stream()
                .map(e -> fromEntity(e))
                .toList();
    }
}
