package com.tidesquare.btms.dto.response;

import java.util.List;

import com.tidesquare.btms.entity.Customer;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
public class CustomerRes extends UserRes {
    public static CustomerRes fromEntity(Customer entity) {
        if (entity == null) {
            return null;
        }

        return CustomerRes.builder()
                .id(entity.getId())
                .loginId(entity.getLoginId())
                .role(entity.getRole())
                .name(entity.getName())
                .gender(entity.getGender())
                .cellPhoneNumber(entity.getCellPhoneNumber())
                .accountingCode(entity.getAccountingCode())
                .birthday(entity.getBirthday())
                .phoneNumber(entity.getPhoneNumber())
                .employeeNo(entity.getEmployeeNo())
                .email(entity.getEmail())
                .joinAuthType(entity.getJoinAuthType())
                .joinDate(entity.getJoinDate())
                .joinStatus(entity.getJoinStatus())
                .leaveDate(entity.getLeaveDate())
                .leaveProcessUserId(entity.getLeaveProcessUserId())
                .incorrectPasswordCount(entity.getIncorrectPasswordCount())
                .passwordUpdateDate(entity.getPasswordUpdateDate())
                .nextPasswordUpdateDate(entity.getNextPasswordUpdateDate())
                .accountingCode(entity.getAccountingCode())
                .skypassMemberLastNm(entity.getSkypassMemberLastNm())
                .skypassMemberNo(entity.getSkypassMemberNo())
                .skypassUse(entity.getSkypassUse())
                .workspace(WorkspaceRes.fromEntity(entity.getWorkspace()))
                .department(DepartmentRes.fromEntity(entity.getDepartment()))
                .build();
    }

    public static List<CustomerRes> fromCustomers(List<Customer> entities) {
        if (entities == null) {
            return null;
        }

        return entities.stream().map(e -> fromEntity(e)).toList();
    }
}
