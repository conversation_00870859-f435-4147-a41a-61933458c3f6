package com.tidesquare.btms.dto.response;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import com.tidesquare.btms.constant.InvoicePublishType;
import com.tidesquare.btms.constant.InvoiceServiceType;
import com.tidesquare.btms.constant.InvoiceTemplateType;

@Builder
@Getter
@Setter
public class InvoiceTemplateRes {
    private Long invoiceTemplateId;

    private InvoiceServiceType invoiceServiceType;

    private InvoiceTemplateType invoiceTemplateType;

    private String name;

    private Date createDate;

    private Long creatorUserId;

    private Date modifyDate;

    private Long modifyUserId;     

    private InvoicePublishType invoicePublishType;
}
