package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.entity.USAState;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class USAStateRes {
    private String code;
    private String name;
    private String nameEn;

    public static USAStateRes fromEntity(USAState entity) {
        if (entity == null)
            return null;
        return USAStateRes.builder()
                .code(entity.getCode())
                .name(entity.getName())
                .nameEn(entity.getNameEn())
                .build();
    }
}
