package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.constant.GubunType;
import com.tidesquare.btms.constant.PaymentMethod;
import com.tidesquare.btms.constant.PaymentStatus;
import com.tidesquare.btms.entity.Payment;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Builder
@Getter
@Setter
@RegisterForReflection
public class PaymentRes {
    private Long paymentId; // 결제번호

    private CompanyRes company;

    private WorkspaceRes workspace;

    private GubunType gubun; // 구분

    private PaymentMethod paymentMethod; // 결제수단

    private PaymentStatus paymentStatus; // 결제상태

    private Double paymentAmount; // 결제금액

    private Long parentPayment; // 원결제번호

    private UserRes handlerUser; // 처리관리자아이디

    private Long creatorUserId; // 등록관리자아이디

    private Long createDate; // 등록일시

    private String repayYn; // 부분취소여부

    private String requestJson; // ERP 요청 json

    private List<PaymentChargeRes> paymentCharges;

    private List<PaymentCashRes> paymentCashs;

    private PaymentCardRes paymentCard;

    private HotelPaymentMappingRes hotelPaymentMapping;

    public static PaymentRes fromEntity(Payment entity) {
        if (entity == null) {
            return null;
        }
        return PaymentRes.builder()
                .paymentId(entity.getPaymentId())
                .gubun(entity.getGubun())
                .paymentMethod(entity.getPaymentMethod())
                .paymentStatus(entity.getPaymentStatus())
                .paymentAmount(entity.getPaymentAmount())
                .parentPayment(entity.getParentPaymentId())
                .handlerUser(UserRes.fromEntity(entity.getHandlerUser()))
                .creatorUserId(entity.getCreatorUserId())
                .createDate(Optional.ofNullable(entity.getCreateDate()).map(Date::getTime).orElse(null))
                .repayYn(entity.getRepayYn())
                .requestJson(entity.getRequestJson())
                .paymentCharges(entity.getPaymentCharges() != null ? PaymentChargeRes.fromEntities(new ArrayList<>(entity.getPaymentCharges())) : null)
                .paymentCashs(entity.getPaymentCashs() != null ? PaymentCashRes.fromEntities(new ArrayList<>(entity.getPaymentCashs())) : null)
                .paymentCard(PaymentCardRes.fromEntity(entity.getPaymentCard()))
                .hotelPaymentMapping(HotelPaymentMappingRes.fromEntity(entity.getHotelPaymentMapping()))
                .build();
    }

    public static List<PaymentRes> fromEntities(List<Payment> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(PaymentRes::fromEntity)
                .toList();
    }
}
