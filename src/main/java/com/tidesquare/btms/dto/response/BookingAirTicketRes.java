package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.constant.CommissionAmountNoteType;
import com.tidesquare.btms.constant.GDSType;
import com.tidesquare.btms.constant.UpAmountNoteType;
import com.tidesquare.btms.entity.BookingAirTicket;
import com.tidesquare.btms.constant.VoidType;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Optional;

import java.util.List;

@Builder
@Getter
@Setter
public class BookingAirTicketRes {
    private Long id;

    private AirlineRes ticketAirline;

    private UserRes manager; // 2020.02.03.kulc78 - DSR 티켓생성 담당자

    private UserRes ticketer; // 발권자 - 마지막 담당자

    private String ticketNo;

    private String conjunctionNo;

    private String pnrNo;

    private VoidType voidType;

    private UserRes passenger;

    private String originTicketNo;

    private String exchangeTicketNo;

    private String exchangeTicketInfo;

    // private CodeDto payTypeCode;

    private Long ticketDate;

    private Long refundDate; // 환불일시

    private String creationLocation; // 생성위치

    private String ticketYmd;

    private String fareCalcInfo;

    // private CodeDto fareCurrencyUnitCode;

    private Long fromTicketDate;

    private Long toTicketDate;

    private String fromAirportCode;

    private String toAirportCode;

    private Double fareAmount;

    private Double dcAmount;

    private Double taxAmount;

    private Double netAmount; // NET = FARE - DC

    private Double commissionAmount;

    private Double commissionTasfAmount; // TASF

    private Double commissionVatAmount; // VAT

    private Double uncutCommissionAmount; // 절삭전수수료

    private Double dsrCommissionAmount;// DSR 수수료

    private Double ticketAmount; // 발권금액 = Fare - DC + Tax

    private String firstName;

    private String lastName;

    private Double cardAmount; // 카드발권금액 (Payment로 삭제될 수 있음)

    private Double cashAmount; // 현금발권금액 (Payment로 삭제될 수 있음)

    private Double tasfAmount;// 항공요금과 별도로 계산

    private String endorsement;

    private String receiveEmail;

    private Boolean isTicketSend;

    private Date ticketSendDate;

    private String ticketRemark;

    // private List<BookingAirTicketScheduledDto> bookingAirTicketSchedules;

    // private List<BookingAirTicketTaxDto> bookingAirTicketTaxes;

    // private List<BookingAirTicketPaymentDto> bookingAirTicketPayments;

    // private List<BookingAirTicketRefundDto> bookingAirTicketRefunds;

    private Long bookingAirId;

    private String eticket;

    private String taxInvoiceMonth;

    private String groupNumber;

    private String officeId;

    private Double receivableAmount;

    private int invoicePublishCount;

    private GDSType gdsType;

    private Double upAmount;

    private UpAmountNoteType upAmountNoteType;

    private Double wvrAmount;

    private CommissionAmountNoteType commissionAmountNoteType;

    private Double totalAmount;
    private Double commissionRate;

    private String memo;

    // private AttachFileDto attachFile;

    private String eticket4Direct;

    private Double originCommissionAmount;// 수수료 = TASF

    private Long airlineId;

    private String ticketDateStr;

    private CompanyRes company;

    // private RefundRequestDTO refundRequest; // 환불내역서

    private Boolean isOverseasTicket; // 국내(false)/해외(true)

    private double totalPaidAmount;

    public static BookingAirTicketRes fromEntity(BookingAirTicket entity) {
        if (entity == null)
            return null;
        return BookingAirTicketRes.builder()
                .id(entity.getId())
                .ticketNo(entity.getTicketNo())
                .conjunctionNo(entity.getConjunctionNo())
                .pnrNo(entity.getPnrNo())
                .voidType(entity.getVoidType())
                .originTicketNo(entity.getOriginTicketNo())
                .exchangeTicketNo(entity.getExchangeTicketNo())
                .exchangeTicketInfo(entity.getExchangeTicketInfo())
                .ticketDate(Optional.ofNullable(entity.getTicketDate()).map(Date::getTime).orElse(null))
                .refundDate(Optional.ofNullable(entity.getRefundDate()).map(Date::getTime).orElse(null))
                .creationLocation(entity.getCreationLocation())
                .ticketYmd(entity.getTicketYmd())
                .fareCalcInfo(entity.getFareCalcInfo())
                .fromTicketDate(Optional.ofNullable(entity.getFromTicketDate()).map(Date::getTime).orElse(null))
                .toTicketDate(Optional.ofNullable(entity.getToTicketDate()).map(Date::getTime).orElse(null))
                .fromAirportCode(entity.getFromAirportCode())
                .toAirportCode(entity.getToAirportCode())
                .fareAmount(entity.getFareAmount())
                .dcAmount(entity.getDcAmount())
                .taxAmount(entity.getTaxAmount())
                .netAmount(entity.getNetAmount())
                .commissionAmount(entity.getCommissionAmount())
                .commissionTasfAmount(entity.getCommissionTasfAmount())
                .commissionVatAmount(entity.getCommissionVatAmount())
                .uncutCommissionAmount(entity.getUncutCommissionAmount())
                .dsrCommissionAmount(entity.getDsrCommissionAmount())
                .ticketAmount(entity.getTicketAmount())
                .firstName(entity.getFirstName())
                .lastName(entity.getLastName())
                .cardAmount(entity.getCardAmount())
                .cashAmount(entity.getCashAmount())
                .tasfAmount(entity.getTasfAmount())
                .endorsement(entity.getEndorsement())
                .receiveEmail(entity.getReceiveEmail())
                .isTicketSend(entity.getIsTicketSend())
                .ticketSendDate(entity.getTicketSendDate())
                .ticketRemark(entity.getTicketRemark())
                .bookingAirId(entity.getBookingAirId())
                .eticket(entity.getEticket())
                .taxInvoiceMonth(entity.getTaxInvoiceMonth())
                .groupNumber(entity.getGroupNumber())
                .officeId(entity.getOfficeId())
                .receivableAmount(entity.getReceivableAmount())
                .invoicePublishCount(entity.getInvoicePublishCount())
                .gdsType(entity.getGdsType())
                .upAmount(entity.getUpAmount())
                .upAmountNoteType(entity.getUpAmountNoteType())
                .wvrAmount(entity.getWvrAmount())
                .commissionAmountNoteType(entity.getCommissionAmountNoteType())
                .totalAmount(entity.getTotalAmount())
                .commissionRate(entity.getCommissionRate())
                .memo(entity.getMemo())
                .eticket4Direct(entity.getEticket4Direct())
                .originCommissionAmount(entity.getOriginCommissionAmount())
                .isOverseasTicket(entity.getIsTicketSend())
                .totalPaidAmount(entity.getPaymentAmount())
                .build();
    }

    public static List<BookingAirTicketRes> fromEntities(List<BookingAirTicket> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(BookingAirTicketRes::fromEntity)
                .toList();
    }
}
