package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.entity.Department;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class DepartmentRes {
    private Long id;
    private String name;
    private Long parentId;

    public static DepartmentRes fromEntity(Department entity) {
        if (entity == null)
            return null;
        return DepartmentRes.builder()
                .id(entity.getId())
                .name(entity.getName())
                .build();
    }
}
