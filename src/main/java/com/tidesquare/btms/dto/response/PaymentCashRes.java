package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.constant.ChargeType;
import com.tidesquare.btms.entity.PaymentCash;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.stream.Collectors;

@Getter
@Setter
@Builder
@RegisterForReflection
public class PaymentCashRes {
    private Long paymentId; // 결제번호

    private String detailPK; // 참조관리순번(PK)

    private ChargeType chargeType; //요금구분

    private Integer isCorporation; // 법인여부

    //BTMS 에 등록 필요
    private String divCode; // 사업장

    private String collectNumber; // 입급번호(PK)

    private String masterPK; // 참조관리번호(PK)

    private String accountManageCode; // 계좌관리코드

    private String accountManageName; // 계좌관리명

    private String receiptDate; // 처리일자

    private Double receiptAmount; // 처리금액

    private String receiptDeptCode; //처리부서코드

    private String receiptPrsnCode; //처리사번

    private String serviceType; //서비스타입

    private String depCustCode; //입금 거래처 코드

    private String depCustName; //입금 거래처명

    private String bJukyo; //법인팀 입금 적요

    private String remark; //비고

    private String orderId; //주문번호

    private String ticketNumber; //티켓번호

    private String receiptKindCode; //입금처리코드

    private String matchDate; //입금일자

    //NULL 무관
    private String hccKindCode; //혜택종류코드

    private String invoiceYm; //인보이스년월

    private String invoiceDate; //인보이스일자

    private String refKindCode; //참조종류 코드

    private String refKindName; //참조종류 코드명

    private String fareCode; //요금코드

    private String fareCodeName; //요금코드명

    private String travelId;

    private String travelerName;

    //2020.03.18.kulc78 - 은행입금 멀티
    private Long bookingAirId;

    public static PaymentCashRes fromEntity(PaymentCash entity) {
        if (entity == null) {
            return null;
        }
        return PaymentCashRes.builder()
                .paymentId(entity.getPaymentId())
                .detailPK(entity.getDetailPK())
                .chargeType(entity.getChargeType())
                .isCorporation(entity.getIsCorporation())
                .divCode(entity.getDivCode())
                .collectNumber(entity.getCollectNumber())
                .masterPK(entity.getMasterPK())
                .accountManageCode(entity.getAccountManageCode())
                .accountManageName(entity.getAccountManageName())
                .receiptDate(entity.getReceiptDate())
                .receiptAmount(entity.getReceiptAmount())
                .receiptDeptCode(entity.getReceiptDeptCode())
                .receiptPrsnCode(entity.getReceiptPrsnCode())
                .serviceType(entity.getServiceType())
                .depCustCode(entity.getDepCustCode())
                .depCustName(entity.getDepCustName())
                .bJukyo(entity.getBJukyo())
                .remark(entity.getRemark())
                .orderId(entity.getOrderId())
                .ticketNumber(entity.getTicketNumber())
                .receiptKindCode(entity.getReceiptKindCode())
                .matchDate(entity.getMatchDate())
                .hccKindCode(entity.getHccKindCode())
                .invoiceYm(entity.getInvoiceYm())
                .invoiceDate(entity.getInvoiceDate())
                .refKindCode(entity.getRefKindCode())
                .refKindName(entity.getRefKindName())
                .fareCode(entity.getFareCode())
                .fareCodeName(entity.getFareCodeName())
                .bookingAirId(entity.getBookingAirId())
                .build();
    }

    public static List<PaymentCashRes> fromEntities(List<PaymentCash> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(PaymentCashRes::fromEntity) // Sử dụng phương thức fromEntity để chuyển đổi
                .collect(Collectors.toList());
    }
}
