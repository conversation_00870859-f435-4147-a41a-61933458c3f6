package com.tidesquare.btms.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tidesquare.btms.constant.*;
import com.tidesquare.btms.entity.Company;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Builder
@Getter
@Setter
public class CompanyRes {
    private Long id;

    private CompanyType companyType; // 거래처 유형

    @JsonProperty("isSamsungPG")
    private boolean isSamsungPG;

    private String siteCode; // 타이드스퀘어 거래처 코드

    private String erpSiteCode; // 은행입금 관련 거래처 코드

    private String name;

    private String businessNum;

    private String representativeName;

    private String companyTelNo;

    private String companyFaxNo;

    private BtmsSettingRes btmsSetting;

    private AirEmSettingRes airEmSetting;

    @JsonProperty("isExcludeAirUse")
    private boolean isExcludeAirUse;

    // 해외 이코노미 항공 수수료
    private Double economyAirComRate;

    // 해외 비즈니스 항공 수수료
    private Double businessAirComRate;

    // 국내 항공 수수료
    private Double domesticAirComRate;

    // 국내 판매 수수료
    private Double domesticSaleComRate;

    // S/FARE OR 판매총액
    private DomesticSaleComRateType domesticSaleComRateType;

    // 월말 결제 여부
    @JsonProperty("isMonthEndPayment")
    private boolean isMonthEndPayment;

    // 세금계산서 발행 VAT 설정 여부 (Default: false)
    @JsonProperty("isTaxInvoiceVat")
    private boolean isTaxInvoiceVat;

    // 항공 수수료 절삭 타입
    private AirComMathType airComMathType;

    // 항공 수수료 절삭 단위
    private AirComCuttingUnit comCuttingUnit;

    // 항공 수수료 VAT 절삭 타입
    private AirComMathType airComVatMathType;

    // 항공 수수료 VAT 절삭 단위
    private AirComCuttingUnit comVatCuttingUnit;

    private CommissionCalculationType commissionCalculationType;

    @JsonProperty("isDeleted")
    private boolean isDeleted;

    private AddressRes address;

    private Long createDate;

    private TravelAgencyUserRes creator;// NO

    private List<WorkspaceRes> workspaces;// NO

    @JsonProperty("isAgency")
    private boolean isAgency;

    private List<ExcludeAirlineRes> excludeAirlines;

    private Double airYearlyVolume;

    private Double hotelYearlyVolume;

    private Long invoiceFormId;

    private Long invoiceUnitTemplateId;

    private Long invoiceBundleTemplateId;

    private Long refundInvoiceUnitTemplateId;

    private Long refundInvoiceBundleTemplateId;

    private Long hotelInvoiceUnitTemplateId;

    private Long hotelInvoiceBundleTemplateId;

    @JsonProperty("isCustom")
    private boolean isCustom;

    @JsonProperty("isGroup")
    private boolean isGroup;

    private String groupCode;

    private CompanyRes parent;

    public static CompanyRes fromEntity(Company entity) {
        if (entity == null)
            return null;
        return CompanyRes.builder()
                .id(entity.getId())
                .businessNum(entity.getBusinessNum())
                .representativeName(entity.getRepresentativeName())
                .companyType(entity.getCompanyType())
                .siteCode(entity.getSiteCode())
                .erpSiteCode(entity.getErpSiteCode())
                .name(entity.getName())
                .companyTelNo(entity.getCompanyTelNo())
                .companyFaxNo(entity.getCompanyFaxNo())
                .economyAirComRate(entity.getEconomyAirComRate())
                .businessAirComRate(entity.getBusinessAirComRate())
                .domesticAirComRate(entity.getDomesticAirComRate())
                .domesticSaleComRate(entity.getDomesticSaleComRate())
                .isMonthEndPayment(entity.isMonthEndPayment())
                .isTaxInvoiceVat(entity.isTaxInvoiceVat())
                .isDeleted(entity.isDeleted())
                .createDate(Optional.ofNullable(entity.getCreateDate()).map(Date::getTime).orElse(null))
                .isAgency(entity.isAgency())
                .excludeAirlines(entity.getExcludeAirlines() != null ? ExcludeAirlineRes.fromEntities(new ArrayList<>(entity.getExcludeAirlines())) : null)
                .airComVatMathType(entity.getAirComVatMathType())
                .airYearlyVolume(entity.getAirYearlyVolume())
                .hotelYearlyVolume(entity.getHotelYearlyVolume())
                .invoiceFormId(entity.getInvoiceFormId())
                .invoiceUnitTemplateId(entity.getInvoiceUnitTemplateId())
                .invoiceBundleTemplateId(entity.getInvoiceBundleTemplateId())
                .refundInvoiceUnitTemplateId(entity.getRefundInvoiceUnitTemplateId())
                .refundInvoiceBundleTemplateId(entity.getRefundInvoiceBundleTemplateId())
                .hotelInvoiceUnitTemplateId(entity.getHotelInvoiceUnitTemplateId())
                .hotelInvoiceBundleTemplateId(entity.getHotelInvoiceBundleTemplateId())
                .isCustom(entity.isCustom())
                .isExcludeAirUse(entity.isExcludeAirUse())
                .comCuttingUnit(entity.getComCuttingUnit())
                .comVatCuttingUnit(entity.getComVatCuttingUnit())
                .airComMathType(entity.getAirComMathType())
                .airComVatMathType(entity.getAirComVatMathType())
                .isSamsungPG(entity.isSamsungPG())
                .domesticSaleComRateType(entity.getDomesticSaleComRateType())
                .commissionCalculationType(entity.getCommissionCalculationType())
                .btmsSetting(BtmsSettingRes.fromEntity(entity.getBtmsSetting()))
                .airEmSetting(AirEmSettingRes.fromEntity(entity.getAirEmSetting()))
                .isGroup(entity.isGroup())
                .groupCode(entity.getGroupCode())
                .creator(TravelAgencyUserRes.fromEntity(entity.getCreator()))
                .parent(CompanyRes.fromEntity(entity.getParent()))
                .build();
    }

    public static List<CompanyRes> fromEntities(List<Company> entities) {
        return entities.stream()
                .map(e -> fromEntity(e))
                .toList();
    }
}
