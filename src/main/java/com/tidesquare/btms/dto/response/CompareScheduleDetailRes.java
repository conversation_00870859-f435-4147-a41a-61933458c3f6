package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.entity.CompareScheduleDetail;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Builder
@Getter
@Setter
public class CompareScheduleDetailRes {
    private Long id;

    private Integer itinerarySeq;

    private String totalHour;

    private String totalMin;

    private String flightHour;

    private String flightMin;

    private Integer stopoverNo;

    private String departureTerminal; // 출발터미널

    private String arrivalTerminal; // 도착터미널

    private Boolean seatWaiting; // 좌석 확정/대기

    private List<CompareFlightDetailRes> flightDetails;

    public static CompareScheduleDetailRes fromEntity(CompareScheduleDetail entity) {
        if (entity == null) {
            return null;
        }
        return CompareScheduleDetailRes.builder()
                .id(entity.getId())
                .itinerarySeq(entity.getItinerarySeq())
                .totalHour(entity.getTotalHour())
                .totalMin(entity.getTotalMin())
                .flightHour(entity.getFlightHour())
                .flightMin(entity.getFlightMin())
                .stopoverNo(entity.getStopoverNo())
                .departureTerminal(entity.getDepartureTerminal())
                .arrivalTerminal(entity.getArrivalTerminal())
                .seatWaiting(entity.getSeatWaiting())
                .flightDetails((entity.getFlightDetails() != null) ? CompareFlightDetailRes.fromEntities(new ArrayList<>(entity.getFlightDetails())) : null)
                .build();
    }

    public static List<CompareScheduleDetailRes> fromEntities(List<CompareScheduleDetail> entities) {
        if (entities == null)
            return null;
        return entities.stream()
                .map(CompareScheduleDetailRes::fromEntity)
                .toList();
    }
}
