package com.tidesquare.btms.dto.response;

import java.util.List;

import com.tidesquare.btms.constant.ManagerType;
import com.tidesquare.btms.entity.BtmsManager;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class BtmsManagerRes {
    private int displayOrder;

    private ManagerType managerType;

    private Boolean isOverseas;

    private UserRes travelAgencyUser;

    public static BtmsManagerRes fromEntity(BtmsManager entity) {
        if (entity == null) {
            return null;
        }
        return BtmsManagerRes.builder()
                .displayOrder(entity.getDisplayOrder())
                .managerType(entity.getManagerType())
                .isOverseas(entity.getIsOverseas())
                .travelAgencyUser(UserRes.fromEntity(entity.getTravelAgencyUser()))
                .build();
    }

    public static List<BtmsManagerRes> fromEntities(List<BtmsManager> entities) {
        if (entities == null)
            return null;
        return entities.stream()
                .map(e -> fromEntity(e))
                .toList();
    }
}
