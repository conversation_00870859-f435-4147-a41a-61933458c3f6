package com.tidesquare.btms.dto.response;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tidesquare.btms.constant.ExceptTimeType;
import com.tidesquare.btms.constant.SeatClass;
import com.tidesquare.btms.constant.TravelRuleType;
import com.tidesquare.btms.entity.TravelRule;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class TravelRuleRes {
    private Long travelRuleId;
    private Long workspaceId;
    private TravelRuleType travelRuleType;
    private Integer travelLimitDay;
    private SeatClass baseSeatTypeCode;
    private Integer exceptBordingTime;
    private SeatClass exceptSeatTypeCode;
    private String etc;
    private ExceptTimeType exceptTimeType;
    @JsonProperty("isExceptUse")
    private Boolean isExceptUse;
    private List<ExceptCityRes> exceptCities;

    public static TravelRuleRes fromEntity(TravelRule entity) {
        if (entity == null) {
            return null;
        }

        return TravelRuleRes.builder()
                .travelRuleId(entity.getTravelRuleId())
                .workspaceId(entity.getWorkspaceId())
                .travelRuleType(entity.getTravelRuleType())
                .travelLimitDay(entity.getTravelLimitDay())
                .baseSeatTypeCode(entity.getBaseSeatTypeCode())
                .exceptBordingTime(entity.getExceptBordingTime())
                .exceptSeatTypeCode(entity.getExceptSeatTypeCode())
                .etc(entity.getEtc())
                .exceptTimeType(entity.getExceptTimeType())
                .isExceptUse(entity.getIsExceptUse())
                .exceptCities(entity.getExceptCities() != null ? ExceptCityRes.fromEntities(new ArrayList<>(entity.getExceptCities())) : null)
                .build();
    }
}
