package com.tidesquare.btms.dto.response;

import java.util.List;

import com.tidesquare.btms.constant.ContactRequestStatus;
import com.tidesquare.btms.constant.ContactRequestType;
import com.tidesquare.btms.entity.ContactRequest;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class ContactRequestRes {

    private Long id;

    private ContactRequestType requestType;

    private String name;

    private String businessName;

    private String email;

    private String phoneNumber;

    private String message;

    private String note;

    private ContactRequestStatus status;

    private UserRes respondent;

    private Long answeredAt;

    private Long createDate;

    public static ContactRequestRes fromEntity(ContactRequest entity) {
        if (entity == null) {
            return null;
        }

        return ContactRequestRes.builder()
                .id(entity.getId())
                .requestType(entity.getRequestType())
                .name(entity.getName())
                .businessName(entity.getBusinessName())
                .email(entity.getEmail())
                .phoneNumber(entity.getPhoneNumber())
                .message(entity.getMessage())
                .note(entity.getNote())
                .status(entity.getStatus())
                .respondent(UserRes.fromEntity(entity.getRespondent()))
                .answeredAt(entity.getAnsweredAt() != null ? entity.getAnsweredAt().getTime() : null)
                .createDate(entity.getCreateDate().getTime())
                .build();
    }

    public static List<ContactRequestRes> fromEntities(List<ContactRequest> entities) {
        if (entities == null)
            return null;
        return entities.stream()
                .map(ContactRequestRes::fromEntity)
                .toList();
    }
}
