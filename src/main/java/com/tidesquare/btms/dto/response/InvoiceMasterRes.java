package com.tidesquare.btms.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tidesquare.btms.constant.InvoiceLanguageType;
import com.tidesquare.btms.constant.InvoiceReservationType;
import com.tidesquare.btms.constant.InvoiceViewType;
import com.tidesquare.btms.entity.InvoiceMaster;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Optional;
import java.util.Date;
@Builder
@Getter
@Setter
public class InvoiceMasterRes {
    private Long invoiceId;

    private InvoiceTemplateRes invoiceTemplate;

    private CompanyRes company;

    private InvoiceReservationType reservationType;

    private InvoiceLanguageType languageType;

    private InvoiceViewType viewType;

    @JsonProperty("isRefundBill")
    private boolean isRefundBill;

    private String filePath;

    private Long createDate;

    private Long creatorUserId;

    private Long modifyDate;

    private Long modifyUserId;

    private Boolean isOldVersion;

    private Boolean isDelete;

    public static InvoiceMasterRes fromEntity(InvoiceMaster entity) {
        if (entity == null)
            return null;
        return InvoiceMasterRes.builder()
                .invoiceId(entity.getInvoiceId())
                .reservationType(entity.getReservationType())
                .languageType(entity.getLanguageType())
                .viewType(entity.getViewType())
                .isRefundBill(entity.isRefundBill())
                .filePath(entity.getFilePath())
                .isOldVersion(entity.getIsOldVersion())
                .createDate(Optional.ofNullable(entity.getCreateDate()).map(Date::getTime).orElse(null))
                .creatorUserId(entity.getCreatorUserId())
                .modifyDate(Optional.ofNullable(entity.getModifyDate()).map(Date::getTime).orElse(null))
                .modifyUserId(entity.getModifyUserId())
                .build();
    }

    public static List<InvoiceMasterRes> fromEntities(List<InvoiceMaster> entites) {
        if (entites == null)
            return null;
        return entites.stream()
                .map(e -> fromEntity(e))
                .toList();
    }
}
