package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.constant.TravelBookingType;
import com.tidesquare.btms.constant.TravelStatus;
import com.tidesquare.btms.entity.TravelStatusHistory;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Builder
@Getter
@Setter
public class TravelStatusHistoryRes {
    private TravelBookingType travelBookingType;

    private TravelStatus status;

    private String modifyInfo;

    private Long modifyDate;

    private UserRes modifier;

    public static TravelStatusHistoryRes fromEntity(TravelStatusHistory entity) {
        if (entity == null)
            return null;
        return TravelStatusHistoryRes.builder()
                .modifyInfo(entity.getModifyInfo())
                .modifyDate(Optional.ofNullable(entity.getModifyDate()).map(Date::getTime).orElse(null))
                .travelBookingType(entity.getTravelBookingType())
                .modifier(UserRes.fromEntity(entity.getModifier()))
                .status(entity.getStatus())
                .build();
    }

    public static List<TravelStatusHistoryRes> fromEntites(List<TravelStatusHistory> entities) {
        if(entities == null) return null;
        return entities.stream()
                .map(e -> fromEntity(e))
                .toList();
    }
}
