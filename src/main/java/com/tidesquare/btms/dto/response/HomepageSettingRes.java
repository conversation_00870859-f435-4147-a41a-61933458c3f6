package com.tidesquare.btms.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.tidesquare.btms.entity.HomepageSetting;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class HomepageSettingRes {
    private Long companyId;

    @JsonProperty("isUseDefaultLogo")
    private boolean isUseDefaultLogo;

    private AttachFileRes loginLogoAttachFile;

    private AttachFileRes gnbLogoAttachFile;

    public static HomepageSettingRes fromEntity(HomepageSetting entity) {
        if (entity == null) {
            return null;
        }

        return HomepageSettingRes.builder()
                .companyId(entity.getCompanyId())
                .isUseDefaultLogo(entity.isUseDefaultLogo())
                .loginLogoAttachFile(AttachFileRes.fromEntity(entity.getLoginLogoAttachFile()))
                .gnbLogoAttachFile(AttachFileRes.fromEntity(entity.getGnbLogoAttachFile()))
                .build();
    }
}
