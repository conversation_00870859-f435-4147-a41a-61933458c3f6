package com.tidesquare.btms.dto.response;

import java.util.ArrayList;
import java.util.List;

import com.tidesquare.btms.constant.BookingHotelStatus;
import com.tidesquare.btms.entity.BookingHotel;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
@RegisterForReflection
public class BookingHotelRes {
    private Long id;
    private CompanyRes company;
    private Long workspaceId;
    private BookingHotelStatus status;
    private Long bookingDate;
    private String hotelInflowPath;
    private String violationReason;
    private Long reserverUserId;
    private String reserverName;
    private String reserverPhoneNumber;
    private String reserverEmail;
    private String approvalMemo;
    private Boolean isApproval;
    private Long approvalUserId;
    private Long approvalDate;
    private Long createDate;
    private String hotelMasterId;
    private String hotelNameKr;
    private String hotelNameEn;
    private Long bookingId;
    private String checkInYmd;
    private String checkOutYmd;
    private String roomInfo;
    private Double salePrice;
    private String bookingCode;
    private String hotelCategory;
    private TravelAgencyUserRes manager;
    private String natNm;
    private String endCityNm;
    private Integer roomCnt;
    private List<BookingHotelGuestRes> bookingHotelGuests;
    private List<DocumentNumberRes> documentNumbers;

    public static BookingHotelRes fromEntity(BookingHotel entity) {
        if (entity == null) {
            return null;
        }

        return BookingHotelRes.builder()
                .id(entity.getId())
                .company(CompanyRes.fromEntity(entity.getCompany()))
                .workspaceId(entity.getWorkspaceId())
                .status(entity.getStatus())
                .bookingDate(entity.getBookingDate() != null ? entity.getBookingDate().getTime() : null)
                .hotelInflowPath(entity.getHotelInflowPath())
                .violationReason(entity.getViolationReason())
                .reserverUserId(entity.getReserverUserId())
                .reserverName(entity.getReserverName())
                .reserverPhoneNumber(entity.getReserverPhoneNumber())
                .reserverEmail(entity.getReserverEmail())
                .approvalMemo(entity.getApprovalMemo())
                .isApproval(entity.getIsApproval())
                .approvalUserId(entity.getApprovalUserId())
                .approvalDate(entity.getApprovalDate() != null ? entity.getApprovalDate().getTime() : null)
                .createDate(entity.getCreateDate() != null ? entity.getCreateDate().getTime() : null)
                .hotelMasterId(entity.getHotelMasterId())
                .hotelNameKr(entity.getHotelNameKr())
                .hotelNameEn(entity.getHotelNameEn())
                .bookingId(entity.getBookingId())
                .checkInYmd(entity.getCheckInYmd())
                .checkOutYmd(entity.getCheckOutYmd())
                .roomInfo(entity.getRoomInfo())
                .salePrice(entity.getSalePrice())
                .bookingCode(entity.getBookingCode())
                .hotelCategory(entity.getHotelCategory())
                .manager(TravelAgencyUserRes.fromEntity(entity.getManager()))
                .natNm(entity.getNatNm())
                .endCityNm(entity.getEndCityNm())
                .roomCnt(entity.getRoomCnt())
                .bookingHotelGuests(entity.getBookingHotelGuests() != null ? BookingHotelGuestRes.fromEntities(new ArrayList<>(entity.getBookingHotelGuests())) : null)
                .documentNumbers(entity.getDocumentNumbers() != null ? DocumentNumberRes.fromEntities(new ArrayList<>(entity.getDocumentNumbers())) : null)
                .build();
    }

    public static List<BookingHotelRes> fromEntities(List<BookingHotel> entities) {
        if (entities == null) {
            return null;
        }

        return entities.stream()
                .map(BookingHotelRes::fromEntity)
                .toList();
    }
}
