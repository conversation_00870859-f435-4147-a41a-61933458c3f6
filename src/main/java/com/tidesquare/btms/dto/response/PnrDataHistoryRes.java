package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.constant.PnrDataHistoryType;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Builder
@Getter
@Setter
public class PnrDataHistoryRes {
    private String pnrData;

    private UserRes manager;    //예약자정보(CRS에서 예약한 경우 필수 예약자)

    private Date createDate;

    private PnrDataHistoryType pnrDataHistoryType;
}
