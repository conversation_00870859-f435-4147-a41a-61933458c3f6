package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.entity.CompareFlightDetail;
import com.tidesquare.btms.utils.DateUtil;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Builder
@Getter
@Setter
public class CompareFlightDetailRes {
    private Long id;

    private Integer scheduleSeqNo;

    private String airlineCode;

    private String airlineName;

    private String operatingAirlineCode;

    private String operatingAirlineName;

    private String airlineFlightNo;

    private String fromAirportCode;

    private String fromAirportName;

    private String fromDate;

    private String toAirportCode;

    private String toAirportName;

    private String toDate;

    private String leadHour; // HHMM형식

    private String leadMin; // HHMM형식

    private String groundHour;

    private String groundMin;

    private String bookingClassCode;

    private String seatClassName;

    private Integer numberOfSeats;

    private Boolean gdsBookingStatusCode;

    private Integer dateVariation;

    public static CompareFlightDetailRes fromEntity(CompareFlightDetail entity) {
        if (entity == null) {
            return null;
        }
        return CompareFlightDetailRes.builder()
                .id(entity.getId())
                .scheduleSeqNo(entity.getScheduleSeqNo())
                .airlineCode(entity.getAirlineCode())
                .airlineName(entity.getAirlineName())
                .operatingAirlineCode(entity.getOperatingAirlineCode())
                .operatingAirlineName(entity.getOperatingAirlineName())
                .airlineFlightNo(entity.getAirlineFlightNo())
                .fromAirportCode(entity.getFromAirportCode())
                .fromAirportName(entity.getFromAirportName())
                .fromDate(entity.getFromDate() != null ? DateUtil.date2String(entity.getFromDate()) : null)
                .toAirportCode(entity.getToAirportCode())
                .toAirportName(entity.getToAirportName())
                .toDate(entity.getToDate() != null ? DateUtil.date2String(entity.getToDate()) : null)
                .leadHour(entity.getLeadHour())
                .leadMin(entity.getLeadMin())
                .groundHour(entity.getGroundHour())
                .groundMin(entity.getGroundMin())
                .bookingClassCode(entity.getBookingClassCode())
                .seatClassName(entity.getSeatClassName())
                .numberOfSeats(entity.getNumberOfSeats())
                .gdsBookingStatusCode(entity.getGdsBookingStatusCode())
                .dateVariation(entity.getDateVariation())
                .build();
    }

    public static List<CompareFlightDetailRes> fromEntities(List<CompareFlightDetail> entities) {
        if(entities == null) return null;
        return entities.stream()
                .map(CompareFlightDetailRes::fromEntity)
                .toList();
    }
}
