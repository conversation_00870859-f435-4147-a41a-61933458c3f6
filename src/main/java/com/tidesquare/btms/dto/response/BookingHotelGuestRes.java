package com.tidesquare.btms.dto.response;

import java.util.List;

import com.tidesquare.btms.entity.BookingHotelGuest;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
@RegisterForReflection
public class BookingHotelGuestRes {
    private long bookingHotelId;
    private int roomIndex;
    private int guestOrderSeq;
    private String ageGubun;
    private String firstName;
    private String lastName;
    private String guestName;
    private CustomerRes guestUser;

    public static BookingHotelGuestRes fromEntity(BookingHotelGuest entity) {
        if (entity == null) {
            return null;
        }

        return BookingHotelGuestRes.builder()
                .bookingHotelId(entity.getBookingHotelId())
                .roomIndex(entity.getRoomIndex())
                .guestOrderSeq(entity.getGuestOrderSeq())
                .ageGubun(entity.getAgeGubun())
                .firstName(entity.getFirstName())
                .lastName(entity.getLastName())
                .guestName(entity.getGuestName())
                .guestUser(CustomerRes.fromEntity(entity.getGuestUser()))
                .build();
    }

    public static List<BookingHotelGuestRes> fromEntities(List<BookingHotelGuest> entities) {
        if (entities == null) {
            return null;
        }

        return entities.stream()
                .map(BookingHotelGuestRes::fromEntity)
                .toList();
    }
}
