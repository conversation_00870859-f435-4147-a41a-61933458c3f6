package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.constant.CardCompanyCode;
import com.tidesquare.btms.entity.PaymentCard;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Builder
@RegisterForReflection
public class PaymentCardRes {
    private Long paymentId; // 결제번호

    private String mid; // 상점아이디

    private String goodName; // 상품명

    private String customerName; // 고객명

    private String phoneNumber; // 전화번호

    private String email; // Email

    private String cardNumber; // 카드번호

    private CardCompanyCode cardCompanyCode; // 카드사코드

    private String cardKind; // 카드 종류 (0 : 신용 / 1 : 체크 / 2 : 기프트)

    private String expiryY; // 유효기간_년

    private String expiryM; // 유효기간_월

    private String quotaMonth; // 할부개월

    private Boolean isQuotaInterest; // 무이자여부

    private String url; // 가맹점URL

    private String cancelMessage; // 취소사유

    private String resultCode; // 결과코드

    private String resultMessage; // 결과메시지

    private String tId; // PG거래번호

    private String approvalNumber; // 승인번호

    private String approvalYmd; // 승인일자

    private String approvalHms; // 승인시간

    private Boolean isPartCancel; // 부분취소 가능 여부(1: 부분취소 가능 /0: 부분취소 불가능)

    private String birthday; // 생년월일(YYMMDD)

    private String moid; // 주문번호

    private Boolean isCorporateCard;

    private String cardAlias;

    public static PaymentCardRes fromEntity(PaymentCard entity) {
        if (entity == null) {
            return null;
        }
        return PaymentCardRes.builder()
                .paymentId(entity.getPaymentId())
                .mid(entity.getMid())
                .goodName(entity.getGoodName())
                .customerName(entity.getCustomerName())
                .phoneNumber(entity.getPhoneNumber())
                .email(entity.getEmail())
                .cardNumber(entity.getCardNumber())
                .cardCompanyCode(entity.getCardCompanyCode())
                .cardKind(entity.getCardKind())
                .expiryY(entity.getExpiryY())
                .expiryM(entity.getExpiryM())
                .quotaMonth(entity.getQuotaMonth())
                .isQuotaInterest(entity.getIsQuotaInterest())
                .url(entity.getUrl())
                .cancelMessage(entity.getCancelMessage())
                .resultCode(entity.getResultCode())
                .resultMessage(entity.getResultMessage())
                .tId(entity.getTId())
                .approvalNumber(entity.getApprovalNumber())
                .approvalYmd(entity.getApprovalYmd())
                .approvalHms(entity.getApprovalHms())
                .isPartCancel(entity.getIsPartCancel())
                .isCorporateCard(entity.getIsCorporateCard())
                .birthday(entity.getBirthday())
                .moid(entity.getMoid())
                .cardAlias(entity.getCardAlias())
                .build();
    }

    public static List<PaymentCardRes> fromEntities(List<PaymentCard> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream().map(PaymentCardRes::fromEntity).toList();
    }
}
