package com.tidesquare.btms.dto.response;

import java.util.List;

import com.tidesquare.btms.entity.HotelPaymentMapping;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
@RegisterForReflection
public class HotelPaymentMappingRes {
    private Long paymentId;
    private BookingHotelRes bookingHotel;

    public static HotelPaymentMappingRes fromEntity(HotelPaymentMapping entity) {
        if (entity == null) {
            return null;
        }

        return HotelPaymentMappingRes.builder()
                .paymentId(entity.getPaymentId())
                .bookingHotel(BookingHotelRes.fromEntity(entity.getBookingHotel()))
                .build();
    }

    public static List<HotelPaymentMappingRes> fromEntities(List<HotelPaymentMapping> entities) {
        if (entities == null) {
            return null;
        }

        return entities.stream()
                .map(HotelPaymentMappingRes::fromEntity)
                .toList();
    }
}
