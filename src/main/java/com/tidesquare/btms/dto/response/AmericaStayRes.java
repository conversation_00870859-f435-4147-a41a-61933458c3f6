package com.tidesquare.btms.dto.response;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import com.tidesquare.btms.entity.AmericaStay;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter

public class AmericaStayRes {
    private Long id;

    private String stateCode;

    private String stateName;

    private String cityCode;

    private String cityName;

    private String zipCode;

    private String address;

    private Long creatorUserId;

    private Long createDate;

    private Long modifierUserId;

    private Long modifyDate;

    public static AmericaStayRes fromEntity(AmericaStay entity) {
        if (entity == null)
            return null;
        return AmericaStayRes.builder()
                .id(entity.getId())
                .stateCode(entity.getStateCode())
                .stateName(entity.getStateName())
                .cityCode(entity.getCityCode())
                .cityName(entity.getCityName())
                .zipCode(entity.getZipCode())
                .address(entity.getAddress())
                .creatorUserId(entity.getCreatorUserId())
                .createDate(Optional.ofNullable(entity.getCreateDate()).map(Date::getTime).orElse(null))
                .modifyDate(Optional.ofNullable(entity.getModifyDate()).map(Date::getTime).orElse(null))
                .modifierUserId(entity.getModifierUserId())
                .build();
    }

    public static List<AmericaStayRes> fromEntities(List<AmericaStay> entities) {
        if (entities == null) {
            return null;
        }
        return entities.stream()
                .map(AmericaStayRes::fromEntity)
                .toList();
    }
}
