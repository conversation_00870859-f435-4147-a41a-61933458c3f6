package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.entity.Code;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class CodeRes {
    private Long id;
    private String code;
    private String name;

    public static CodeRes fromEntity(Code entity) {
        if (entity == null) {
            return null;
        }
        return CodeRes.builder()
                .id(entity.getId())
                .code(entity.getCode())
                .name(entity.getName())
                .build();
    }
}
