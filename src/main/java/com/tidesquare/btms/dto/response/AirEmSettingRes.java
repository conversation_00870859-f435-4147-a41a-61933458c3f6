package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.entity.embeddable.AirEmSetting;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class AirEmSettingRes {
    private boolean reservFront;
    private boolean reservAdmin;
    private boolean reservAgency;
    private boolean ticketFront;
    private boolean ticketAdmin;
    private boolean ticketAgency;
    private boolean completeFront;
    private boolean completeAdmin;
    private boolean approvalFront;
    private boolean approvalAdmin;
    private boolean approvalAgency;
    private boolean rejectFront;
    private boolean rejectAdmin;
    private boolean rejectAgency;

    public static AirEmSettingRes fromEntity(AirEmSetting entity) {
        if (entity == null) {
            return null;
        }
        return AirEmSettingRes.builder()
                .reservFront(entity.isReservFront())
                .reservAdmin(entity.isReservAdmin())
                .reservAgency(entity.isReservAgency())
                .ticketFront(entity.isTicketFront())
                .ticketAdmin(entity.isTicketAdmin())
                .ticketAgency(entity.isTicketAgency())
                .completeFront(entity.isCompleteFront())
                .completeAdmin(entity.isCompleteAdmin())
                .approvalFront(entity.isApprovalFront())
                .approvalAdmin(entity.isApprovalAdmin())
                .approvalAgency(entity.isApprovalAgency())
                .rejectFront(entity.isRejectFront())
                .rejectAdmin(entity.isRejectAdmin())
                .rejectAgency(entity.isRejectAgency())
                .build();
    }
}
