package com.tidesquare.btms.dto.response;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Builder
@Getter
@Setter
public class CompanyAccountRes {
    private Long id;

    private Long companyId;

    private String customType;

    private String sBankCd;         //은행코드

    private String sBankNm;         //은행명

    private String sBankAccnt;      //계좌번호

    private String sDepositors;     //예금주명

    private String fBankCd;         //외국은행코드

    private String fBankNm;         //외국은행명

    private String fBankAccnt;      //외국계좌번호

    private String fBankDepositors; //외국예금주명

    private UserRes creator;

    private Date createDate;
}
