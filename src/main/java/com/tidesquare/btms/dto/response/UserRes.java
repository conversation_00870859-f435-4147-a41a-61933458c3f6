package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.constant.Gender;
import com.tidesquare.btms.constant.JoinAuthType;
import com.tidesquare.btms.constant.JoinStatus;
import com.tidesquare.btms.constant.Role;
import com.tidesquare.btms.entity.User;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@SuperBuilder
public class UserRes {
    // private Country country;
    //
    // private Workspace workspace;
    //
    // private Position position;
    //
    private DepartmentRes department;

    private Long id;

    private String loginId;

    private Role role;

    private String name;

    private Gender gender;

    private String birthday;

    private String cellPhoneNumber;

    private String phoneNumber;

    private String employeeNo;

    private String email;

    // private AttachFile profileImageAttachFile;

    private Date joinDate;

    private JoinAuthType joinAuthType;

    private JoinStatus joinStatus;

    private Date leaveDate;

    private Long leaveProcessUserId;

    private int incorrectPasswordCount;

    private Date passwordUpdateDate;

    private Date nextPasswordUpdateDate;

    private String accountingCode;

    private String skypassMemberNo;

    private String skypassMemberLastNm;

    private Boolean skypassUse;

    private WorkspaceRes workspace;

    public static UserRes fromEntity(User entity) {
        if (entity == null) {
            return null;
        }

        return UserRes.builder()
                .id(entity.getId())
                .loginId(entity.getLoginId())
                .role(entity.getRole())
                .name(entity.getName())
                .gender(entity.getGender())
                .cellPhoneNumber(entity.getCellPhoneNumber())
                .accountingCode(entity.getAccountingCode())
                .birthday(entity.getBirthday())
                .phoneNumber(entity.getPhoneNumber())
                .employeeNo(entity.getEmployeeNo())
                .email(entity.getEmail())
                .joinAuthType(entity.getJoinAuthType())
                .joinDate(entity.getJoinDate())
                .joinStatus(entity.getJoinStatus())
                .leaveDate(entity.getLeaveDate())
                .leaveProcessUserId(entity.getLeaveProcessUserId())
                .incorrectPasswordCount(entity.getIncorrectPasswordCount())
                .passwordUpdateDate(entity.getPasswordUpdateDate())
                .nextPasswordUpdateDate(entity.getNextPasswordUpdateDate())
                .accountingCode(entity.getAccountingCode())
                .skypassMemberLastNm(entity.getSkypassMemberLastNm())
                .skypassMemberNo(entity.getSkypassMemberNo())
                .skypassUse(entity.getSkypassUse())
                .workspace(WorkspaceRes.fromEntity(entity.getWorkspace()))
                .department(DepartmentRes.fromEntity(entity.getDepartment()))
                .build();
    }

    public static List<UserRes> fromEntities(List<User> entities) {
        if (entities == null) {
            return null;
        }

        return entities.stream().map(e -> fromEntity(e)).toList();
    }
}
