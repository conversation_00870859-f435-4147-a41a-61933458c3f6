package com.tidesquare.btms.dto.response;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class CompanyTravelInfoRes {
    private String travelProcess;    //진행방식
    private String majorCountry;    //주요 출장지
    private String majorSeatClass;    //주요 class
    private String preferredAirline; //선호항공사
    private String airlineContract; //항공사 계약
    private String airPaymentMeans;    //지불수단
    private String preferredHotel;    //선호 호텔
    private String roomClass;//투숙규정
    private String hotelPaymentMeans;//지불수단
    private String visaProcess;//진행절차
    private String visaPaymentMeans;//지불수단
}
