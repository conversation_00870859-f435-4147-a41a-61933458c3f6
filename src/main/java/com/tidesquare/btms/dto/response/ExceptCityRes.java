package com.tidesquare.btms.dto.response;

import java.util.List;

import com.tidesquare.btms.entity.ExceptCity;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class ExceptCityRes {
    private Long travelRuleId;
    private Long positionId;
    private Long cityId;
    private String cityCode;
    private String cityName;

    public static ExceptCityRes fromEntity(ExceptCity entity) {
        if (entity == null) {
            return null;
        }

        return ExceptCityRes.builder()
                .travelRuleId(entity.getTravelRuleId())
                .positionId(entity.getPositionId())
                .cityId(entity.getCityId())
                .cityCode(entity.getCityCode())
                .cityName(entity.getCityName())
                .build();
    }

    public static List<ExceptCityRes> fromEntities(List<ExceptCity> entities) {
        if (entities == null) {
            return null;
        }

        return entities.stream()
                .map(ExceptCityRes::fromEntity)
                .toList();
    }
}
