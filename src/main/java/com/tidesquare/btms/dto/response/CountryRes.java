package com.tidesquare.btms.dto.response;

import com.tidesquare.btms.constant.Continent;
import com.tidesquare.btms.entity.Country;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Builder
@Getter
@Setter
public class CountryRes {
    private Long id;
    private String code2;
    private String code3;
    private Continent continent;
    private String name;
    private String nameEng;

    public static CountryRes fromEntity(Country entity) {
        if (entity == null) {
            return null;
        }
        return CountryRes.builder()
                .id(entity.getId())
                .code2(entity.getCode2())
                .code3(entity.getCode2())
                .continent(entity.getContinent())
                .name(entity.getName())
                .nameEng(entity.getNameEng())
                .build();
    }
}
