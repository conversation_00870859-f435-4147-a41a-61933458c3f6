package com.tidesquare.btms.repository;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.CompareScheduleDetail;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@ApplicationScoped
public class CompareScheduleDetailRepo {
    @Inject
    private StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public CompareScheduleDetail insert(CompareScheduleDetail compareScheduleDetail) {
        this.statelessSession.insert(compareScheduleDetail);
        return compareScheduleDetail;
    }
}
