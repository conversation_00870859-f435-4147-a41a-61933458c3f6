package com.tidesquare.btms.repository;

import java.util.List;
import java.util.ArrayList;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.constant.JoinStatus;
import com.tidesquare.btms.constant.UserType;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.Customer;
import com.tidesquare.btms.entity.CustomerPassport;
import com.tidesquare.btms.entity.CustomerPassport_;
import com.tidesquare.btms.entity.Customer_;
import com.tidesquare.btms.entity.Workspace;
import com.tidesquare.btms.entity.Workspace_;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;

@Startup
@ApplicationScoped
public class CustomerRepo {

    @Inject
    private StatelessSession statelessSession;

    @PostConstruct
    void init() {
    }

    public void insert(Customer customer) {
        this.statelessSession.insert(customer);
    }

    public Customer findByCompanyIdAndFirstNameAndLastName(Long companyId, String firstName, String lastName) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Customer> query = builder.createQuery(Customer.class);
        Root<Customer> root = query.from(Customer.class);
        Join<Customer, CustomerPassport> customerPassportJoin = root.join(Customer_.customerPassport, JoinType.INNER);
        Join<Customer, Workspace> workspaceJoin = root.join(Customer_.workspace, JoinType.INNER);
        query.select(root);
        query.where(builder.equal(workspaceJoin.get(Workspace_.company), new Company(companyId)),
                builder.equal(builder.upper(customerPassportJoin.get(CustomerPassport_.lastName)), lastName.toUpperCase()),
                builder.equal(builder.upper(customerPassportJoin.get(CustomerPassport_.firstName)), firstName.toUpperCase()));
        Customer customer = this.statelessSession.createSelectionQuery(query).setMaxResults(1).getSingleResultOrNull();
        return customer;
    }

    public Customer findByCompanyIdAndName(Long companyId, String name) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Customer> query = builder.createQuery(Customer.class);
        Root<Customer> root = query.from(Customer.class);
        Join<Customer, Workspace> workspaceJoin = root.join(Customer_.workspace, JoinType.INNER);
        query.select(root);
        query.where(builder.equal(workspaceJoin.get(Workspace_.company), new Company(companyId)), builder.equal(builder.upper(root.get(Customer_.name)), name.toUpperCase()));
        Customer customer = this.statelessSession.createSelectionQuery(query).setMaxResults(1).getSingleResultOrNull();
        return customer;
    }

    public Customer findOne(Long id) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Customer> query = builder.createQuery(Customer.class);
        Root<Customer> root = query.from(Customer.class);
        query.select(root);
        query.where(builder.equal(root.get(Customer_.id), id));
        Customer customer = this.statelessSession.createSelectionQuery(query).getSingleResultOrNull();
        return customer;
    }

    public Customer findOneFetchWorkspace(Long id) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Customer> query = builder.createQuery(Customer.class);
        Root<Customer> root = query.from(Customer.class);
        root.fetch(Customer_.workspace, JoinType.INNER);
        query.select(root);
        query.where(builder.equal(root.get(Customer_.id), id));
        Customer customer = this.statelessSession.createSelectionQuery(query).getSingleResultOrNull();
        return customer;
    }

    public List<Customer> findActiveAdminByWorkspaceId(Long workspaceId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Customer> query = builder.createQuery(Customer.class);
        Root<Customer> root = query.from(Customer.class);
        query.select(root);
        query.where(builder.equal(root.get(Customer_.workspace), new Workspace(workspaceId)),
                builder.equal(root.get(Customer_.joinStatus), JoinStatus.Approval),
                builder.equal(root.get(Customer_.isAdmin), true));
        List<Customer> customers = this.statelessSession.createSelectionQuery(query).getResultList();
        return customers;
    }

    public Customer findByEmailAndJoinStatus(UserType userType, String email, JoinStatus joinStatus) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Customer> query = builder.createQuery(Customer.class);
        Root<Customer> root = query.from(Customer.class);
        query.select(root);

        List<Predicate> predicates = new ArrayList<>();
        if (userType != null) {
            predicates.add(builder.equal(root.get(Customer_.userType), userType));
        }
        predicates.add(builder.equal(root.get(Customer_.email), email));
        predicates.add(builder.equal(root.get(Customer_.joinStatus), joinStatus));
        query.where(predicates.toArray(new Predicate[0]));

        Customer customer = this.statelessSession.createSelectionQuery(query).setMaxResults(1).getSingleResultOrNull();
        return customer;
    }

    public Customer findFetchWorkspaceByEmailAndJoinStatus(UserType userType, String email, JoinStatus joinStatus) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Customer> query = builder.createQuery(Customer.class);
        Root<Customer> root = query.from(Customer.class);
        root.fetch(Customer_.workspace, JoinType.INNER);
        query.select(root);

        List<Predicate> predicates = new ArrayList<>();
        if (userType != null) {
            predicates.add(builder.equal(root.get(Customer_.userType), userType));
        }
        predicates.add(builder.equal(root.get(Customer_.email), email));
        predicates.add(builder.equal(root.get(Customer_.joinStatus), joinStatus));
        query.where(predicates.toArray(new Predicate[0]));

        Customer customer = this.statelessSession.createSelectionQuery(query).setMaxResults(1).getSingleResultOrNull();
        return customer;
    }

}
