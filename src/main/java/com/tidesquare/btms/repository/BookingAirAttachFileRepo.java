package com.tidesquare.btms.repository;

import java.util.List;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.AttachFile;
import com.tidesquare.btms.entity.BookingAirAttachFile;
import com.tidesquare.btms.entity.BookingAirAttachFile_;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

@Startup
@ApplicationScoped
public class BookingAirAttachFileRepo {
    @Inject
    private StatelessSession statelessSession;

    @PostConstruct
    void init() {
    }

    @Transactional(rollbackOn = Exception.class)
    public BookingAirAttachFile insert(BookingAirAttachFile bookingAirAttachFile) {
        this.statelessSession.insert(bookingAirAttachFile);
        return bookingAirAttachFile;
    }

    public List<AttachFile> getAttachFilesByBookingAirId(Long bookingAirId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<BookingAirAttachFile> query = builder.createQuery(BookingAirAttachFile.class);
        Root<BookingAirAttachFile> root = query.from(BookingAirAttachFile.class);
        root.fetch(BookingAirAttachFile_.attachFile, JoinType.INNER);

        query.select(root);
        query.where(builder.equal(root.get(BookingAirAttachFile_.bookingAirId), bookingAirId));
        query.orderBy(builder.desc(root.get(BookingAirAttachFile_.attachFile)));

        List<BookingAirAttachFile> bookingAirAttachFiles = this.statelessSession.createSelectionQuery(query).getResultList();
        return bookingAirAttachFiles.stream().map(item->item.getAttachFile()).toList();
    }

}
