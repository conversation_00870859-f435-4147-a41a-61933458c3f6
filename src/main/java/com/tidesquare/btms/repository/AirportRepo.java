package com.tidesquare.btms.repository;

import java.util.List;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.Airport;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;

@ApplicationScoped
public class AirportRepo {
    @Inject
    private StatelessSession statelessSession;

    public List<Airport> findAll() {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Airport> query = builder.createQuery(Airport.class);
        Root<Airport> root = query.from(Airport.class);
        query.select(root);

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    public void insert(Airport airport) {
        this.statelessSession.insert(airport);
    }
}
