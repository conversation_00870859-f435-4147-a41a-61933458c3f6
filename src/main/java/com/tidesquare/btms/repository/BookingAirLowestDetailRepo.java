package com.tidesquare.btms.repository;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.BookingAirLowestDetail;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class BookingAirLowestDetailRepo {
    @Inject
    private StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public BookingAirLowestDetail insert(BookingAirLowestDetail bookingAirLowestDetail) {
        this.statelessSession.insert(bookingAirLowestDetail);
        return bookingAirLowestDetail;
    }
}