package com.tidesquare.btms.repository;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.BookingAirLowest;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class BookingAirLowestRepo {
    @Inject
    private StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public BookingAirLowest insert(BookingAirLowest bookingAirLowest) {
        this.statelessSession.insert(bookingAirLowest);
        return bookingAirLowest;
    }
}