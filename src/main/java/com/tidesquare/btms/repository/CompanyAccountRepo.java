package com.tidesquare.btms.repository;

import java.util.List;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.CompanyAccount;
import com.tidesquare.btms.entity.CompanyAccount_;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaDelete;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

@ApplicationScoped
@Startup
public class CompanyAccountRepo {

    @Inject
    private StatelessSession statelessSession;

    @PostConstruct
    public void init() {
    }

    @Transactional(rollbackOn = Exception.class)
    public void insert(CompanyAccount companyMemo) {
        this.statelessSession.insert(companyMemo);
    }

    public List<CompanyAccount> findByCompanyId(Long companyId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<CompanyAccount> query = builder.createQuery(CompanyAccount.class);
        Root<CompanyAccount> root = query.from(CompanyAccount.class);
        query.select(root);
        query.where(builder.equal(root.get(CompanyAccount_.companyId), companyId));
        query.orderBy(builder.asc(root.get(CompanyAccount_.id)));

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    @Transactional(rollbackOn = Exception.class)
    public void deleteAllByCompanyIds(List<Long> companyIds) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaDelete<CompanyAccount> delete = builder.createCriteriaDelete(CompanyAccount.class);

        Root<CompanyAccount> root = delete.from(CompanyAccount.class);

        delete.where(root.get(CompanyAccount_.companyId).in(companyIds));

        this.statelessSession.createMutationQuery(delete).executeUpdate();
    }
}
