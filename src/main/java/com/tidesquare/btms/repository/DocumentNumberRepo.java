package com.tidesquare.btms.repository;

import com.tidesquare.btms.constant.BookingType;
import com.tidesquare.btms.entity.DocumentNumber;
import com.tidesquare.btms.entity.DocumentNumber_;
import com.tidesquare.btms.utils.ListUtil;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaDelete;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

import org.hibernate.StatelessSession;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedSet;
import java.util.TreeSet;

@ApplicationScoped
@Slf4j
public class DocumentNumberRepo {
    @Inject
    private StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public DocumentNumber insert(DocumentNumber documentNumber) {
        this.statelessSession.insert(documentNumber);
        return documentNumber;
    }

    @Transactional(rollbackOn = Exception.class)
    public void deleteByBookingIdAndBookingType(Long bookingId, BookingType bookingType) {
        CriteriaBuilder buidler = this.statelessSession.getCriteriaBuilder();
        CriteriaDelete<DocumentNumber> delete = buidler.createCriteriaDelete(DocumentNumber.class);

        Root<DocumentNumber> root = delete.from(DocumentNumber.class);

        delete.where(buidler.equal(root.get(DocumentNumber_.bookingId), bookingId), buidler.equal(root.get(DocumentNumber_.bookingType), bookingType));

        this.statelessSession.createMutationQuery(delete).executeUpdate();
    }

    public List<DocumentNumber> findByBookingIdAndBookingType(Long bookingId, BookingType bookingType) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<DocumentNumber> query = builder.createQuery(DocumentNumber.class);
        Root<DocumentNumber> root = query.from(DocumentNumber.class);

        query.where(builder.equal(root.get(DocumentNumber_.bookingId), bookingId), builder.equal(root.get(DocumentNumber_.bookingType), bookingType));

        query.select(root);

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    public List<DocumentNumber> getByBookingIdsAndBookingType(List<Long> bookingIds, BookingType bookingType) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<DocumentNumber> query = builder.createQuery(DocumentNumber.class);
        Root<DocumentNumber> root = query.from(DocumentNumber.class);

        query.where(root.get(DocumentNumber_.bookingId).in(bookingIds), builder.equal(root.get(DocumentNumber_.bookingType), bookingType));

        query.select(root);

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    public Map<Long, SortedSet<DocumentNumber>> getMapByBookingIdsAndBookingType(List<Long> bookingIds, BookingType bookingType) {
        List<List<Long>> bookingIdsChunks = ListUtil.chunk(bookingIds, 512);

        Map<Long, SortedSet<DocumentNumber>> documentNumberMap = new HashMap<>();
        for (List<Long> bookingIdsChunk : bookingIdsChunks) {
            List<DocumentNumber> documentNumbers = this.getByBookingIdsAndBookingType(bookingIdsChunk, bookingType);
            for (DocumentNumber documentNumber : documentNumbers) {
                documentNumberMap.computeIfAbsent(documentNumber.getBookingId(), k -> new TreeSet<>()).add(documentNumber);
            }
        }

        return documentNumberMap;
    }

}
