package com.tidesquare.btms.repository;

import java.util.List;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.constant.TravelRuleType;
import com.tidesquare.btms.entity.TravelRule;
import com.tidesquare.btms.entity.TravelRuleAir;
import com.tidesquare.btms.entity.TravelRuleAir_;
import com.tidesquare.btms.entity.TravelRule_;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaDelete;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

@ApplicationScoped
@Startup
public class TravelRuleRepo {

    @Inject
    private StatelessSession statelessSession;

    @PostConstruct
    public void init() {
    }

    public TravelRule findByWorkspaceIdAndTravelRuleType(Long workspaceId, TravelRuleType travelRuleType) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<TravelRule> query = builder.createQuery(TravelRule.class);

        Root<TravelRule> root = query.from(TravelRule.class);

        query.select(root);
        query.where(builder.equal(root.get(TravelRule_.workspaceId), workspaceId), builder.equal(root.get(TravelRule_.travelRuleType), travelRuleType));
        root.join(TravelRule_.exceptCities, JoinType.LEFT);

        return this.statelessSession.createSelectionQuery(query).getSingleResultOrNull();
    }

    public List<TravelRule> findByWorkspaceIds(List<Long> workspaceIds) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<TravelRule> query = builder.createQuery(TravelRule.class);

        Root<TravelRule> root = query.from(TravelRule.class);

        query.select(root);
        query.where(root.get(TravelRule_.workspaceId).in(workspaceIds));
        query.orderBy(builder.asc(root.get(TravelRule_.travelRuleId)));

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    @Transactional(rollbackOn = Exception.class)
    public void deleteByTravelRuleIds(List<Long> travelRuleIds) {
        CriteriaBuilder builder1 = this.statelessSession.getCriteriaBuilder();
        CriteriaDelete<TravelRule> delete1 = builder1.createCriteriaDelete(TravelRule.class);
        Root<TravelRule> root1 = delete1.from(TravelRule.class);
        delete1.where(root1.get(TravelRule_.travelRuleId).in(travelRuleIds));
        this.statelessSession.createMutationQuery(delete1).executeUpdate();

        CriteriaBuilder builder2 = this.statelessSession.getCriteriaBuilder();
        CriteriaDelete<TravelRuleAir> delete2 = builder2.createCriteriaDelete(TravelRuleAir.class);
        Root<TravelRuleAir> root2 = delete2.from(TravelRuleAir.class);
        delete2.where(root2.get(TravelRuleAir_.travelRuleId).in(travelRuleIds));
        this.statelessSession.createMutationQuery(delete2).executeUpdate();

        CriteriaBuilder builder3 = this.statelessSession.getCriteriaBuilder();
        CriteriaDelete<TravelRuleAir> delete3 = builder3.createCriteriaDelete(TravelRuleAir.class);
        Root<TravelRuleAir> root3 = delete3.from(TravelRuleAir.class);
        delete3.where(root3.get(TravelRuleAir_.travelRuleId).in(travelRuleIds));
        this.statelessSession.createMutationQuery(delete3).executeUpdate();
    }
}
