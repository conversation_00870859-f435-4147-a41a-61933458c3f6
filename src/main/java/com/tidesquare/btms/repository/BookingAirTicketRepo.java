package com.tidesquare.btms.repository;

import java.util.List;

import org.hibernate.StatelessSession;
import com.tidesquare.btms.entity.BookingAirTicket;
import com.tidesquare.btms.entity.BookingAirTicket_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;

@ApplicationScoped
public class BookingAirTicketRepo {
    @Inject
    private StatelessSession statelessSession;

    public List<BookingAirTicket> findAllByBookingAirId(Long id) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<BookingAirTicket> query = builder.createQuery(BookingAirTicket.class);
        Root<BookingAirTicket> root = query.from(BookingAirTicket.class);
        query.select(root);
        query.where(builder.equal(root.get(BookingAirTicket_.bookingAirId), id));
        return this.statelessSession.createSelectionQuery(query).getResultList();
    }
}
