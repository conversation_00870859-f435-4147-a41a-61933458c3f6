package com.tidesquare.btms.repository;

import com.tidesquare.btms.entity.TravelerMileageInfo;
import com.tidesquare.btms.entity.TravelerMileageInfo_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaDelete;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

import org.hibernate.StatelessSession;

import java.util.List;

@ApplicationScoped
public class TravelerMileageInfoRepo {
    @Inject
    private StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public TravelerMileageInfo insert(TravelerMileageInfo travelerMileageInfo) {
        this.statelessSession.insert(travelerMileageInfo);
        return travelerMileageInfo;
    }

    @Transactional(rollbackOn = Exception.class)
    public void deleteByBookingAirTravelerId(Long bookingAirTravelerId) {
        CriteriaBuilder cb = this.statelessSession.getCriteriaBuilder();
        CriteriaDelete<TravelerMileageInfo> delete = cb.createCriteriaDelete(TravelerMileageInfo.class);

        Root<TravelerMileageInfo> root = delete.from(TravelerMileageInfo.class);

        delete.where(cb.equal(root.get(TravelerMileageInfo_.bookingAirTravelerId), bookingAirTravelerId));

        this.statelessSession.createMutationQuery(delete).executeUpdate();
    }

    @Transactional(rollbackOn = Exception.class)
    public void deleteByBookingAirTravelerIds(List<Long> bookingAirTravelerIds) {
        CriteriaBuilder cb = this.statelessSession.getCriteriaBuilder();
        CriteriaDelete<TravelerMileageInfo> delete = cb.createCriteriaDelete(TravelerMileageInfo.class);

        Root<TravelerMileageInfo> root = delete.from(TravelerMileageInfo.class);

        delete.where(root.get(TravelerMileageInfo_.bookingAirTravelerId).in(bookingAirTravelerIds));

        this.statelessSession.createMutationQuery(delete).executeUpdate();
    }
}
