package com.tidesquare.btms.repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedSet;
import java.util.TreeSet;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.BookingHotelGuest;
import com.tidesquare.btms.entity.BookingHotelGuest_;
import com.tidesquare.btms.utils.ListUtil;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;

@ApplicationScoped
public class BookingHotelGuestRepo {

    @Inject
    private StatelessSession statelessSession;

    public List<BookingHotelGuest> getByBookingAirIds(List<Long> bookingHotelIds) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<BookingHotelGuest> query = builder.createQuery(BookingHotelGuest.class);
        Root<BookingHotelGuest> root = query.from(BookingHotelGuest.class);

        query.select(root);
        query.where(root.get(BookingHotelGuest_.bookingHotelId).in(bookingHotelIds));

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    public Map<Long, SortedSet<BookingHotelGuest>> getMapByBookingHotelIds(List<Long> bookingHotelIds) {
        List<List<Long>> bookingHotelIdsChunks = ListUtil.chunk(bookingHotelIds, 512);

        Map<Long, SortedSet<BookingHotelGuest>> bookingHotelGuestMap = new HashMap<>();
        for (List<Long> bookingHotelIdsChunk : bookingHotelIdsChunks) {
            List<BookingHotelGuest> bookingHotelGuests = this.getByBookingAirIds(bookingHotelIdsChunk);
            for (BookingHotelGuest bookingHotelGuest : bookingHotelGuests) {
                bookingHotelGuestMap.computeIfAbsent(bookingHotelGuest.getBookingHotelId(), k -> new TreeSet<>()).add(bookingHotelGuest);
            }
        }

        return bookingHotelGuestMap;
    }
}
