package com.tidesquare.btms.repository;

import java.util.List;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.TravelStatusHistory;
import com.tidesquare.btms.entity.TravelStatusHistory_;
import com.tidesquare.btms.entity.User;
import com.tidesquare.btms.entity.User_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Fetch;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class TravelStatusHistoryRepo {
    @Inject
    private StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public TravelStatusHistory insert(TravelStatusHistory travelStatusHistory) {
        this.statelessSession.insert(travelStatusHistory);
        return travelStatusHistory;
    }

    public List<TravelStatusHistory> findAllByTravelId(Long travelId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<TravelStatusHistory> query = builder.createQuery(TravelStatusHistory.class);

        Root<TravelStatusHistory> root = query.from(TravelStatusHistory.class);
        Fetch<TravelStatusHistory, User> userJoin = root.fetch(TravelStatusHistory_.modifier, JoinType.LEFT);
        userJoin.fetch(User_.WORKSPACE, JoinType.LEFT);
        query.where(builder.equal(root.get(TravelStatusHistory_.travelId), travelId));
        query.select(root);

        return this.statelessSession.createQuery(query).getResultList();
    }

}
