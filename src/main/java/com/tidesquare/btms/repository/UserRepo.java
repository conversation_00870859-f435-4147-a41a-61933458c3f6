package com.tidesquare.btms.repository;

import java.util.List;
import java.util.Map;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.User;
import com.tidesquare.btms.entity.User_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.CriteriaUpdate;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class UserRepo {
    @Inject
    private StatelessSession statelessSession;

    public List<User> findByIds(List<Long> ids) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<User> query = builder.createQuery(User.class);
        Root<User> root = query.from(User.class);
        query.select(root);
        query.where(root.get(User_.id).in(ids));

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    public User findById(Long id) {
        return this.statelessSession.get(User.class, id);
    }

    @Transactional(rollbackOn = Exception.class)
    public void update(Long id, Map<String, Object> updateValues) {
        CriteriaBuilder cb = this.statelessSession.getCriteriaBuilder();
        CriteriaUpdate<User> update = cb.createCriteriaUpdate(User.class);

        Root<User> e = update.from(User.class);

        for (Map.Entry<String, Object> entry : updateValues.entrySet()) {
            update.set(entry.getKey(), entry.getValue());
        }

        update.where(cb.equal(e.get(User_.id), id));
        this.statelessSession.createMutationQuery(update).executeUpdate();
    }
}
