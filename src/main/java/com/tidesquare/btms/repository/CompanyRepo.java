package com.tidesquare.btms.repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.constant.CompanyType;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.Company_;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.CriteriaUpdate;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response.Status;

@ApplicationScoped
@Startup
public class CompanyRepo {

    @Inject
    private StatelessSession statelessSession;

    @PostConstruct
    public void init() {
    }

    @Transactional(rollbackOn = Exception.class)
    public void insert(Company company) {
        this.statelessSession.insert(company);
    }

    public Company findById(Long id) {
        return this.statelessSession.get(Company.class, id);
    }

    public Company findById(Long id, List<String> fetches) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Company> query = builder.createQuery(Company.class);
        Root<Company> root = query.from(Company.class);
        if (fetches != null) {
            for (String attribute : fetches) {
                root.fetch(attribute, JoinType.LEFT);
            }
        }

        query.select(root);
        query.where(builder.equal(root.get(Company_.id), id));

        return this.statelessSession.createSelectionQuery(query).getSingleResultOrNull();
    }

    public Company findFetchParentById(Long id) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Company> query = builder.createQuery(Company.class);
        Root<Company> root = query.from(Company.class);
        root.fetch(Company_.parent, JoinType.LEFT);

        query.select(root);
        query.where(builder.equal(root.get(Company_.id), id));

        return this.statelessSession.createSelectionQuery(query).getSingleResultOrNull();
    }

    public Company findTop1ByIsDeletedFalseAndSiteCode(String siteCode) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Company> query = builder.createQuery(Company.class);
        Root<Company> root = query.from(Company.class);

        query.select(root);
        query.where(builder.equal(root.get(Company_.siteCode), siteCode), builder.equal(root.get(Company_.isDeleted), false));

        return this.statelessSession.createSelectionQuery(query).setMaxResults(1).getSingleResultOrNull();
    }

    public Company getNewestGroup() {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Company> query = builder.createQuery(Company.class);
        Root<Company> root = query.from(Company.class);

        query.select(root);
        query.where(builder.equal(root.get(Company_.isGroup), true), builder.equal(root.get(Company_.isDeleted), false));
        query.orderBy(builder.desc(root.get(Company_.id)));

        return this.statelessSession.createSelectionQuery(query).setMaxResults(1).getSingleResultOrNull();
    }

    public String getNextSiteCode(CompanyType companyType) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Company> query = builder.createQuery(Company.class);
        Root<Company> root = query.from(Company.class);

        query.select(root);

        List<Predicate> predicates = new ArrayList<>();
        predicates.add(builder.equal(root.get(Company_.isGroup), false));
        predicates.add(builder.equal(root.get(Company_.isDeleted), false));
        if (companyType.equals(CompanyType.CORP)) {
            predicates.add(builder.greaterThanOrEqualTo(root.get(Company_.siteCode), "A01"));
            predicates.add(builder.lessThanOrEqualTo(root.get(Company_.siteCode), "O99"));
        } else if (companyType.equals(CompanyType.COMM)) {
            predicates.add(builder.greaterThanOrEqualTo(root.get(Company_.siteCode), "S01"));
            predicates.add(builder.lessThanOrEqualTo(root.get(Company_.siteCode), "Z99"));
        } else if (companyType.equals(CompanyType.PAYM)) {
            predicates.add(builder.greaterThanOrEqualTo(root.get(Company_.siteCode), "P01"));
            predicates.add(builder.lessThanOrEqualTo(root.get(Company_.siteCode), "R99"));
        } else {
            throw new ApiException(Status.BAD_REQUEST, ErrorCode.E400);
        }
        query.where(predicates.toArray(new Predicate[0]));
        query.orderBy(builder.desc(root.get(Company_.SITE_CODE)));

        Company company = this.statelessSession.createSelectionQuery(query).setMaxResults(1).getSingleResultOrNull();
        if (company == null) {
            if (companyType.equals(CompanyType.CORP)) {
                return "A01";
            } else if (companyType.equals(CompanyType.COMM)) {
                return "S01";
            } else if (companyType.equals(CompanyType.PAYM)) {
                return "P01";
            } else {
                throw new ApiException(Status.BAD_REQUEST, ErrorCode.E400);
            }
        }

        String prevSiteCode = company.getSiteCode();
        String prevSymbol = prevSiteCode.substring(0, 1);
        int prevIndex = Integer.parseInt(prevSiteCode.substring(1, 3));

        int nextIndex = prevIndex + 1;
        String nextSymbol = prevSymbol;
        if (nextIndex == 100) {
            nextIndex = 01;
            nextSymbol = String.valueOf((char) (prevSymbol.charAt(0) + 1));
        }

        return nextSymbol + String.format("%02d", nextIndex);
    }

    @Transactional(rollbackOn = Exception.class)
    public void updateParentId(Long parentId, List<Long> childrenIds) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaUpdate<Company> update = builder.createCriteriaUpdate(Company.class);
        Root<Company> root = update.from(Company.class);

        update.set(Company_.PARENT, new Company(parentId));

        update.where(root.get(Company_.id).in(childrenIds), builder.equal(root.get(Company_.isGroup), false), builder.isNull(root.get(Company_.parent)));
        this.statelessSession.createMutationQuery(update).executeUpdate();
    }

    @Transactional(rollbackOn = Exception.class)
    public void deleteAllChildren(Long parentId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaUpdate<Company> update = builder.createCriteriaUpdate(Company.class);
        Root<Company> root = update.from(Company.class);

        update.set(Company_.PARENT, null);

        update.where(builder.equal(root.get(Company_.parent), new Company(parentId)));
        this.statelessSession.createMutationQuery(update).executeUpdate();
    }

    public List<Company> findByParentId(Long parentId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Company> query = builder.createQuery(Company.class);
        Root<Company> root = query.from(Company.class);

        query.select(root);
        query.where(builder.equal(root.get(Company_.parent), new Company(parentId)));
        query.orderBy(builder.desc(root.get(Company_.id)));

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    @Transactional(rollbackOn = Exception.class)
    public void update(List<Long> companyIds, Map<String, Object> updateValues) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaUpdate<Company> update = builder.createCriteriaUpdate(Company.class);

        Root<Company> root = update.from(Company.class);

        for (Map.Entry<String, Object> entry : updateValues.entrySet()) {
            switch (entry.getKey()) {
            case Company_.BUSINESS_NUM:
            case Company_.COMPANY_FAX_NO:
            case Company_.COMPANY_TEL_NO:
                update.set(entry.getKey(), builder.function("doc_encrypt", String.class, builder.literal(entry.getValue())));
                break;
            default:
                update.set(entry.getKey(), entry.getValue());
                break;
            }
        }

        update.where(root.get(Company_.id).in(companyIds));
        this.statelessSession.createMutationQuery(update).executeUpdate();
    }

    @Transactional(rollbackOn = Exception.class)
    public void update(Long companyId, Map<String, Object> updateValues) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaUpdate<Company> update = builder.createCriteriaUpdate(Company.class);

        Root<Company> root = update.from(Company.class);

        for (Map.Entry<String, Object> entry : updateValues.entrySet()) {
            switch (entry.getKey()) {
            case Company_.BUSINESS_NUM:
            case Company_.COMPANY_FAX_NO:
            case Company_.COMPANY_TEL_NO:
                update.set(entry.getKey(), builder.function("doc_encrypt", String.class, builder.literal(entry.getValue())));
                break;
            default:
                update.set(entry.getKey(), entry.getValue());
                break;
            }
        }

        update.where(builder.equal(root.get(Company_.id), companyId));
        this.statelessSession.createMutationQuery(update).executeUpdate();
    }
}
