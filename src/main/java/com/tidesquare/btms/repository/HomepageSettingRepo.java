package com.tidesquare.btms.repository;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.HomepageSetting;
import com.tidesquare.btms.entity.HomepageSetting_;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

@ApplicationScoped
@Startup
public class HomepageSettingRepo {

    @Inject
    private StatelessSession statelessSession;

    @PostConstruct
    public void init() {
    }

    @Transactional(rollbackOn = Exception.class)
    public void insertOrUpdate(HomepageSetting homepageSetting) {
        String sql = """
                INSERT INTO homepagesetting (companyId, isUseDefaultLogo, loginLogoAttachFileId, gnbLogoAttachFileId)
                VALUES (:companyId, :isUseDefaultLogo, :loginLogoAttachFileId, :gnbLogoAttachFileId)
                ON DUPLICATE KEY UPDATE
                    isUseDefaultLogo = :isUseDefaultLogo,
                    loginLogoAttachFileId = :loginLogoAttachFileId,
                    gnbLogoAttachFileId = :gnbLogoAttachFileId
                """;
        this.statelessSession.createNativeMutationQuery(sql)
                .setParameter("companyId", homepageSetting.getCompanyId())
                .setParameter("isUseDefaultLogo", homepageSetting.isUseDefaultLogo())
                .setParameter("loginLogoAttachFileId", homepageSetting.getLoginLogoAttachFile() == null ? null : homepageSetting.getLoginLogoAttachFile().getId())
                .setParameter("gnbLogoAttachFileId", homepageSetting.getGnbLogoAttachFile() == null ? null : homepageSetting.getGnbLogoAttachFile().getId())
                .executeUpdate();
    }

    public HomepageSetting findByCompanyId(Long companyId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<HomepageSetting> query = builder.createQuery(HomepageSetting.class);
        Root<HomepageSetting> root = query.from(HomepageSetting.class);
        query.select(root);
        query.where(builder.equal(root.get(HomepageSetting_.companyId), companyId));

        HomepageSetting homepageSetting = this.statelessSession.createSelectionQuery(query).getSingleResultOrNull();
        if (homepageSetting == null) {
            homepageSetting = new HomepageSetting();
            homepageSetting.setCompanyId(companyId);
            this.insertOrUpdate(homepageSetting);
        }

        return homepageSetting;
    }

}
