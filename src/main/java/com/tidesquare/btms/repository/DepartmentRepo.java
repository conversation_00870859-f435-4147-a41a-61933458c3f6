package com.tidesquare.btms.repository;

import com.tidesquare.btms.entity.Department;
import com.tidesquare.btms.entity.Department_;
import com.tidesquare.btms.entity.Workspace_;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

import java.util.List;

import org.hibernate.StatelessSession;

@ApplicationScoped
@Startup
public class DepartmentRepo {

    @Inject
    private StatelessSession statelessSession;

    @PostConstruct
    public void init() {
    }

    @Transactional(rollbackOn = Exception.class)
    public void insert(Department department) {
        this.statelessSession.insert(department);
    }

    public List<Department> findByWorkspaceId(Long workspaceId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Department> query = builder.createQuery(Department.class);

        Root<Department> root = query.from(Department.class);

        query.select(root);
        query.where(builder.equal(root.get(Department_.workspace).get(Workspace_.id), workspaceId),
                builder.equal(root.get(Department_.isDeleted), false),
                builder.equal(root.get(Department_.isUse), true));
        query.orderBy(builder.asc(root.get(Department_.id)));

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    public List<Department> findByWorkspaceIdAndParentDepartmentId(Long workspaceId, Long parentDepartmentId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Department> query = builder.createQuery(Department.class);

        Root<Department> root = query.from(Department.class);

        query.select(root);
        query.where(builder.equal(root.get(Department_.workspace).get(Workspace_.id), workspaceId),
                builder.equal(root.get(Department_.parentId), parentDepartmentId),
                builder.equal(root.get(Department_.isDeleted), false),
                builder.equal(root.get(Department_.isUse), true));
        query.orderBy(builder.asc(root.get(Department_.id)));

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    public List<Department> findRecursiveByWorkspaceIdAndParentDepartmentId(Long workspaceId, Long parentDepartmentId) {
        String sql = """
                    WITH RECURSIVE department_tree AS (
                        SELECT * FROM department WHERE workspaceId = :workspaceId AND departmentId = :parentDepartmentId AND isUse = 1 AND isDeleted = 0
                        UNION ALL
                        SELECT d.* FROM department d JOIN department_tree dt ON d.parentId = dt.departmentId WHERE d.workspaceId = :workspaceId AND d.isUse = 1 AND d.isDeleted = 0
                    )
                    SELECT * FROM department_tree
                """;
        return this.statelessSession.createNativeQuery(sql, Department.class)
                .setParameter("workspaceId", workspaceId)
                .setParameter("parentDepartmentId", parentDepartmentId)
                .getResultList();
    }
}
