package com.tidesquare.btms.repository;

import com.tidesquare.btms.entity.Traveler;
import com.tidesquare.btms.entity.Traveler_;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaDelete;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.CriteriaUpdate;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

import org.hibernate.StatelessSession;

import java.util.List;

@Startup
@ApplicationScoped
public class TravelerRepo {
    @Inject
    private StatelessSession statelessSession;

    @PostConstruct
    void init() {
    }

    @Transactional(rollbackOn = Exception.class)
    public void insert(Traveler traveler) {
        this.statelessSession.insert(traveler);
    }

    public Traveler findByTravelIdAndIsReserverTrue(Long travelId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Traveler> query = builder.createQuery(Traveler.class);
        Root<Traveler> root = query.from(Traveler.class);
        query.select(root);
        query.where(builder.equal(root.get(Traveler_.isReserver), true), builder.equal(root.get(Traveler_.travelId), travelId));
        return this.statelessSession.createSelectionQuery(query).setMaxResults(1).getSingleResultOrNull();
    }

    public boolean checkUserIsReverserOrTravelerOfTravel(Long travelId, Long travelerUserId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Traveler> query = builder.createQuery(Traveler.class);
        Root<Traveler> root = query.from(Traveler.class);
        query.select(root);
        query.where(builder.equal(root.get(Traveler_.travelId), travelId), builder.equal(root.get(Traveler_.travelerUserId), travelerUserId));
        return this.statelessSession.createSelectionQuery(query).setMaxResults(1).getSingleResultOrNull() != null;
    }

    public List<Traveler> findByTravelId(Long travelId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Traveler> query = builder.createQuery(Traveler.class);
        Root<Traveler> root = query.from(Traveler.class);
        query.select(root);
        query.where(builder.equal(root.get(Traveler_.travelId), travelId));
        query.orderBy(builder.desc(root.get(Traveler_.isReserver)), builder.asc(root.get(Traveler_.id)));

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    @Transactional(rollbackOn = Exception.class)
    public void deleteByTravelId(Long travelId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaDelete<Traveler> delete = builder.createCriteriaDelete(Traveler.class);
        Root<Traveler> root = delete.from(Traveler.class);
        delete.where(builder.equal(root.get(Traveler_.travelId), travelId));
        this.statelessSession.createMutationQuery(delete).executeUpdate();
    }

    @Transactional(rollbackOn = Exception.class)
    public void removeReserver(Long travelId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaUpdate<Traveler> update = builder.createCriteriaUpdate(Traveler.class);
        Root<Traveler> root = update.from(Traveler.class);
        update.set(Traveler_.TRAVELER_USER_ID, null);
        update.set(Traveler_.ACCOUNTING_CODE, null);
        update.where(builder.equal(root.get(Traveler_.isReserver), true), builder.equal(root.get(Traveler_.travelId), travelId));
        this.statelessSession.createMutationQuery(update).executeUpdate();
    }

    @Transactional(rollbackOn = Exception.class)
    public void updateReserver(Long travelId, Long travelerUserId, String name, String email, String accountingCode, String cellPhoneNumber) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaUpdate<Traveler> update = builder.createCriteriaUpdate(Traveler.class);
        Root<Traveler> root = update.from(Traveler.class);
        update.set(Traveler_.TRAVELER_USER_ID, travelerUserId);
        update.set(Traveler_.NAME, name);
        update.set(Traveler_.EMAIL, builder.function("doc_encrypt", String.class, builder.literal(email)));
        update.set(Traveler_.ACCOUNTING_CODE, accountingCode);
        update.set(Traveler_.CELL_PHONE_NUMBER, builder.function("doc_encrypt", String.class, builder.literal(cellPhoneNumber)));
        update.where(builder.equal(root.get(Traveler_.isReserver), true), builder.equal(root.get(Traveler_.travelId), travelId));
        this.statelessSession.createMutationQuery(update).executeUpdate();
    }
}
