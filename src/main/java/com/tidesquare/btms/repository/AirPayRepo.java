package com.tidesquare.btms.repository;

import com.tidesquare.btms.entity.*;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.*;
import org.hibernate.StatelessSession;

import java.util.List;

@ApplicationScoped
public class AirPayRepo {
    @Inject
    private StatelessSession statelessSession;

    public AirPayInfo findByTravelIdAndBookingAirTicketIdAndIsUseTrue(Long travelId, Long bookingAirTicketId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();

        CriteriaQuery<AirPayInfo> query = builder.createQuery(AirPayInfo.class);

        Root<AirPayInfo> root = query.from(AirPayInfo.class);

        Predicate travelIdPredicate = builder.equal(root.get(AirPayInfo_.travelId), travelId);
        Predicate bookingAirIdPredicate = builder.equal(root.get(AirPayInfo_.bookingAirTicketId), bookingAirTicketId);
        Predicate isUseTruePredicate = builder.isTrue(root.get(AirPayInfo_.isUse));

        query.where(travelIdPredicate, bookingAirIdPredicate, isUseTruePredicate);

        query.select(root);

        List<AirPayInfo> resultList = this.statelessSession.createQuery(query).getResultList();
        if (!resultList.isEmpty()) {
            return resultList.get(0);
        } else {
            return null;
        }
    }

    public List<AirPayInfo> findByTravelIdAndIsUseTrue(Long travelId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<AirPayInfo> query = builder.createQuery(AirPayInfo.class);

        Root<AirPayInfo> root = query.from(AirPayInfo.class);
        query.where(builder.equal(root.get(AirPayInfo_.travelId), travelId), builder.isTrue(root.get(AirPayInfo_.isUse)));
        query.select(root);

        return this.statelessSession.createQuery(query).getResultList();
    }

}
