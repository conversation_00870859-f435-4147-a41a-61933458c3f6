package com.tidesquare.btms.repository;

import com.tidesquare.btms.entity.*;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.*;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import org.hibernate.StatelessSession;

@ApplicationScoped
@Slf4j
@Startup
public class TravelRepo {
    @Inject
    private StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public Travel insert(Travel travel) {
        this.statelessSession.insert(travel);
        return travel;
    }

    @PostConstruct
    public void init() {
    }

    public Travel findById(Long travelId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Travel> query = builder.createQuery(Travel.class);

        Root<Travel> travelRoot = query.from(Travel.class);

        Fetch<Travel, BookingAir> bookingAirJoin = travelRoot.fetch(Travel_.bookingAir, JoinType.INNER);
        bookingAirJoin.fetch(BookingAir_.bookingAirSchedules, JoinType.LEFT);
        bookingAirJoin.fetch(BookingAir_.bookingAirLowest, JoinType.LEFT);

        Fetch<BookingAir, BookingAirTraveler> bookingAirTravelersJoin = bookingAirJoin.fetch(BookingAir_.bookingAirTravelers, JoinType.LEFT);
        bookingAirTravelersJoin.fetch(BookingAirTraveler_.travelerMileageInfos, JoinType.LEFT);

        query.where(builder.equal(travelRoot.get(Travel_.id), travelId));

        query.select(travelRoot);

        return this.statelessSession.createSelectionQuery(query).getSingleResultOrNull();
    }

    @Transactional(rollbackOn = Exception.class)
    public void update(Long travelId, Map<String, Object> updateValues) {
        CriteriaBuilder cb = this.statelessSession.getCriteriaBuilder();
        CriteriaUpdate<Travel> update = cb.createCriteriaUpdate(Travel.class);

        Root<Travel> e = update.from(Travel.class);

        for (Map.Entry<String, Object> entry : updateValues.entrySet()) {
            update.set(entry.getKey(), entry.getValue());
        }

        update.where(cb.equal(e.get(Travel_.id), travelId));
        this.statelessSession.createMutationQuery(update).executeUpdate();
    }

    @Transactional(rollbackOn = Exception.class)
    public void update(Travel travel) {
        this.statelessSession.update(travel);
    }

    public Travel findSingleTravel(Long id) {
        return this.statelessSession.get(Travel.class, id);
    }

    public Travel findFetchBookingAirById(Long travelId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Travel> query = builder.createQuery(Travel.class);

        Root<Travel> travelRoot = query.from(Travel.class);

        travelRoot.fetch(Travel_.bookingAir, JoinType.LEFT);

        query.where(builder.equal(travelRoot.get(Travel_.id), travelId));

        query.select(travelRoot);

        return this.statelessSession.createSelectionQuery(query).getSingleResultOrNull();
    }

    public Travel findFetchBookingAirAndBookingAirTicketById(Long travelId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Travel> query = builder.createQuery(Travel.class);

        Root<Travel> travelRoot = query.from(Travel.class);

        Fetch<Travel, BookingAir> bookingAirJoin = travelRoot.fetch(Travel_.bookingAir, JoinType.INNER);
        bookingAirJoin.fetch(BookingAir_.bookingAirTickets, JoinType.LEFT);

        query.where(builder.equal(travelRoot.get(Travel_.id), travelId));

        query.select(travelRoot);

        return this.statelessSession.createSelectionQuery(query).getSingleResultOrNull();
    }

    public Travel findFetchBookingAirAndBookingAirScheduleAndBookingAirTravelerById(Long travelId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Travel> query = builder.createQuery(Travel.class);

        Root<Travel> travelRoot = query.from(Travel.class);

        Fetch<Travel, BookingAir> bookingAirJoin = travelRoot.fetch(Travel_.bookingAir, JoinType.INNER);
        bookingAirJoin.fetch(BookingAir_.BOOKING_AIR_SCHEDULES, JoinType.LEFT);
        bookingAirJoin.fetch(BookingAir_.BOOKING_AIR_TRAVELERS, JoinType.LEFT);

        query.where(builder.equal(travelRoot.get(Travel_.id), travelId));

        query.select(travelRoot);

        return this.statelessSession.createSelectionQuery(query).getSingleResultOrNull();
    }
}
