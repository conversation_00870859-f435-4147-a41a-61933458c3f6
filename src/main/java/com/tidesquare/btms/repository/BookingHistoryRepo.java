package com.tidesquare.btms.repository;

import com.tidesquare.btms.entity.BookingHistory;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.hibernate.StatelessSession;

@ApplicationScoped
public class BookingHistoryRepo {
    @Inject
    private StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public BookingHistory insert(BookingHistory bookingHistory) {
        this.statelessSession.insert(bookingHistory);
        return bookingHistory;
    }
}
