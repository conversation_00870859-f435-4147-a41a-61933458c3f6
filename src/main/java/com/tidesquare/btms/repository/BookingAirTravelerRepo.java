package com.tidesquare.btms.repository;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.BookingAirTraveler;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import com.tidesquare.btms.entity.BookingAirTraveler_;

import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaDelete;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.CriteriaUpdate;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Root;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedSet;
import java.util.TreeSet;

@ApplicationScoped
public class BookingAirTravelerRepo {
    @Inject
    StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public BookingAirTraveler insert(BookingAirTraveler bookingAirTraveler) {
        this.statelessSession.insert(bookingAirTraveler);
        return bookingAirTraveler;
    }

    public List<BookingAirTraveler> findByBookingAirId(Long bookingAirId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<BookingAirTraveler> query = builder.createQuery(BookingAirTraveler.class);
        Root<BookingAirTraveler> root = query.from(BookingAirTraveler.class);

        query.select(root).where(builder.equal(root.get(BookingAirTraveler_.bookingAirId), bookingAirId));
        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    public List<BookingAirTraveler> findFetchTravelerMileageInfosByBookingAirId(Long bookingAirId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<BookingAirTraveler> query = builder.createQuery(BookingAirTraveler.class);
        Root<BookingAirTraveler> root = query.from(BookingAirTraveler.class);
        root.fetch(BookingAirTraveler_.travelerMileageInfos, JoinType.LEFT);

        query.select(root).where(builder.equal(root.get(BookingAirTraveler_.bookingAirId), bookingAirId));
        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    public List<BookingAirTraveler> findByBookingAirIds(List<Long> bookingAirIds) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<BookingAirTraveler> query = builder.createQuery(BookingAirTraveler.class);
        Root<BookingAirTraveler> root = query.from(BookingAirTraveler.class);

        query.select(root).where(root.get(BookingAirTraveler_.bookingAirId).in(bookingAirIds));
        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    public Map<Long, SortedSet<BookingAirTraveler>> getBookingAirTravelersByBookingAirIds(List<Long> bookingAirIds) {
        List<BookingAirTraveler> bookingAirTravelers = this.findByBookingAirIds(bookingAirIds);

        Map<Long, SortedSet<BookingAirTraveler>> bookingAirTravelerMap = new HashMap<>();
        for (BookingAirTraveler bookingAirTraveler : bookingAirTravelers) {
            bookingAirTravelerMap.computeIfAbsent(bookingAirTraveler.getBookingAirId(), k -> new TreeSet<>()).add(bookingAirTraveler);
        }
        return bookingAirTravelerMap;
    }

    @Transactional(rollbackOn = Exception.class)
    public void delete(Long id) {
        CriteriaBuilder cb = this.statelessSession.getCriteriaBuilder();
        CriteriaDelete<BookingAirTraveler> delete = cb.createCriteriaDelete(BookingAirTraveler.class);

        Root<BookingAirTraveler> e = delete.from(BookingAirTraveler.class);

        delete.where(cb.equal(e.get(BookingAirTraveler_.id), id));

        this.statelessSession.createMutationQuery(delete).executeUpdate();
    }

    @Transactional(rollbackOn = Exception.class)
    public void delete(List<Long> ids) {
        CriteriaBuilder cb = this.statelessSession.getCriteriaBuilder();
        CriteriaDelete<BookingAirTraveler> delete = cb.createCriteriaDelete(BookingAirTraveler.class);

        Root<BookingAirTraveler> e = delete.from(BookingAirTraveler.class);

        delete.where(e.get(BookingAirTraveler_.id).in(ids));

        this.statelessSession.createMutationQuery(delete).executeUpdate();
    }

    @Transactional(rollbackOn = Exception.class)
    public void update(BookingAirTraveler bookingAirTraveler) {
        this.statelessSession.update(bookingAirTraveler);
    }

    @Transactional(rollbackOn = Exception.class)
    public void update(List<Long> ids, Map<String, Object> updateValue) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaUpdate<BookingAirTraveler> update = builder.createCriteriaUpdate(BookingAirTraveler.class);

        Root<BookingAirTraveler> e = update.from(BookingAirTraveler.class);

        for (Map.Entry<String, Object> entry : updateValue.entrySet()) {
            update.set(entry.getKey(), entry.getValue());
        }

        update.where(e.get(BookingAirTraveler_.id).in(ids));
        this.statelessSession.createMutationQuery(update).executeUpdate();
    }
}
