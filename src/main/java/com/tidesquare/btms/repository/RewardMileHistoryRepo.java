package com.tidesquare.btms.repository;

import java.util.ArrayList;
import java.util.List;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.constant.RewardMileHistoryType;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.RewardMileHistory;
import com.tidesquare.btms.entity.RewardMileHistory_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class RewardMileHistoryRepo {
  @Inject
  private StatelessSession statelessSession;

  @Transactional(rollbackOn = Exception.class)
  public void createRewardMileHistory(RewardMileHistory rewardMileHistory) {
    this.statelessSession.insert(rewardMileHistory);
  }

  public RewardMileHistory findLastRewardMileHistory(Long companyId) {
    CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
    CriteriaQuery<RewardMileHistory> query = builder.createQuery(RewardMileHistory.class);
    Root<RewardMileHistory> root = query.from(RewardMileHistory.class);
    query.select(root);
    query.where(builder.equal(root.get(RewardMileHistory_.COMPANY), new Company(companyId)));
    query.orderBy(builder.desc(root.get(RewardMileHistory_.USED_DATE)));
    return this.statelessSession.createQuery(query).getResultList().size() > 0
        ? this.statelessSession.createQuery(query).getResultList().get(0)
        : null;
  }

  public List<RewardMileHistory> getListRewardHistories(Long companyId, RewardMileHistoryType type) {
    CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
    CriteriaQuery<RewardMileHistory> query = builder.createQuery(RewardMileHistory.class);
    Root<RewardMileHistory> root = query.from(RewardMileHistory.class);
    root.fetch(RewardMileHistory_.USER, JoinType.LEFT);
    query.select(root);

    List<Predicate> condition = new ArrayList<>();
    condition.add(builder.equal(root.get(RewardMileHistory_.COMPANY), new Company(companyId)));

    if (RewardMileHistoryType.ADDED == type) {
      condition.add(builder.greaterThan(root.get(RewardMileHistory_.USED_MILES), 0));
    } else if (RewardMileHistoryType.USED == type) {
      condition.add(builder.lessThan(root.get(RewardMileHistory_.USED_MILES), 0));
    }

    query.where(condition.toArray(new Predicate[0]));
    query.orderBy(builder.desc(root.get(RewardMileHistory_.USED_DATE)));

    return this.statelessSession.createQuery(query).getResultList();
  }

}
