package com.tidesquare.btms.repository;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.constant.CancelBookingIndexType;
import com.tidesquare.btms.entity.CancelBookingIndex;
import com.tidesquare.btms.entity.CancelBookingIndex_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.CriteriaUpdate;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@ApplicationScoped
public class CancelBookingIndexRepo {
  @Inject
  private StatelessSession statelessSession;

  public Long getIndexValue(CancelBookingIndexType type){
    CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
    CriteriaQuery<CancelBookingIndex> query = builder.createQuery(CancelBookingIndex.class);

    Root<CancelBookingIndex> root = query.from(CancelBookingIndex.class);

    query.where(builder.equal(root.get(CancelBookingIndex_.type), type));
    query.select(root);

    return this.statelessSession.createSelectionQuery(query).getSingleResult().getIndexValue();
  }

  @Transactional(rollbackOn = Exception.class)
  public void insertOrUpdate(CancelBookingIndex cancelBookingIndex){
    if(this.getIndexValue(cancelBookingIndex.getType()) == null){
      this.insert(cancelBookingIndex);
    }else{
      this.update(cancelBookingIndex);
    }
  }

  private void insert(CancelBookingIndex cancelBookingIndex){
    this.statelessSession.insert(cancelBookingIndex);
  }

  private void update(CancelBookingIndex cancelBookingIndex){
    CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
    CriteriaUpdate<CancelBookingIndex> update = builder.createCriteriaUpdate(CancelBookingIndex.class);
    Root<CancelBookingIndex> root = update.from(CancelBookingIndex.class);

    update.set(root.get(CancelBookingIndex_.indexValue), cancelBookingIndex.getIndexValue());
    update.where(builder.equal(root.get(CancelBookingIndex_.type), cancelBookingIndex.getType()));

    this.statelessSession.createMutationQuery(update).executeUpdate();
  }
}
