package com.tidesquare.btms.repository;

import java.util.List;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.Airline;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;

@ApplicationScoped
public class AirlineRepo {
    @Inject
    private StatelessSession statelessSession;

    public List<Airline> findAll() {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Airline> query = builder.createQuery(Airline.class);
        Root<Airline> root = query.from(Airline.class);
        query.select(root);

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    public void insert(Airline airline) {
        this.statelessSession.insert(airline);
    }

}
