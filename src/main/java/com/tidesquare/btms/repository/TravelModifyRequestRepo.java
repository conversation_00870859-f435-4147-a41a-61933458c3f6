package com.tidesquare.btms.repository;

import java.util.List;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.TravelModifyRequest;
import com.tidesquare.btms.entity.TravelModifyRequest_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;

@ApplicationScoped
public class TravelModifyRequestRepo {
    @Inject
    private StatelessSession statelessSession;

    public List<TravelModifyRequest> findByTravelId(Long travelId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<TravelModifyRequest> query = builder.createQuery(TravelModifyRequest.class);
        Root<TravelModifyRequest> root = query.from(TravelModifyRequest.class);
        query.select(root);
        query.where(builder.equal(root.get(TravelModifyRequest_.travelId), travelId));

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }
}
