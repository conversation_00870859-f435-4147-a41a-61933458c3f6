package com.tidesquare.btms.repository;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.BookingAirLowestFlight;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class BookingAirLowestFlightRepo {
    @Inject
    private StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public BookingAirLowestFlight insert(BookingAirLowestFlight bookingAirLowestFlight) {
        this.statelessSession.insert(bookingAirLowestFlight);
        return bookingAirLowestFlight;
    }
}