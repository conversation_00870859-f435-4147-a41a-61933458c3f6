package com.tidesquare.btms.repository;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.AmericaStay;
import com.tidesquare.btms.entity.AmericaStay_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;

@ApplicationScoped
public class AmericaStayRepo {
    @Inject
    private StatelessSession statelessSession;

    public AmericaStay findByTravelId(Long travelId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<AmericaStay> query = builder.createQuery(AmericaStay.class);
        Root<AmericaStay> root = query.from(AmericaStay.class);
        query.select(root);
        query.where(builder.equal(root.get(AmericaStay_.travelId), travelId));

        return this.statelessSession.createSelectionQuery(query).setMaxResults(1).getSingleResultOrNull();
    }
}
