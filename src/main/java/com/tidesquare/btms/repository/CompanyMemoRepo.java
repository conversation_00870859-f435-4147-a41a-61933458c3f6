package com.tidesquare.btms.repository;

import java.util.List;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.CompanyMemo;
import com.tidesquare.btms.entity.CompanyMemo_;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaDelete;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

@ApplicationScoped
@Startup
public class CompanyMemoRepo {

    @Inject
    private StatelessSession statelessSession;

    @PostConstruct
    public void init() {
    }

    @Transactional(rollbackOn = Exception.class)
    public void insert(CompanyMemo companyMemo) {
        this.statelessSession.insert(companyMemo);
    }

    public List<CompanyMemo> findByCompanyId(Long companyId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<CompanyMemo> query = builder.createQuery(CompanyMemo.class);
        Root<CompanyMemo> root = query.from(CompanyMemo.class);
        query.select(root);
        query.where(builder.equal(root.get(CompanyMemo_.companyId), companyId));
        query.orderBy(builder.asc(root.get(CompanyMemo_.id)));

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    @Transactional(rollbackOn = Exception.class)
    public void deleteAllByCompanyIds(List<Long> companyIds) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaDelete<CompanyMemo> delete = builder.createCriteriaDelete(CompanyMemo.class);

        Root<CompanyMemo> root = delete.from(CompanyMemo.class);

        delete.where(root.get(CompanyMemo_.companyId).in(companyIds));

        this.statelessSession.createMutationQuery(delete).executeUpdate();
    }
}
