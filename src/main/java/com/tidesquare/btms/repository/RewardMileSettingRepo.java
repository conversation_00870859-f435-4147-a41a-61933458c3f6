package com.tidesquare.btms.repository;

import java.util.List;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.RewardMileSetting;
import com.tidesquare.btms.entity.RewardMileSetting_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaDelete;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class RewardMileSettingRepo {
  @Inject
  private StatelessSession statelessSession;

  public RewardMileSetting get(Long companyId) {
    RewardMileSetting res = this.statelessSession.get(RewardMileSetting.class, companyId);
    if (res == null) {
      res = RewardMileSetting.builder().companyId(companyId).build();
      this.insertOrUpdate(res);
    }
    return res;
  }

  @Transactional(rollbackOn = Exception.class)
  public void insertOrUpdate(RewardMileSetting rewardMileSetting) {
    String sql = """
            INSERT INTO rewardmilesetting (
                companyId,
                ratio,
                rewardMileType,
                maximumAccumulatedMiles,
                isUse
            ) VALUES (
                :companyId,
                :ratio,
                :rewardMileType,
                NULLIF(:maximumAccumulatedMiles, null),
                :isUse
            )
            ON DUPLICATE KEY UPDATE
                ratio = VALUES(ratio),
                rewardMileType = VALUES(rewardMileType),
                maximumAccumulatedMiles = NULLIF(:maximumAccumulatedMiles, null),
                isUse = VALUES(isUse)
        """;

    this.statelessSession.createNativeMutationQuery(sql)
        .setParameter("companyId", rewardMileSetting.getCompanyId())
        .setParameter("ratio", rewardMileSetting.getRatio())
        .setParameter("rewardMileType", rewardMileSetting.getRewardMileType().name())
        .setParameter("maximumAccumulatedMiles", rewardMileSetting.getMaximumAccumulatedMiles())
        .setParameter("isUse", rewardMileSetting.getIsUse())
        .executeUpdate();
  }

  @Transactional(rollbackOn = Exception.class)
  public void deleteAllByCompanyIds(List<Long> companyIds) {
    CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
    CriteriaDelete<RewardMileSetting> delete = builder.createCriteriaDelete(RewardMileSetting.class);

    Root<RewardMileSetting> root = delete.from(RewardMileSetting.class);

    delete.where(root.get(RewardMileSetting_.companyId).in(companyIds));

    this.statelessSession.createMutationQuery(delete).executeUpdate();
  }
}
