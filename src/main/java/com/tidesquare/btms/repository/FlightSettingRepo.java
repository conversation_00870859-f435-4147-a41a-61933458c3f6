package com.tidesquare.btms.repository;

import java.util.List;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.FlightSetting;
import com.tidesquare.btms.entity.FlightSetting_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.json.Json;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.CriteriaUpdate;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class FlightSettingRepo {
  @Inject
  private StatelessSession statelessSession;

  public String findByCompanyId(Long companyId) {
    CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
    CriteriaQuery<FlightSetting> query = builder.createQuery(FlightSetting.class);
    Root<FlightSetting> root = query.from(FlightSetting.class);
    query.select(root);
    query.where(builder.equal(root.get("companyId"), companyId));
    List<FlightSetting> results = this.statelessSession.createQuery(query).getResultList();
    if (results.isEmpty()) {
      insert(companyId, Json.createArrayBuilder().build().toString());
      return "[]";
    }
    return results.get(0).getContent();

  }

  @Transactional(rollbackOn = Exception.class)
  public void insert(Long companyId, String content) {
    this.statelessSession.insert(FlightSetting.builder().companyId(companyId).content(content).build());
  }

  @Transactional(rollbackOn = Exception.class)
  public void insertOrUpdate(Long companyId, String content) {
    String sql = "INSERT INTO flight_setting (company_id, content) VALUES (:companyId, :content) ON DUPLICATE KEY UPDATE content = :content";
    this.statelessSession.createNativeMutationQuery(sql).setParameter("companyId", companyId).setParameter("content", content).executeUpdate();
  }

  @Transactional(rollbackOn = Exception.class)
  public void update(Long companyId, String content) {
    CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
    CriteriaUpdate<FlightSetting> query = builder.createCriteriaUpdate(FlightSetting.class);
    Root<FlightSetting> root = query.from(FlightSetting.class);
    query.where(builder.equal(root.get(FlightSetting_.companyId), companyId));
    query.set("content", content);
    this.statelessSession.createMutationQuery(query).executeUpdate();
  }
}
