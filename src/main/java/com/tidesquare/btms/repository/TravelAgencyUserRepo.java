package com.tidesquare.btms.repository;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.constant.JoinStatus;
import com.tidesquare.btms.entity.TravelAgencyUser;
import com.tidesquare.btms.entity.TravelAgencyUser_;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Root;

@Startup
@ApplicationScoped
public class TravelAgencyUserRepo {

    @Inject
    private StatelessSession statelessSession;

    @PostConstruct
    void init() {
    }

    public TravelAgencyUser findAndFetchDepartmentByEmployeeNoAndJoinStatusApprovalAndSineCode4AmadeusNotNull(String employeeNo) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<TravelAgencyUser> query = builder.createQuery(TravelAgencyUser.class);
        Root<TravelAgencyUser> root = query.from(TravelAgencyUser.class);
        root.fetch(TravelAgencyUser_.department, JoinType.LEFT);
        query.select(root);
        query.where(builder.equal(root.get(TravelAgencyUser_.employeeNo), employeeNo),
                builder.equal(root.get(TravelAgencyUser_.joinStatus), JoinStatus.Approval),
                builder.isNotNull(root.get(TravelAgencyUser_.sineCode4Amadeus)));
        TravelAgencyUser travelAgencyUser = this.statelessSession.createSelectionQuery(query).setMaxResults(1).getSingleResultOrNull();
        return travelAgencyUser;
    }
}
