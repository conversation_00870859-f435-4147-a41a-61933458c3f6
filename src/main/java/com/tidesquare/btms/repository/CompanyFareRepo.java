package com.tidesquare.btms.repository;

import java.util.List;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.CompanyFare;
import com.tidesquare.btms.entity.CompanyFare_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaDelete;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class CompanyFareRepo {

    @Inject
    private StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public void insert(CompanyFare companyFare) {
        this.statelessSession.insert(companyFare);
    }

    public List<CompanyFare> findByCompanyId(Long companyId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<CompanyFare> query = builder.createQuery(CompanyFare.class);
        Root<CompanyFare> root = query.from(CompanyFare.class);
        query.select(root);
        query.where(builder.equal(root.get(CompanyFare_.companyId), companyId));
        query.orderBy(builder.desc(root.get(CompanyFare_.id)));

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    @Transactional(rollbackOn = Exception.class)
    public void deleteAllByCompanyIds(List<Long> companyIds) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaDelete<CompanyFare> delete = builder.createCriteriaDelete(CompanyFare.class);

        Root<CompanyFare> root = delete.from(CompanyFare.class);

        delete.where(root.get(CompanyFare_.companyId).in(companyIds));

        this.statelessSession.createMutationQuery(delete).executeUpdate();
    }
}
