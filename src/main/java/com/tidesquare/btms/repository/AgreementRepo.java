package com.tidesquare.btms.repository;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.Agreement;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class AgreementRepo {

    @Inject
    private StatelessSession statelessSession;

    public void insert(Agreement agreement) {
        this.statelessSession.insert(agreement);
    }
}
