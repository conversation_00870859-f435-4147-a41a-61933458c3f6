package com.tidesquare.btms.repository;

import java.util.List;
import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.TravelFareRuleRecord;
import com.tidesquare.btms.entity.TravelFareRuleRecord_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class TravelFareRuleRecordRepo {
    @Inject
    private StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public TravelFareRuleRecord insert(TravelFareRuleRecord travelFareRuleRecord) {
        this.statelessSession.insert(travelFareRuleRecord);
        return travelFareRuleRecord;
    }

    public List<TravelFareRuleRecord> findByTravelId(Long travelId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<TravelFareRuleRecord> query = builder.createQuery(TravelFareRuleRecord.class);

        Root<TravelFareRuleRecord> root = query.from(TravelFareRuleRecord.class);

        query.select(root);
        query.where(builder.equal(root.get(TravelFareRuleRecord_.travelId), travelId));

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }
}
