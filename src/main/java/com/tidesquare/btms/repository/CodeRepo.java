package com.tidesquare.btms.repository;

import java.util.List;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.Code;
import com.tidesquare.btms.entity.Code_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;

@ApplicationScoped
public class CodeRepo {
    @Inject
    private StatelessSession statelessSession;

    public List<Code> findByGroupCode(String groupCode) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Code> query = builder.createQuery(Code.class);
        Root<Code> root = query.from(Code.class);
        query.select(root);
        query.where(builder.equal(root.get(Code_.isUse), true), builder.equal(root.get(Code_.groupCode), groupCode));
        query.orderBy(builder.asc(root.get(Code_.displayOrder)));

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }
}
