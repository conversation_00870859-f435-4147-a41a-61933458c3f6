package com.tidesquare.btms.repository;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.StellaSearchFareLog;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
public class StellaSearchFareLogRepo {

    @Inject
    private StatelessSession statelessSession;

    public void insert(StellaSearchFareLog entity) {
        this.statelessSession.insert(entity);
    }
}
