package com.tidesquare.btms.repository;

import java.util.List;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.CompareAirSchedule;
import com.tidesquare.btms.entity.CompareAirSchedule_;
import com.tidesquare.btms.entity.CompareScheduleDetail;
import com.tidesquare.btms.entity.CompareScheduleDetail_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Fetch;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@ApplicationScoped
public class CompareAirScheduleRepo {
    @Inject
    private StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public CompareAirSchedule insert(CompareAirSchedule compareAirSchedule) {
        this.statelessSession.insert(compareAirSchedule);
        return compareAirSchedule;
    }

    public List<CompareAirSchedule> findAllByBookingAirId(Long bookingAirId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<CompareAirSchedule> query = builder.createQuery(CompareAirSchedule.class);

        Root<CompareAirSchedule> compareAirScheduleRoot = query.from(CompareAirSchedule.class);

        Fetch<CompareAirSchedule, CompareScheduleDetail> compareAirScheduleJoin = compareAirScheduleRoot
                .fetch(CompareAirSchedule_.compareScheduleDetails, JoinType.LEFT);
        compareAirScheduleJoin.fetch(CompareScheduleDetail_.flightDetails, JoinType.LEFT);

        query.where(builder.equal(compareAirScheduleRoot.get(CompareAirSchedule_.bookingAirId), bookingAirId));
        query.select(compareAirScheduleRoot);

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }
}
