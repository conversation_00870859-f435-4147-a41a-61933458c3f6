package com.tidesquare.btms.repository;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.constant.InflowBookingType;
import com.tidesquare.btms.entity.BookingAir;
import com.tidesquare.btms.entity.BookingAirSchedule;
import com.tidesquare.btms.entity.BookingAirSchedule_;
import com.tidesquare.btms.entity.BookingAir_;
import com.tidesquare.btms.entity.Travel;
import com.tidesquare.btms.entity.Travel_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.CriteriaUpdate;
import jakarta.persistence.criteria.Fetch;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@ApplicationScoped
public class BookingAirRepo {
    @Inject
    private StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public BookingAir insert(BookingAir bookingAir) {
        this.statelessSession.insert(bookingAir);
        return bookingAir;
    }

    public BookingAir findById(Long bookingAirId) {
        return this.statelessSession.get(BookingAir.class, bookingAirId);
    }

    public BookingAir findAndFetchTravelById(Long bookingAirId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<BookingAir> query = builder.createQuery(BookingAir.class);
        Root<BookingAir> root = query.from(BookingAir.class);
        root.fetch(BookingAir_.travel, JoinType.INNER);

        query.select(root);
        query.where(builder.equal(root.get(BookingAir_.id), bookingAirId));

        return this.statelessSession.createSelectionQuery(query).getSingleResultOrNull();
    }

    public BookingAir findByTravelId(Long travelId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<BookingAir> query = builder.createQuery(BookingAir.class);
        Root<BookingAir> root = query.from(BookingAir.class);

        query.select(root);
        query.where(builder.equal(root.get(BookingAir_.travel), new Travel(travelId)));
        query.orderBy(builder.desc(root.get(BookingAir_.id)));

        return this.statelessSession.createSelectionQuery(query).setMaxResults(1).getSingleResultOrNull();
    }

    public BookingAir findByPnrNoAndNotEndTravel(String pnrNo) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<BookingAir> query = builder.createQuery(BookingAir.class);
        Root<BookingAir> root = query.from(BookingAir.class);
        Join<BookingAir, BookingAirSchedule> bookingAirScheduleJoin = root.join(BookingAir_.bookingAirSchedules,
                JoinType.INNER);
        Fetch<BookingAir, Travel> travelFetch = root.fetch(BookingAir_.travel, JoinType.INNER);
        @SuppressWarnings("unchecked")
        Join<BookingAir, Travel> travelJoin = (Join<BookingAir, Travel>) travelFetch;

        query.select(root);
        query.where(builder.equal(root.get(BookingAir_.pnrNo), pnrNo),
                builder.notEqual(root.get(BookingAir_.inflowBookingType), InflowBookingType.DIRECT),
                builder.equal(travelJoin.get(Travel_.isDeleted), false),
                builder.greaterThan(bookingAirScheduleJoin.get(BookingAirSchedule_.toDate), new Date(System.currentTimeMillis() - 365 * 24 * 60 * 60 * 1000L)));

        return this.statelessSession.createSelectionQuery(query).setMaxResults(1).getSingleResultOrNull();
    }

    @Transactional(rollbackOn = Exception.class)
    public void update(BookingAir bookingAir) {
        this.statelessSession.update(bookingAir);
    }

    @Transactional(rollbackOn = Exception.class)
    public void update(Long id, Map<String, Object> updateValues) {
        CriteriaBuilder cb = this.statelessSession.getCriteriaBuilder();
        CriteriaUpdate<BookingAir> update = cb.createCriteriaUpdate(BookingAir.class);

        Root<BookingAir> e = update.from(BookingAir.class);

        for (Map.Entry<String, Object> entry : updateValues.entrySet()) {
            update.set(entry.getKey(), entry.getValue());
        }

        update.where(cb.equal(e.get(BookingAir_.id), id));
        this.statelessSession.createMutationQuery(update).executeUpdate();
    }

    public List<BookingAir> getBookingAirsWithLimitLessThanFiveYears(Long indexValue, int limit) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();

        LocalDateTime fiveYearsAgo = LocalDateTime.now().minusYears(5);
        Date fiveYearsAgoDate = Date.from(fiveYearsAgo.atZone(ZoneId.systemDefault()).toInstant());

        CriteriaQuery<BookingAir> query = builder.createQuery(BookingAir.class);
        Root<BookingAir> root = query.from(BookingAir.class);
        query.select(root);
        query.where(builder.greaterThan(root.get(BookingAir_.id), indexValue));
        List<BookingAir> bookingAirs = this.statelessSession.createSelectionQuery(query)
                .setMaxResults(limit)
                .getResultList()
                .stream()
                .filter(bookingAir -> bookingAir.getCreateDate().before(fiveYearsAgoDate))
                .collect(Collectors.toList());

        return bookingAirs.isEmpty() ? new ArrayList<>() : bookingAirs;
    }
}
