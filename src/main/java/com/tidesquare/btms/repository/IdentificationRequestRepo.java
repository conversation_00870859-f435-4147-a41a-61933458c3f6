package com.tidesquare.btms.repository;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.IdentificationRequest;
import com.tidesquare.btms.entity.IdentificationRequest_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;

@ApplicationScoped
@Slf4j
public class IdentificationRequestRepo {

    @Inject
    private StatelessSession statelessSession;

    public void insert(IdentificationRequest identificationRequest) {
        this.statelessSession.insert(identificationRequest);
    }

    public IdentificationRequest findByVerificationMeansAccount(String verificationMeansAccount) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<IdentificationRequest> query = builder.createQuery(IdentificationRequest.class);
        Root<IdentificationRequest> root = query.from(IdentificationRequest.class);
        query.select(root);
        query.where(builder.equal(root.get(IdentificationRequest_.verificationMeansAccount), verificationMeansAccount));
        query.orderBy(builder.desc(root.get(IdentificationRequest_.id)));

        return this.statelessSession.createSelectionQuery(query).setMaxResults(1).getSingleResultOrNull();
    }
}
