package com.tidesquare.btms.repository;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.CompareFlightDetail;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@ApplicationScoped
public class CompareFlightDetailRepo {
    @Inject
    private StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public CompareFlightDetail insert(CompareFlightDetail compareFlightDetail) {
        this.statelessSession.insert(compareFlightDetail);
        return compareFlightDetail;
    }
}
