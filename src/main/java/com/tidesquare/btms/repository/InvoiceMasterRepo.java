package com.tidesquare.btms.repository;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.InvoiceMaster;
import com.tidesquare.btms.entity.InvoiceMasterPublish;
import com.tidesquare.btms.entity.InvoiceMasterPublish_;
import com.tidesquare.btms.entity.InvoiceMaster_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Fetch;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@ApplicationScoped
@Slf4j
public class InvoiceMasterRepo {
    @Inject
    private StatelessSession statelessSession;

    public List<InvoiceMaster> findAllByBookingAirId(Long bookingAirId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<InvoiceMasterPublish> query = builder.createQuery(InvoiceMasterPublish.class);

        Root<InvoiceMasterPublish> root = query.from(InvoiceMasterPublish.class);
        Fetch<InvoiceMasterPublish, InvoiceMaster> invoiceMasterPublishJoin = root.fetch(InvoiceMasterPublish_.invoice, JoinType.INNER);
        invoiceMasterPublishJoin.fetch(InvoiceMaster_.invoiceTemplate, JoinType.INNER);
        
        query.where(builder.equal(root.get(InvoiceMasterPublish_.bookingAirId), bookingAirId));

        query.select(root);

        List<InvoiceMasterPublish> invoicePublishs = this.statelessSession.createSelectionQuery(query).getResultList();
        return invoicePublishs.stream().map(e -> e.getInvoice()).toList();
    }
}
