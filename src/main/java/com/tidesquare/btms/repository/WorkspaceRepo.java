package com.tidesquare.btms.repository;

import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.Company_;
import com.tidesquare.btms.entity.Workspace;
import com.tidesquare.btms.entity.Workspace_;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

import java.util.List;

import org.hibernate.StatelessSession;

@ApplicationScoped
@Startup
public class WorkspaceRepo {

    @Inject
    private StatelessSession statelessSession;

    @PostConstruct
    public void init() {
    }

    @Transactional(rollbackOn = Exception.class)
    public Workspace insert(Workspace workspace) {
        this.statelessSession.insert(workspace);
        return workspace;
    }

    public Workspace findCreatedFirstByCompanyId(Long companyId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Workspace> query = builder.createQuery(Workspace.class);

        Root<Workspace> root = query.from(Workspace.class);

        query.where(builder.equal(root.get(Workspace_.company), new Company(companyId)), builder.equal(root.get(Workspace_.isUse), true));

        query.select(root);

        return this.statelessSession.createSelectionQuery(query).setMaxResults(1).getSingleResultOrNull();
    }

    public List<Workspace> findByCompanyIds(List<Long> companyIds) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Workspace> query = builder.createQuery(Workspace.class);

        Root<Workspace> root = query.from(Workspace.class);

        query.select(root);
        query.where(root.get(Workspace_.company).get(Company_.id).in(companyIds), builder.equal(root.get(Workspace_.isUse), true));
        query.orderBy(builder.asc(root.get(Workspace_.id)));

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }
}
