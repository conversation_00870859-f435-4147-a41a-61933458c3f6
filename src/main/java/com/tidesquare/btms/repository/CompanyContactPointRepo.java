package com.tidesquare.btms.repository;

import java.util.List;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.CompanyContactPoint;
import com.tidesquare.btms.entity.CompanyContactPoint_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaDelete;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class CompanyContactPointRepo {

    @Inject
    private StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public void insert(CompanyContactPoint companyContactPoint) {
        this.statelessSession.insert(companyContactPoint);
    }

    public List<CompanyContactPoint> findByCompanyId(Long companyId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<CompanyContactPoint> query = builder.createQuery(CompanyContactPoint.class);
        Root<CompanyContactPoint> root = query.from(CompanyContactPoint.class);
        query.select(root);
        query.where(builder.equal(root.get(CompanyContactPoint_.companyId), companyId));
        query.orderBy(builder.desc(root.get(CompanyContactPoint_.id)));

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    @Transactional(rollbackOn = Exception.class)
    public void deleteAllByCompanyIds(List<Long> companyIds) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaDelete<CompanyContactPoint> delete = builder.createCriteriaDelete(CompanyContactPoint.class);

        Root<CompanyContactPoint> root = delete.from(CompanyContactPoint.class);

        delete.where(root.get(CompanyContactPoint_.companyId).in(companyIds));

        this.statelessSession.createMutationQuery(delete).executeUpdate();
    }
}
