package com.tidesquare.btms.repository;

import java.util.ArrayList;
import java.util.List;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.constant.ManagerType;
import com.tidesquare.btms.entity.BtmsManager;
import com.tidesquare.btms.entity.BtmsManager_;
import com.tidesquare.btms.entity.TravelAgencyUser;
import com.tidesquare.btms.entity.TravelAgencyUser_;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaDelete;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Fetch;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Order;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

@Startup
@ApplicationScoped
public class BtmsManagerRepo {
    @Inject
    private StatelessSession statelessSession;

    @PostConstruct
    public void init() {
    }

    @Transactional(rollbackOn = Exception.class)
    public void insert(BtmsManager btmsManager) {
        this.statelessSession.insert(btmsManager);
    }

    public List<BtmsManager> findByCompanyId(Long companyId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<BtmsManager> query = builder.createQuery(BtmsManager.class);
        Root<BtmsManager> root = query.from(BtmsManager.class);
        query.select(root);
        query.where(builder.equal(root.get(BtmsManager_.companyId), companyId));
        query.orderBy(builder.asc(root.get(BtmsManager_.managerType)), builder.asc(root.get(BtmsManager_.isOverseas)), builder.asc(root.get(BtmsManager_.displayOrder)));

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    public BtmsManager findOneAndFetchDepartmentByCompanyIdAndIsOverseasAndManagerType(Long companyId, boolean isOverseas, ManagerType managerType) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<BtmsManager> query = builder.createQuery(BtmsManager.class);
        Root<BtmsManager> root = query.from(BtmsManager.class);
        Fetch<BtmsManager, TravelAgencyUser> travelAgencyUserFetch = root.fetch(BtmsManager_.travelAgencyUser, JoinType.INNER);
        travelAgencyUserFetch.fetch(TravelAgencyUser_.department, JoinType.LEFT);
        query.select(root);
        query.where(builder.equal(root.get(BtmsManager_.companyId), companyId),
                builder.equal(root.get(BtmsManager_.isOverseas), isOverseas),
                builder.equal(root.get(BtmsManager_.managerType), managerType));
        query.orderBy(builder.asc(root.get(BtmsManager_.displayOrder)));

        return this.statelessSession.createSelectionQuery(query).setMaxResults(1).getSingleResultOrNull();
    }

    public List<BtmsManager> findFetchTravelAgencyUser(Long companyId, Boolean isOverseas, ManagerType managerType) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<BtmsManager> query = builder.createQuery(BtmsManager.class);
        Root<BtmsManager> root = query.from(BtmsManager.class);
        root.fetch(BtmsManager_.travelAgencyUser, JoinType.INNER);
        query.select(root);
        List<Predicate> predicates = new ArrayList<>();
        predicates.add(builder.equal(root.get(BtmsManager_.companyId), companyId));

        if (isOverseas != null) {
            predicates.add(builder.equal(root.get(BtmsManager_.isOverseas), isOverseas));
        }
        if (managerType != null) {
            predicates.add(builder.equal(root.get(BtmsManager_.managerType), managerType));
        }

        query.where(predicates.toArray(new Predicate[0]));
        List<Order> orders = new ArrayList<>();
        if (managerType == null) {
            orders.add(builder.asc(root.get(BtmsManager_.managerType)));
        }
        if (isOverseas == null) {
            orders.add(builder.asc(root.get(BtmsManager_.isOverseas)));
        }
        orders.add(builder.asc(root.get(BtmsManager_.displayOrder)));
        query.orderBy(orders.toArray(new Order[0]));

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

    @Transactional(rollbackOn = Exception.class)
    public void deleteAllByCompanyIds(List<Long> companyIds) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaDelete<BtmsManager> delete = builder.createCriteriaDelete(BtmsManager.class);

        Root<BtmsManager> root = delete.from(BtmsManager.class);

        delete.where(root.get(BtmsManager_.companyId).in(companyIds));

        this.statelessSession.createMutationQuery(delete).executeUpdate();
    }

}
