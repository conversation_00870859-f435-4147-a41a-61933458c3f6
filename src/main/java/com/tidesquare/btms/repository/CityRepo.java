package com.tidesquare.btms.repository;

import java.util.List;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.City;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;

@ApplicationScoped
public class CityRepo {
    @Inject
    private StatelessSession statelessSession;

    public List<City> findAll() {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<City> query = builder.createQuery(City.class);
        Root<City> root = query.from(City.class);
        query.select(root);

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

}
