package com.tidesquare.btms.repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedSet;
import java.util.TreeSet;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.BookingAirSchedule;
import com.tidesquare.btms.entity.BookingAirSchedule_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaDelete;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class BookingAirScheduleRepo {
    @Inject
    StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public BookingAirSchedule insert(BookingAirSchedule bookingAirSchedule) {
        this.statelessSession.insert(bookingAirSchedule);
        return bookingAirSchedule;
    }

    @Transactional(rollbackOn = Exception.class)
    public void deleteByBookingAirId(Long bookingAirId) {
        CriteriaBuilder cb = this.statelessSession.getCriteriaBuilder();
        CriteriaDelete<BookingAirSchedule> delete = cb.createCriteriaDelete(BookingAirSchedule.class);

        Root<BookingAirSchedule> e = delete.from(BookingAirSchedule.class);

        delete.where(cb.equal(e.get(BookingAirSchedule_.bookingAirId), bookingAirId));

        this.statelessSession.createMutationQuery(delete).executeUpdate();
    }

    public Map<Long, SortedSet<BookingAirSchedule>> getBookingAirSchedulesByBookingAirIds(List<Long> bookingAirIds) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<BookingAirSchedule> query = builder.createQuery(BookingAirSchedule.class);
        Root<BookingAirSchedule> root = query.from(BookingAirSchedule.class);
        query.select(root);
        query.where(root.get(BookingAirSchedule_.bookingAirId).in(bookingAirIds));
        query.orderBy(builder.asc(root.get(BookingAirSchedule_.bookingAirId)), builder.asc(root.get(BookingAirSchedule_.id)));

        List<BookingAirSchedule> bookingAirSchedules = this.statelessSession.createSelectionQuery(query).getResultList();
        Map<Long, SortedSet<BookingAirSchedule>> bookingAirScheduleMap = new HashMap<>();
        for (BookingAirSchedule bookingAirSchedule : bookingAirSchedules) {
            bookingAirScheduleMap.computeIfAbsent(bookingAirSchedule.getBookingAirId(), k -> new TreeSet<>()).add(bookingAirSchedule);
        }
        return bookingAirScheduleMap;
    }
}
