package com.tidesquare.btms.repository;

import com.tidesquare.btms.entity.TravelCity;
import com.tidesquare.btms.entity.TravelCity_;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaDelete;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

import org.hibernate.StatelessSession;

@Startup
@ApplicationScoped
public class TravelCityRepo {
    @Inject
    private StatelessSession statelessSession;

    @PostConstruct
    void init() {
    }

    @Transactional(rollbackOn = Exception.class)
    public void insert(TravelCity travelCity) {
        this.statelessSession.insert(travelCity);
    }

    @Transactional(rollbackOn = Exception.class)
    public void deleteByTravelId(Long travelId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaDelete<TravelCity> delete = builder.createCriteriaDelete(TravelCity.class);
        Root<TravelCity> root = delete.from(TravelCity.class);
        delete.where(builder.equal(root.get(TravelCity_.travelId), travelId));
        this.statelessSession.createMutationQuery(delete).executeUpdate();
    }
}
