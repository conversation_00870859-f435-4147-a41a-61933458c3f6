package com.tidesquare.btms.repository;

import java.util.Date;
import java.util.Map;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.constant.ContactRequestStatus;
import com.tidesquare.btms.entity.ContactRequest;
import com.tidesquare.btms.entity.ContactRequest_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.CriteriaUpdate;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

@ApplicationScoped
public class ContactRequestRepo {

    @Inject
    private StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public void insert(ContactRequest contactRequest) {
        this.statelessSession.insert(contactRequest);
    }

    public ContactRequest findById(Long id) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<ContactRequest> query = builder.createQuery(ContactRequest.class);
        Root<ContactRequest> root = query.from(ContactRequest.class);
        root.fetch(ContactRequest_.respondent, JoinType.LEFT);
        query.select(root);

        query.where(builder.equal(root.get(ContactRequest_.id), id),
                builder.or(
                        builder.equal(root.get(ContactRequest_.status), ContactRequestStatus.NEW),
                        builder.and(
                                builder.equal(root.get(ContactRequest_.status), ContactRequestStatus.ANSWERED),
                                builder.greaterThan(root.get(ContactRequest_.answeredAt), new Date(System.currentTimeMillis() - 1 * 30 * 24 * 60 * 60 * 1000L)))));

        return this.statelessSession.createSelectionQuery(query).getSingleResultOrNull();
    }

    @Transactional(rollbackOn = Exception.class)
    public void update(Long id, Map<String, Object> updateValues) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaUpdate<ContactRequest> update = builder.createCriteriaUpdate(ContactRequest.class);
        Root<ContactRequest> root = update.from(ContactRequest.class);

        for (Map.Entry<String, Object> entry : updateValues.entrySet()) {
            update.set(entry.getKey(), entry.getValue());
        }

        update.where(builder.equal(root.get(ContactRequest_.id), id),
                builder.or(
                        builder.equal(root.get(ContactRequest_.status), ContactRequestStatus.NEW),
                        builder.and(
                                builder.equal(root.get(ContactRequest_.status), ContactRequestStatus.ANSWERED),
                                builder.greaterThan(root.get(ContactRequest_.answeredAt), new Date(System.currentTimeMillis() - 1 * 30 * 24 * 60 * 60 * 1000L)))));

        this.statelessSession.createMutationQuery(update).executeUpdate();
    }

}
