package com.tidesquare.btms.repository;

import com.tidesquare.btms.entity.PnrDataHistory;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import org.hibernate.StatelessSession;

@ApplicationScoped
public class PnrDataHistoryRepo {
    @Inject
    private StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public PnrDataHistory insert(PnrDataHistory pnrDataHistory) {
        this.statelessSession.insert(pnrDataHistory);
        return pnrDataHistory;
    }

}
