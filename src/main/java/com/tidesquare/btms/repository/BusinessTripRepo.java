package com.tidesquare.btms.repository;

import com.tidesquare.btms.entity.BusinessTrip;
import com.tidesquare.btms.entity.BusinessTrip_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaUpdate;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

import org.hibernate.StatelessSession;

import java.util.Map;

@ApplicationScoped
public class BusinessTripRepo {
    @Inject
    private StatelessSession statelessSession;

    @Transactional(rollbackOn = Exception.class)
    public BusinessTrip insert(BusinessTrip businessTrip) {
        this.statelessSession.insert(businessTrip);
        return businessTrip;
    }

    @Transactional(rollbackOn = Exception.class)
    public void update(Long businessTripId, Map<String, Object> updateValues) {
        CriteriaBuilder cb = this.statelessSession.getCriteriaBuilder();
        CriteriaUpdate<BusinessTrip> update = cb.createCriteriaUpdate(BusinessTrip.class);

        Root<BusinessTrip> e = update.from(BusinessTrip.class);

        for (Map.Entry<String, Object> entry : updateValues.entrySet()) {
            update.set(entry.getKey(), entry.getValue());
        }

        update.where(cb.equal(e.get(BusinessTrip_.id), businessTripId));
        this.statelessSession.createMutationQuery(update).executeUpdate();
    }

    @Transactional(rollbackOn = Exception.class)
    public void update(BusinessTrip businessTrip) {
        this.statelessSession.update(businessTrip);
    }
}
