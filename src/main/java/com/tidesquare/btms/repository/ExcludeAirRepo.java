package com.tidesquare.btms.repository;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.ExcludeAirline;
import com.tidesquare.btms.entity.ExcludeAirline_;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;

import java.util.List;

@ApplicationScoped
public class ExcludeAirRepo {

    @Inject
    private StatelessSession statelessSession;

    public List<ExcludeAirline> findByCompanyId(Long companyId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<ExcludeAirline> query = builder.createQuery(ExcludeAirline.class);
        Root<ExcludeAirline> root = query.from(ExcludeAirline.class);

        query.where(builder.equal(root.get(ExcludeAirline_.companyId), companyId));
        query.select(root);

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }
    
}
