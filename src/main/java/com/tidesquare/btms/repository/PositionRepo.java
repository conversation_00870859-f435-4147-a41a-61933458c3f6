package com.tidesquare.btms.repository;

import com.tidesquare.btms.entity.Position;
import com.tidesquare.btms.entity.Position_;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;
import jakarta.transaction.Transactional;

import java.util.List;

import org.hibernate.StatelessSession;

@ApplicationScoped
@Startup
public class PositionRepo {

    @Inject
    private StatelessSession statelessSession;

    @PostConstruct
    public void init() {
    }

    @Transactional(rollbackOn = Exception.class)
    public void insert(Position position) {
        this.statelessSession.insert(position);
    }

    public List<Position> findByCompanyId(Long companyId) {
        CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
        CriteriaQuery<Position> query = builder.createQuery(Position.class);

        Root<Position> root = query.from(Position.class);

        query.select(root);
        query.where(builder.equal(root.get(Position_.companyId), companyId), builder.equal(root.get(Position_.isUse), true));
        query.orderBy(builder.asc(root.get(Position_.id)));

        return this.statelessSession.createSelectionQuery(query).getResultList();
    }

}
