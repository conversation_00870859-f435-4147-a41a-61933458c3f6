package com.tidesquare.btms.repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedSet;
import java.util.TreeSet;

import org.hibernate.StatelessSession;

import com.tidesquare.btms.entity.PaymentCash;
import com.tidesquare.btms.entity.PaymentCash_;
import com.tidesquare.btms.utils.ListUtil;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Root;

@ApplicationScoped
public class PaymentCashRepo {

    @Inject
    private StatelessSession statelessSession;

    public Map<Long, SortedSet<PaymentCash>> getPaymentCashByPaymentIds(List<Long> paymentIds) {
        List<List<Long>> paymentIdsChunks = ListUtil.chunk(paymentIds, 512);

        Map<Long, SortedSet<PaymentCash>> paymentCashMap = new HashMap<>();
        for (List<Long> paymentIdsChunk : paymentIdsChunks) {
            CriteriaBuilder builder = this.statelessSession.getCriteriaBuilder();
            CriteriaQuery<PaymentCash> query = builder.createQuery(PaymentCash.class);
            Root<PaymentCash> root = query.from(PaymentCash.class);
            query.select(root);
            query.where(root.get(PaymentCash_.paymentId).in(paymentIdsChunk));

            List<PaymentCash> paymentCashs = this.statelessSession.createSelectionQuery(query).getResultList();
            for (PaymentCash paymentCash : paymentCashs) {
                paymentCashMap.computeIfAbsent(paymentCash.getPaymentId(), k -> new TreeSet<>()).add(paymentCash);
            }
        }
        return paymentCashMap;
    }
}
