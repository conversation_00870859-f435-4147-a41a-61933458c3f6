package com.tidesquare.btms.filter;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.constant.UserType;
import com.tidesquare.btms.exception.ApiException;
import com.tidesquare.btms.exception.ErrorCode;

import io.vertx.mutiny.core.Context;
import io.vertx.mutiny.core.Vertx;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.container.ContainerRequestFilter;
import jakarta.ws.rs.core.Response.Status;
import jakarta.ws.rs.ext.Provider;

@Provider
@UserBinding
public class UserFilter implements ContainerRequestFilter {

    @Override
    public void filter(ContainerRequestContext requestContext) {
        Context context = Vertx.currentContext();
        if (context == null) {
            throw new ApiException(Status.UNAUTHORIZED, ErrorCode.E401);
        }
        UserInfo userInfo = context.getLocal("user");
        if (userInfo == null || !userInfo.getUserType().equals(UserType.customer)) {
            throw new ApiException(Status.UNAUTHORIZED, ErrorCode.E401);
        }
    }
}