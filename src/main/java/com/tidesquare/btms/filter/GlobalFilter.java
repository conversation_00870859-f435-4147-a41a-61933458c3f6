package com.tidesquare.btms.filter;

import java.io.IOException;

import org.eclipse.microprofile.config.inject.ConfigProperty;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.service.jwt.JwtService;

import io.vertx.mutiny.core.Context;
import io.vertx.mutiny.core.Vertx;
import jakarta.inject.Inject;
import jakarta.ws.rs.container.ContainerRequestContext;
import jakarta.ws.rs.container.ContainerRequestFilter;
import jakarta.ws.rs.ext.Provider;

@Provider
public class GlobalFilter implements ContainerRequestFilter {

    @Inject
    private JwtService jwtService;

    @ConfigProperty(name = "BTMS_API_KEY")
    private jakarta.inject.Provider<String> btmsApiKey;

    @Override
    public void filter(ContainerRequestContext requestContext) throws IOException {
        String authorizationHeader = requestContext.getHeaderString("Authorization");
        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
            String accessToken = authorizationHeader.substring(7);
            UserInfo userInfo = this.jwtService.verifyToken(accessToken);
            if (userInfo == null) {
                return;
            }

            Context context = Vertx.currentContext();
            context.putLocal("role", "user");
            context.putLocal("user", userInfo);
            return;
        }

        String xBtmsApiKey = requestContext.getHeaderString("X-BTMS-API-KEY");
        if (xBtmsApiKey != null && xBtmsApiKey.equals(this.btmsApiKey.get())) {
            Context context = Vertx.currentContext();
            context.putLocal("role", "service");
            return;
        }

    }

}
