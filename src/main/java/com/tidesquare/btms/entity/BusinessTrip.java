package com.tidesquare.btms.entity;

import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "BusinessTrip")
public class BusinessTrip {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "businessTripId")
	private Long id;

	// 출장명
	@Column
	private String businessTripName;

	// 출국일
	@Column
	private Date fromDate;

	// 귀국일
	@Column
	private Date toDate;

	@Column(updatable = false)
	@CreationTimestamp(source = SourceType.VM)
	private Date createDate;

	@Column
	@UpdateTimestamp(source = SourceType.VM)
	private Date modifyDate;
}
