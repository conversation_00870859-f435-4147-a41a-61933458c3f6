package com.tidesquare.btms.entity;

import lombok.Getter;
import lombok.Setter;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Getter
@Setter
@Entity()
@Table(name = "CompanyContactPoint")
public class CompanyContactPoint {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "COMPANY_CONTACT_ID")
	private Long id;

	@Column(name = "companyId")
	private Long companyId;

	@Column(length = 50)
	private String managerDeptName = "";

	@Column(length = 250)
	private String managerEmail = "";

	@Column(length = 50)
	private String managerPositionName = "";

	@Column(length = 11)
	private String managerPhoneNumber = "";

	@Column(length = 30)
	private String managerName = "";

	@Column(length = 11)
	private String managerCellPhoneNumber = "";
}
