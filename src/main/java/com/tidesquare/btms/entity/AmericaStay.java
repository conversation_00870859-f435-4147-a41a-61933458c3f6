package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;
import org.hibernate.annotations.UpdateTimestamp;

@Getter
@Setter
@Entity
@Table(name = "America_Stay")
@NoArgsConstructor
public class AmericaStay {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "AMERICA_STAY_ID")
    private Long id;

    @Column(name = "TRAVEL_ID")
    private Long travelId;

    @Column(name = "STATE_CODE", length = 3)
    private String stateCode;

    @Column(name = "STATE_NAME", length = 50)
    private String stateName;

    @Column(name = "CITY_CODE", length = 4)
    private String cityCode;

    @Column(name = "CITY_NAME", length = 50)
    private String cityName;

    @Column(name = "ZIPCODE")
    private String zipCode;

    @Column(name = "ADDRESS")
    private String address;

    @Column(name = "CREATOR_USER_ID")
    private Long creatorUserId;

    @Column(name = "CREATE_DATE")
    @CreationTimestamp(source = SourceType.VM)
    private Date createDate;

    @Column(name = "MODIFIER_USER_ID")
    private Long modifierUserId;

    @Column(name = "MODIFY_DATE")
    @UpdateTimestamp(source = SourceType.VM)
    private Date modifyDate;
}
