package com.tidesquare.btms.entity;

import java.util.Date;

import org.hibernate.Hibernate;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;
import org.hibernate.annotations.UpdateTimestamp;

import com.tidesquare.btms.entity.embeddable.Address;

import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "Workspace")
@NoArgsConstructor
public class Workspace {
	public Workspace(Long id) {
		this.id = id;
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "workspaceId")
	private Long id;

	@Column(length = 30)
	private String name;

	@Column(length = 20)
	private String phoneNumber;

	@Embedded
	private Address address;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "companyId", nullable = false)
	private Company company;

	public Company getCompany() {
		if (Hibernate.isInitialized(this.company)) {
			return this.company;
		}
		this.company = new Company(this.company.getId());
		return this.company;
	}

	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private Boolean isUse = true;

	@Column
	@CreationTimestamp(source = SourceType.VM)
	private Date createDate;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "creator_userId")
	private User creator;

	public User getCreator() {
		if (Hibernate.isInitialized(this.creator)) {
			return this.creator;
		}
		this.creator = new User(this.creator.getId());
		return this.creator;
	}

	@Column
	@UpdateTimestamp(source = SourceType.VM)
	private Date modifyDate;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "modifier_userId")
	private User modifier;

	public User getModifier() {
		if (Hibernate.isInitialized(this.modifier)) {
			return this.modifier;
		}
		this.modifier = new User(this.modifier.getId());
		return this.modifier;
	}
}