package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "MILEAGEINFO")
public class MileageInfo implements Comparable<MileageInfo> {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MILEAGEINFOID")
    Long id;

    @Column(length = 19)
    Long userId;

    @Column(length = 100)
    String airline;

    @Column(length = 100)
    String mileageMemberNo;

    @Override
    public int compareTo(MileageInfo o) {
        return this.getId().compareTo(o.getId());
    }
}
