package com.tidesquare.btms.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import org.hibernate.Hibernate;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "HomepageSetting")
public class HomepageSetting {

    public HomepageSetting(Long companyId) {
        this.companyId = companyId;
    }

    @Id
    @Column(name = "companyId")
    private Long companyId;

    @Column(nullable = false)
    private boolean isUseDefaultLogo = true;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "loginLogoAttachFileId")
    private AttachFile loginLogoAttachFile;

    public AttachFile getLoginLogoAttachFile() {
        if (Hibernate.isInitialized(this.loginLogoAttachFile)) {
            return this.loginLogoAttachFile;
        }
        this.loginLogoAttachFile = new AttachFile(this.loginLogoAttachFile.getId());
        return this.loginLogoAttachFile;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "gnbLogoAttachFileId")
    private AttachFile gnbLogoAttachFile;

    public AttachFile getGnbLogoAttachFile() {
        if (Hibernate.isInitialized(this.gnbLogoAttachFile)) {
            return this.gnbLogoAttachFile;
        }
        this.gnbLogoAttachFile = new AttachFile(this.gnbLogoAttachFile.getId());
        return this.gnbLogoAttachFile;
    }
}