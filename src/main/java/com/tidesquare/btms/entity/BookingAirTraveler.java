package com.tidesquare.btms.entity;

import java.util.Date;
import java.util.SortedSet;

import org.hibernate.Hibernate;
import org.hibernate.annotations.ColumnTransformer;

import com.tidesquare.btms.constant.Gender;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "BookingAirTraveler")
public class BookingAirTraveler implements Comparable<BookingAirTraveler> {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "bookingAirTravelerId")
	private Long id;

	@Column
	private Long bookingAirId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "travelerUserId")
	private User traveler;

	public User getTraveler() {
		if (Hibernate.isInitialized(this.traveler)) {
			return this.traveler;
		}
		this.traveler = new User(this.traveler.getId());
		return this.traveler;
	}

	@Column(length = 100)
	private String travelerName;

	@Column(length = 100)
	private String travelerLastName;

	@Column(length = 100)
	private String travelerFirstName;

	@Column(length = 100)
	private String lastName;

	@Column(length = 100)
	private String firstName;

	@Column(columnDefinition = "FLOAT")
	private Double reserveAmount = 0d;

	@Column(columnDefinition = "FLOAT")
	private Double tax = 0d;

	@Column(columnDefinition = "FLOAT")
	private Double airportTax = 0d;

	@Column(columnDefinition = "FLOAT")
	private Double fuelSurcharge = 0d;

	@Column(columnDefinition = "FLOAT")
	private Double dcAmount = 0d;

	@Column(columnDefinition = "FLOAT")
	private Double commissionAmount = 0d;

	@Column(columnDefinition = "FLOAT")
	private Double fareAmount = 0d;

	@Enumerated(EnumType.STRING)
	@Column(length = 10)
	private Gender gender;

	@Column(length = 8)
	private String birthday;

	@Column(length = 250)
	@ColumnTransformer(read = "DOC_DECRYPT(email)", write = "DOC_ENCRYPT(?)")
	private String email;

	@Column(length = 100)
	@ColumnTransformer(read = "DOC_DECRYPT(cellPhoneNumber)", write = "DOC_ENCRYPT(?)")
	private String cellPhoneNumber;

	@Column(length = 100)
	private String title;

	@Column
	private Date modifyDate;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(nullable = true)
	private User modifier;

	public User getModifier() {
		if (Hibernate.isInitialized(this.modifier)) {
			return this.modifier;
		}
		this.modifier = new User(this.modifier.getId());
		return this.modifier;
	}

	@Column(length = 30)
	private String nationalityCode; // 국적코드

	@Column(length = 30)
	private String passportNation; // 여권발행국

	@Column(length = 30)
	private String passportLimitDate; // 여권만료일자

	@Column(length = 100)
	@ColumnTransformer(read = "DOC_DECRYPT(passportNumber)", write = "DOC_ENCRYPT(?)")
	private String passportNumber; // 여권번호

	@Column(length = 30)
	private String discountCode; // 국내선신분할인코드

	@Column(length = 400)
	private String discountName; // 국내선신분할인명

	@Column(length = 100)
	private String settlementManagerEmail; // kortek전표담당자메일

	@OneToMany(fetch = FetchType.LAZY)
	@JoinColumn(name = "bookingAirTravelerId")
	private SortedSet<TravelerMileageInfo> travelerMileageInfos;

	public SortedSet<TravelerMileageInfo> getTravelerMileageInfos() {
		if (Hibernate.isInitialized(this.travelerMileageInfos)) {
			return this.travelerMileageInfos;
		}
		this.travelerMileageInfos = null;
		return this.travelerMileageInfos;
	}

	@Override
	public int compareTo(BookingAirTraveler o) {
		return this.getId().compareTo(o.getId());
	}
}