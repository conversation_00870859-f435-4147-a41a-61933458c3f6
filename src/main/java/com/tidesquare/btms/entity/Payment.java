package com.tidesquare.btms.entity;

import java.util.Date;
import java.util.SortedSet;

import com.tidesquare.btms.constant.GubunType;
import com.tidesquare.btms.constant.PaymentMethod;
import com.tidesquare.btms.constant.PaymentStatus;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.Hibernate;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;

@Getter
@Setter
@Entity
@Table(name = "PG_PAYMENT")
@NoArgsConstructor
public class Payment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "payment_id")
    private Long paymentId; // 결제번호

    public Payment(Long paymentId) {
        this.paymentId = paymentId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id")
    private Company company;

    public Company getCompany() {
        if (Hibernate.isInitialized(this.company)) {
            return this.company;
        }
        this.company = new Company(this.company.getId());
        return this.company;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "workspace_id")
    private Workspace workspace;

    public Workspace getWorkspace() {
        if (Hibernate.isInitialized(this.workspace)) {
            return this.workspace;
        }
        this.workspace = new Workspace(this.workspace.getId());
        return this.workspace;
    }

    @Column(name = "gubun")
    @Enumerated(EnumType.STRING)
    private GubunType gubun; //구분 transaction type

    @Column(name = "payment_method")
    @Enumerated(EnumType.STRING)
    private PaymentMethod paymentMethod; // 결제수단 payment method

    @Column(name = "payment_status")
    @Enumerated(EnumType.STRING)
    private PaymentStatus paymentStatus; // 결제상태 payment status

    @Column(name = "payment_amount")
    private Double paymentAmount; // 결제금액

    @Column(name = "parent_payment_id")
    private Long parentPaymentId; // 원결제번호

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "handler_user_id")
    private User handlerUser; // 처리관리자아이디 people processing transaction

    @Column(name = "creator_user_id")
    private Long creatorUserId; // 등록관리자아이디 people create transaction

    @Column(name = "create_date")
    @CreationTimestamp(source = SourceType.VM)
    private Date createDate; // 등록일시

    @Column(name = "repay_yn")
    //부분취소 시 추가
    private String repayYn; //부분취소여부 partial refund

    //2020.03.18.kulc78 - 은행입금 멀티
    @Column(name = "request_json")
    private String requestJson; //ERP 요청 json - json receive from ERP system (from other system)

    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "payment_id")
    private SortedSet<PaymentCharge> paymentCharges;

    public SortedSet<PaymentCharge> getPaymentCharges() {
        if (Hibernate.isInitialized(this.paymentCharges)) {
            return this.paymentCharges;
        }
        this.paymentCharges = null;
        return this.paymentCharges;
    }

    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "payment_id")
    private SortedSet<PaymentCash> paymentCashs;

    public SortedSet<PaymentCash> getPaymentCashs() {
        if (Hibernate.isInitialized(this.paymentCashs)) {
            return this.paymentCashs;
        }
        this.paymentCashs = null;
        return this.paymentCashs;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @PrimaryKeyJoinColumn()
    private PaymentCard paymentCard;

    public PaymentCard getPaymentCard() {
        if (Hibernate.isInitialized(this.paymentCard)) {
            return this.paymentCard;
        }
        this.paymentCard = new PaymentCard(this.paymentCard.getPaymentId());
        return this.paymentCard;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @PrimaryKeyJoinColumn()
    private PaymentEtcDeposit paymentEtcDeposit;

    public PaymentEtcDeposit getPaymentEtcDeposit() {
        if (Hibernate.isInitialized(this.paymentEtcDeposit)) {
            return this.paymentEtcDeposit;
        }
        this.paymentEtcDeposit = new PaymentEtcDeposit(this.paymentEtcDeposit.getPaymentId());
        return this.paymentEtcDeposit;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @PrimaryKeyJoinColumn()
    private HotelPaymentMapping hotelPaymentMapping;

    public HotelPaymentMapping getHotelPaymentMapping() {
        if (Hibernate.isInitialized(this.hotelPaymentMapping)) {
            return this.hotelPaymentMapping;
        }
        this.hotelPaymentMapping = new HotelPaymentMapping(this.hotelPaymentMapping.getPaymentId());
        return this.hotelPaymentMapping;
    }

}
