package com.tidesquare.btms.entity;

import com.tidesquare.btms.constant.CountryType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "AirportMain")
public class AirportMain {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "airportMainId")
    public Long id;

    @Column
    public int sectionId;

    @Column
    public String airportSection;

    @Column(length = 100)
    private String code;

    @Column(length = 100)
    private String name;

    @Column(length = 100)
    private String subName;

    @Column(length = 100)
    private String nameEng;

    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private Boolean isUse = true;

    @Column
    private Long airportId;

    @Column
    private Long cityId;

    @Column
    private int displayOrder;

    @Enumerated(EnumType.STRING)
    @Column(length = 10)
    private CountryType countryType;
}