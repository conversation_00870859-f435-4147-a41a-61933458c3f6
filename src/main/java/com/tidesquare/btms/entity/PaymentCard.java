package com.tidesquare.btms.entity;

import org.hibernate.annotations.ColumnTransformer;

import com.tidesquare.btms.constant.CardCompanyCode;
import com.tidesquare.btms.entity.converter.CardCompanyCodeConverter;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "PG_CARD")
@NoArgsConstructor
public class PaymentCard {

    public PaymentCard(Long paymentId) {
        this.paymentId = paymentId;
    }

    @Id
    @Column(name = "payment_id")
    private Long paymentId; // 결제번호

    @Column(name = "mid")
    private String mid; // 상점아이디

    @Column(name = "good_name")
    private String goodName; // 상품명

    @Column(name = "customer_name")
    private String customerName; // 고객명

    @Column(name = "phone_number")
    @ColumnTransformer(read = "DOC_DECRYPT(phone_number)", write = "DOC_ENCRYPT(?)")
    private String phoneNumber; // 전화번호

    @Column(name = "email")
    @ColumnTransformer(read = "DOC_DECRYPT(email)", write = "DOC_ENCRYPT(?)")
    private String email; // Email

    @Column(name = "card_number")
    @ColumnTransformer(read = "DOC_DECRYPT(card_number)", write = "DOC_ENCRYPT(?)")
    private String cardNumber; // 카드번호

    @Column(name = "card_company_code")
    @Convert(converter = CardCompanyCodeConverter.class)
    private CardCompanyCode cardCompanyCode; // 카드사코드

    @Column(name = "card_kind")
    private String cardKind; // 카드 종류 (0 : 신용 / 1 : 체크 / 2 : 기프트)

    @Column(name = "expiry_Y")
    @ColumnTransformer(read = "DOC_DECRYPT(expiry_Y)", write = "DOC_ENCRYPT(?)")
    private String expiryY; // 유효기간_년

    @Column(name = "expiry_M")
    @ColumnTransformer(read = "DOC_DECRYPT(expiry_M)", write = "DOC_ENCRYPT(?)")
    private String expiryM; // 유효기간_월

    @Column(name = "quota_month")
    private String quotaMonth; // 할부개월

    @Column(name = "is_quota_interest")
    private Boolean isQuotaInterest = false; // 무이자여부

    @Column(name = "url")
    private String url; // 가맹점URL

    @Column(name = "cancel_message")
    private String cancelMessage; // 취소사유

    @Column(name = "result_code")
    private String resultCode; // 결과코드

    @Column(name = "result_message")
    private String resultMessage; // 결과메시지

    @Column(name = "t_id")
    private String tId; // PG거래번호

    @Column(name = "approval_number")
    private String approvalNumber; // 승인번호

    @Column(name = "approval_ymd")
    private String approvalYmd; // 승인일자

    @Column(name = "approval_hms")
    private String approvalHms; // 승인시간

    @Column(name = "is_part_cancel")
    private Boolean isPartCancel; // 부분취소 가능 여부(1: 부분취소 가능 /0: 부분취소 불가능)

    @Column(name = "birthday")
    @ColumnTransformer(read = "DOC_DECRYPT(birthday)", write = "DOC_ENCRYPT(?)")
    private String birthday; // 생년월일(YYMMDD)

    @Column(name = "card_password")
    @ColumnTransformer(read = "DOC_DECRYPT(card_password)", write = "DOC_ENCRYPT(?)")
    private String cardPassword; // 카드비밀번호앞2자리

    @Column(name = "moid")
    private String moid; // 주문번호

    @Column(name = "is_corporate_card")
    private Boolean isCorporateCard = false;

    @Column(name = "card_alias")
    private String cardAlias;
}
