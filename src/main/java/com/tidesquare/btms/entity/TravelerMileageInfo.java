package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "TRAVELERMILEAGEINFO")
public class TravelerMileageInfo implements Comparable<TravelerMileageInfo> {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "TRAVELERMILEAGEINFOID")
    Long id;

    @Column(length = 19)
    Long bookingAirTravelerId;

    @Column(length = 100)
    String airline;

    @Column(length = 100)
    String mileageMemberNo;

    @Override
    public int compareTo(TravelerMileageInfo o) {
        return this.getId().compareTo(o.getId());
    }
}
