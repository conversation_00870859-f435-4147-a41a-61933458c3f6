package com.tidesquare.btms.entity;

import com.tidesquare.btms.constant.ChargeType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "PG_CASH")
@NoArgsConstructor
public class PaymentCash implements Comparable<PaymentCash> {

    @Id
    @Column(name = "payment_id")
    private Long paymentId;

    @Id
    @Column(name = "detail_pk")
    private String detailPK; // 참조관리순번(PK) Reference management sequence number (PK)

    @Id
    @Column(name = "charge_type")
    @Enumerated(EnumType.STRING)
    private ChargeType chargeType; //요금구분 Rate classification

    @Column(name = "is_corporation")
    private Integer isCorporation; // 법인여부 Whether corporate or not

    //BTMS 에 등록 필요
    @Column(name = "div_code")
    private String divCode; // 사업장 business code

    @Column(name = "collect_number")
    private String collectNumber; // 입급번호(PK) Deposit Number (PK)

    @Column(name = "master_pk")
    private String masterPK; // 참조관리번호(PK) third party

    @Column(name = "account_manage_code")
    private String accountManageCode; // 계좌관리코드

    @Column(name = "account_manage_name")
    private String accountManageName; // 계좌관리명

    @Column(name = "receipt_date")
    private String receiptDate; // 처리일자 Processing date

    @Column(name = "receipt_amount")
    private Double receiptAmount; // 처리금액 Processing amount

    @Column(name = "receipt_dept_code")
    private String receiptDeptCode; //처리부서코드 Processing Department Code

    @Column(name = "receipt_prsn_code")
    private String receiptPrsnCode; //처리사번 Processor number

    @Column(name = "service_type")
    private String serviceType; //서비스타입 service type

    @Column(name = "dep_cust_code")
    private String depCustCode; //입금 거래처 코드

    @Column(name = "dep_cust_name")
    private String depCustName; //입금 거래처명

    @Column(name = "b_jukyo")
    private String bJukyo; //법인팀 입금 적요

    @Column(name = "remark")
    private String remark; //비고

    @Column(name = "order_id")
    private String orderId; //주문번호

    @Column(name = "ticket_number")
    private String ticketNumber; //티켓번호

    @Column(name = "receipt_kind_code")
    private String receiptKindCode; //입금처리코드

    @Column(name = "match_date")
    private String matchDate; //입금일자

    //NULL 무관
    @Column(name = "hcc_kind_code")
    private String hccKindCode; //혜택종류코드

    @Column(name = "invoice_ym")
    private String invoiceYm; //인보이스년월

    @Column(name = "invoice_date")
    private String invoiceDate; //인보이스일자

    @Column(name = "ref_kind_code")
    private String refKindCode; //참조종류 코드

    @Column(name = "ref_kind_name")
    private String refKindName; //참조종류 코드명

    @Column(name = "fare_code")
    private String fareCode; //요금코드

    @Column(name = "fare_code_name")
    private String fareCodeName; //요금코드명

    //2020.03.18.kulc78 - 은행입금 멀티
    @Column(name = "booking_air_id")
    private Long bookingAirId;

    @Override
    public int compareTo(PaymentCash o) {
        return this.getMatchDate().compareTo(o.getMatchDate());
    }

}
