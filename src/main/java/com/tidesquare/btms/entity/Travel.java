package com.tidesquare.btms.entity;

import java.util.SortedSet;
import java.util.Date;

import org.hibernate.Hibernate;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;
import org.hibernate.annotations.UpdateTimestamp;

import com.tidesquare.btms.constant.BookingAirPaymentStatus;
import com.tidesquare.btms.constant.BookingAirTicketStatus;
import com.tidesquare.btms.constant.TravelBookingType;
import com.tidesquare.btms.constant.TravelStatus;
import com.tidesquare.btms.entity.converter.TravelStatusConverter;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "Travel")
@NoArgsConstructor
public class Travel {
    public Travel(Long id) {
        this.id = id;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "travelId")
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 10)
    private TravelBookingType travelBookingType;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "companyId")
    private Company company;

    public Company getCompany() {
        if (Hibernate.isInitialized(this.company)) {
            return this.company;
        }
        this.company = new Company(this.company.getId());
        return this.company;
    }

    @Column
    private Long workspaceId;

    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private Boolean isOverseas;

    @Column
    private int travelPersonnel;

    @Column(length = 10)
    private String departYmd;

    @Column(length = 10)
    private String returnYmd;

    @Column(name = "statusCodeId")
    @Convert(converter = TravelStatusConverter.class)
    private TravelStatus status;

    @Column(length = 1000)
    private String addRequest;

    @Column(length = 7)
    private String seatZone;

    @Column(length = 1000)
    private String violationReason;

    @Column(name = "OVERSEAS_DOCUMENT_NO", length = 100)
    private String overseasDocumentNo;

    @Column(length = 150)
    private String approvalMemo;

    @Column
    private Date cancelDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "cancelUserId")
    private User cancelUser;

    public User getCancelUser() {
        if (Hibernate.isInitialized(this.cancelUser)) {
            return this.cancelUser;
        }
        this.cancelUser = new User(this.cancelUser.getId());
        return this.cancelUser;
    }

    @Column()
    private Long businessTripId;

    @Column(columnDefinition = "BOOLEAN")
    private Boolean isApproval;

    // 수기 예약 추가
    @Column(columnDefinition = "BOOLEAN")
    private Boolean isGroup = false; // 단체여부

    // 수기 예약 추가
    @Column(length = 20)
    private String groupNumber; // 단체번호

    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private Boolean isDeleted = false;

    @Column
    private Long parentTravelId; // 국내선 가는편 출장아이디

    @Column(length = 20)
    private String travelPlace; // 출장지

    @Enumerated(EnumType.STRING)
    @Column(length = 10)
    private BookingAirPaymentStatus paymentStatus; // 결제상태

    @Enumerated(EnumType.STRING)
    @Column(length = 10)
    private BookingAirTicketStatus ticketStatus; // 티켓상태

    @Column
    @CreationTimestamp(source = SourceType.VM)
    private Date createDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "creator_userId")
    private User creator;

    public User getCreator() {
        if (Hibernate.isInitialized(this.creator)) {
            return this.creator;
        }
        this.creator = new User(this.creator.getId());
        return this.creator;
    }

    @Column
    @UpdateTimestamp(source = SourceType.VM)
    private Date modifyDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "modifier_userId")
    private User modifier;

    public User getModifier() {
        if (Hibernate.isInitialized(this.modifier)) {
            return this.modifier;
        }
        this.modifier = new User(this.modifier.getId());
        return this.modifier;
    }

    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "travelId")
    private SortedSet<TravelCity> travelCities;

    public SortedSet<TravelCity> getTravelCities() {
        if (Hibernate.isInitialized(this.travelCities)) {
            return this.travelCities;
        }
        this.travelCities = null;
        return this.travelCities;
    }

    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "travelId")
    private SortedSet<Traveler> travelers;

    public SortedSet<Traveler> getTravelers() {
        if (Hibernate.isInitialized(this.travelers)) {
            return this.travelers;
        }
        this.travelers = null;
        return this.travelers;
    }

    @OneToOne(fetch = FetchType.LAZY, mappedBy = "travel")
    private BookingAir bookingAir;

    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "bookingId")
    private SortedSet<DocumentNumber> documentNumbers;

    public SortedSet<DocumentNumber> getDocumentNumbers() {
        if (Hibernate.isInitialized(this.documentNumbers)) {
            return this.documentNumbers;
        }
        this.documentNumbers = null;
        return this.documentNumbers;
    }
}