package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "GroupCode")
public class GroupCode {
    @Id
    @Column(nullable = false, length = 100)
    private String groupCode;

    @Column(nullable = false, length = 500)
    private String name;

    @Column(length = 1000)
    private String description;

    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private Boolean isUse = false;
}