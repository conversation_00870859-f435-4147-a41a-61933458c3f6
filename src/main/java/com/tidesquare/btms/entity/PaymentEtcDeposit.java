package com.tidesquare.btms.entity;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.Hibernate;

@Getter
@Setter
@Entity
@Table(name = "PG_ETC_DEPOSIT")
@NoArgsConstructor
public class PaymentEtcDeposit {

    public PaymentEtcDeposit(Long paymentId) {
        this.paymentId = paymentId;
    }

    @Id
    @Column(name = "payment_id")
    private Long paymentId; // 결제번호

    @OneToOne(fetch = FetchType.LAZY)
    @PrimaryKeyJoinColumn()
    private Payment payment; // 결제번호

    public Payment getPayment() {
        if (Hibernate.isInitialized(this.payment)) {
            return this.payment;
        }
        this.payment = new Payment(this.payment.getPaymentId());
        return this.payment;
    }

    @Column(name = "DEPOSIT_YMD")
    private String depositYmd;

    @Column(name = "CUSTOMER_NAME")
    private String customerName;

    @Column(name = "ETC_DEPOSIT_DETAIL")
    private String etcDepositDetail;

    @Column(name = "NOTE")
    private String note;
}
