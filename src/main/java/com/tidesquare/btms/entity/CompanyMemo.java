package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * Created by jungwoo on 2018. 11. 21..
 */
@Getter
@Setter
@Entity
@Table(name = "CompanyMemo")
public class CompanyMemo {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "companyMemoId")
	private Long id;

	@Column(name = "companyId")
	private Long companyId;

	@Column()
	private Date meetingDate;

	@Column(length = 1000)
	private String memo = "";
}