package com.tidesquare.btms.entity.embeddable;

import java.util.ArrayList;
import java.util.List;

import com.tidesquare.btms.constant.AdditionalService;
import com.tidesquare.btms.constant.AirSearchOrderBy;
import com.tidesquare.btms.constant.BookingAdminApprovalType;
import com.tidesquare.btms.constant.ComparativePriceType;
import com.tidesquare.btms.constant.JoinAuthType;
import com.tidesquare.btms.constant.MenuPrimary;
import com.tidesquare.btms.constant.MenuUse;
import com.tidesquare.btms.entity.converter.AdditionalServicesConverter;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Embeddable;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Embeddable
public class BtmsSetting {
    @Column(length = 100, unique = true)
    private String url;

    @Column(length = 100, unique = true)
    private String adminUrl;

    @Column(length = 500)
    private String emailDomain;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private JoinAuthType joinAuthType = JoinAuthType.EmailAuth;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private BookingAdminApprovalType bookingAdminApprovalType = BookingAdminApprovalType.TravelRuleViolation;

    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private boolean isReceiptPrint = true;

    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private boolean isUse = true;

    @Column(nullable = false, length = 10)
    private String applyStartYmd;

    @Column(nullable = false, length = 10)
    private String applyEndYmd;

    // 비교견적사용여부
    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private boolean isComparativePrice = false;

    // 최저가사용여부
    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private boolean isLowestPrice = false;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 11)
    private ComparativePriceType comparativePriceType = ComparativePriceType.Reservation;

    // 거래처별 기본정렬 타입
    @Enumerated(EnumType.STRING)
    @Column(name = "AIR_SEARCH_ORDER_BY", nullable = false, length = 30)
    private AirSearchOrderBy airSearchOrderBy = AirSearchOrderBy.recommend;

    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private boolean isOfflineUse = false;

    @Column(nullable = false, length = 5)
    private String csCallNumber1;

    @Column(nullable = false, length = 5)
    private String csCallNumber2;

    @Column(nullable = false, length = 5)
    private String csCallNumber3;

    // 증빙서류 사용 여부
    @Column(name = "IS_DOCUMENT_EVIDENCE_FILE", columnDefinition = "BOOLEAN")
    private boolean isDocumentEvidenceFile = false;

    // 법인카드로만 결제 여부
    @Column(name = "IS_ONLY_CORPORATE_CARD", columnDefinition = "BOOLEAN")
    private boolean isOnlyCorporateCard = false;

    // 문서번호 사용 여부
    @Column(name = "IS_DOCUMENT_NUMBER_USE", columnDefinition = "BOOLEAN")
    private boolean isDocumentNumberUse = false;

    // DT-30418 : 문서번호 사용 필수 체크 여부
    @Column(name = "IS_DOCUMENT_NUMBER_REQUIRED", columnDefinition = "BOOLEAN")
    private boolean isDocumentNumberRequired = false;

    // 문서번호 사용한도
    @Column(name = "DOCUMENT_NUMBER_USE_COUNT", columnDefinition = "BOOLEAN")
    private Integer documentNumberUseCount = 0;

    @Column(name = "DOCUMENT_NUMBER_USE_TITLE", length = 50)
    private String documentNumberUseTitle = "문서번호";

    // 국내선 자동발권 설정 여부
    @Column(name = "IS_DOMESTIC_AUTO_TICKETING", nullable = false, columnDefinition = "BOOLEAN")
    private boolean isDomesticAutoTicketing = false;

    @Enumerated(EnumType.STRING)
    @Column(name = "MENU_USE")
    private MenuUse menuUse = MenuUse.ALL;

    @Enumerated(EnumType.STRING)
    @Column(name = "MENU_PRIMARY")
    private MenuPrimary menuPrimary = MenuPrimary.AIR;

    @Column(name = "ADDITIONAL_SERVICES", length = 10)
    @Convert(converter = AdditionalServicesConverter.class)
    private List<AdditionalService> additionalServices = new ArrayList<AdditionalService>() {
        {
            add(AdditionalService.INSURANCE);
        }
    };

    /* #region Transient */
    @Transient
    private String[] emailDomainList;

    public String[] getEmailDomainList() {
        if (emailDomain == null || emailDomain.isBlank()) {
            return new String[] {};
        }
        return emailDomain.replace("@", "").split(",");
    }

    public void setEmailDomainList(String[] emailDomainList) {
        this.emailDomain = String.join(",", emailDomainList);
    }

    /* #endregion */
}