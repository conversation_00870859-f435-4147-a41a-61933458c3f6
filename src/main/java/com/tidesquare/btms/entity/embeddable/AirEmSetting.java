package com.tidesquare.btms.entity.embeddable;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Embeddable
public class AirEmSetting {
	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean reservFront = true;
	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean reservAdmin = true;
	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean reservAgency = true;
	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean ticketFront = true;
	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean ticketAdmin = true;
	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean ticketAgency = true;
	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean completeFront = true;
	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean completeAdmin = true;
	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean approvalFront = true;
	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean approvalAdmin = true;
	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean approvalAgency = true;
	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean rejectFront = true;
	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean rejectAdmin = true;
	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean rejectAgency = true;
}
