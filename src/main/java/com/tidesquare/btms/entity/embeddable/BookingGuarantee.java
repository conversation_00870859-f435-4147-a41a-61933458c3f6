package com.tidesquare.btms.entity.embeddable;

import com.tidesquare.btms.constant.CardType;
import com.tidesquare.btms.entity.converter.CardTypeConverter;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Embeddable;
import jakarta.persistence.FetchType;
import jakarta.persistence.ManyToOne;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Embeddable
public class BookingGuarantee {
    @ManyToOne(fetch = FetchType.LAZY)
    @Column(name = "cardTypeCodeId")
    @Convert(converter = CardTypeConverter.class)
    private CardType cardType;

    @Column(length = 100)
    private String cardNumber;

    @Column(length = 100)
    private String cardLimitMonth;

    @Column(length = 100)
    private String cardLimitYear;

    @Column(length = 100)
    private String cardPassword;
}
