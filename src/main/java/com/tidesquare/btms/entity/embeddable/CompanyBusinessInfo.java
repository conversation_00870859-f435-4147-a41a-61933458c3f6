package com.tidesquare.btms.entity.embeddable;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Embeddable
public class CompanyBusinessInfo {
	@Column(length = 100)
	private String contract = ""; // 계약서 작성유무
	@Column(length = 8)
	private String contractDate = ""; // 계약서 작성일
	@Column(length = 100)
	private String autoRenewal = ""; // 계약 자동갱신유무
	@Column(length = 100)
	private String airCommRate = ""; // 항공수수료
	@Column(length = 100)
	private String hotelReward = "";// 호텔 리워드
	@Column(length = 100)
	private String yearlyReward = "";// 연간 리워드
}
