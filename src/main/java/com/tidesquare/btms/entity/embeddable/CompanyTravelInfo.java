package com.tidesquare.btms.entity.embeddable;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Embeddable
public class CompanyTravelInfo {
	@Column(length = 100)
	private String travelProcess = ""; // 진행방식
	@Column(length = 100)
	private String majorCountry = ""; // 주요 출장지
	@Column(length = 100)
	private String majorSeatClass = ""; // 주요 class
	@Column(length = 100)
	private String preferredAirline = ""; // 선호항공사
	@Column(length = 100)
	private String airlineContract = ""; // 항공사 계약
	@Column(length = 100)
	private String airPaymentMeans = ""; // 지불수단
	@Column(length = 100)
	private String preferredHotel = ""; // 선호 호텔
	@Column(length = 100)
	private String roomClass = "";// 투숙규정
	@Column(length = 100)
	private String hotelPaymentMeans = "";// 지불수단
	@Column(length = 100)
	private String visaProcess = "";// 진행절차
	@Column(length = 100)
	private String visaPaymentMeans = "";// 지불수단

}
