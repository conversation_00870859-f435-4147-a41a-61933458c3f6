package com.tidesquare.btms.entity;

import java.util.Date;

import org.hibernate.Hibernate;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;

import com.tidesquare.btms.constant.ContactRequestStatus;
import com.tidesquare.btms.constant.ContactRequestType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "contact_request")
@NoArgsConstructor
public class ContactRequest {
    public ContactRequest(Long id) {
        this.id = id;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CONTACT_REQUEST_ID")
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 30, name = "REQUEST_TYPE")
    private ContactRequestType requestType = ContactRequestType.OTHER;

    @Column(nullable = false, length = 100, name = "NAME")
    private String name = "";

    @Column(nullable = false, length = 200, name = "BUSINESS_NAME")
    private String businessName = "";

    @Column(nullable = false, length = 40, name = "EMAIL")
    private String email = "";

    @Column(nullable = false, length = 12, name = "PHONE_NUMBER")
    private String phoneNumber = "";

    @Column(nullable = false, length = 2000, name = "MESSAGE")
    private String message = "";

    @Column(nullable = false, length = 2000, name = "NOTE")
    private String note = "";

    @Column(nullable = false, length = 10, name = "STATUS")
    @Enumerated(EnumType.STRING)
    private ContactRequestStatus status = ContactRequestStatus.NEW;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "RESPONDENT_ID")
    private TravelAgencyUser respondent;

    public TravelAgencyUser getRespondent() {
        if (Hibernate.isInitialized(this.respondent)) {
            return this.respondent;
        }
        this.respondent = new TravelAgencyUser(this.respondent.getId());
        return this.respondent;
    }

    @Column(name = "ANSWERED_AT")
    private Date answeredAt;

    @Column(name = "CREATE_DATE")
    @CreationTimestamp(source = SourceType.VM)
    private Date createDate;
}
