package com.tidesquare.btms.entity;

import lombok.Getter;
import lombok.Setter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;

import java.util.SortedSet;

import org.hibernate.Hibernate;

@Getter
@Setter
@Entity
@Table(name = "BookingAirLowestDetail")
public class BookingAirLowestDetail implements Comparable<BookingAirLowestDetail> {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "bookingAirLowestDetailId")
	private Long id;

	@Column
	private Long bookingAirLowestId;

	//여정SEQ
	@Column(length = 2)
	private Integer itinerarySeq;

	//총시간
	@Column(length = 2)
	private String totalHour;

	//총시간
	@Column(length = 2)
	private String totalMin;

	//총 비행시간 시
	@Column(length = 2)
	private String flightHour;

	//총 비행시간 분
	@Column(length = 2)
	private String flightMin;

	//경유횟수
	@Column(length = 2)
	private Integer stopoverNo;

	@Column(length = 10)
	private String departureTerminal; // 출발터미널

	@Column(length = 10)
	private String arrivalTerminal; // 도착터미널

	@Column(columnDefinition = "BOOLEAN")
	private Boolean seatWaiting; // 좌석 확정/대기

	@OneToMany(fetch = FetchType.LAZY)
	@JoinColumn(name = "bookingAirLowestDetailId")
	private SortedSet<BookingAirLowestFlight> flightDetails;

	public SortedSet<BookingAirLowestFlight> getFlightDetails() {
		if (Hibernate.isInitialized(this.flightDetails)) {
			return this.flightDetails;
		}

		this.flightDetails = null;
		return this.flightDetails;
	}

	@Override
	public int compareTo(BookingAirLowestDetail o) {
		return this.getId().compareTo(o.getId());
	}
}
