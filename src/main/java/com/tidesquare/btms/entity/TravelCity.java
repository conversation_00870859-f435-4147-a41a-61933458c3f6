package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "TRAVEL_CITY")
public class TravelCity implements Comparable<TravelCity> {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "travelId")
    private Long travelId;

    @Column(name = "travelCityId")
    private Long travelCityId;

    @Override
    public int compareTo(TravelCity o) {
        return this.getId().compareTo(o.getId());
    }
}
