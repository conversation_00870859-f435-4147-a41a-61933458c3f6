package com.tidesquare.btms.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;
import org.hibernate.annotations.UpdateTimestamp;

import java.util.Date;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "Airline")
@NoArgsConstructor
public class Airline {
	public Airline(Long id) {
		this.id = id;
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "airlineId")
	private Long id;

	@Column(unique = true, nullable = false, length = 5)
	private String code;

	@Column(length = 3)
	private String codeNo;

	@Column(length = 60)
	private String name;

	@Column(length = 60)
	private String nameEng;

	@Column(length = 1000)
	private String note;

	@Column(length = 20)
	private String alliance;

	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private Boolean isUse = true;

	// 상용팀
	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private Boolean isCommercial = false;

	// 법인팀
	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private Boolean isCorporation = false;

	@Column
	@CreationTimestamp(source = SourceType.VM)
	private Date createDate;

	@Column
	@UpdateTimestamp(source = SourceType.VM)
	private Date modifyDate;
}