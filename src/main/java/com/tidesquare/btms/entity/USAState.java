package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "usa_states")
@NoArgsConstructor
public class USAState {
	public USAState(String code) {
		this.code = code;
	}

	@Id
	@Column(name = "code", length = 3)
	private String code;

	@Column(name = "name", length = 100, nullable = false)
	private String name;

	@Column(name = "name_en", length = 100)
	private String nameEn;
}