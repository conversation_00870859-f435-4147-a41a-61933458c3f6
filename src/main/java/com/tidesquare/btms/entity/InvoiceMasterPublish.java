package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "INVC_MASTER_PUBLISH")
@NoArgsConstructor
public class InvoiceMasterPublish {
    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "invoice_id")
    private InvoiceMaster invoice;

    @Id
    @Column(name = "booking_air_id")
    private Long bookingAirId;

    @Id
    @Column(name = "ticket_number")
    private String ticketNumber;
}
