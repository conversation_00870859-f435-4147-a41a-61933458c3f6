package com.tidesquare.btms.entity;

import com.tidesquare.btms.constant.PnrDataHistoryType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Lob;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "PNRDATAHISTORY")
public class PnrDataHistory {
    @Lob
    @Column
    private String pnrData;

    @Id
    @Column
    private Long bookingAirId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "manager")
    private User manager; // 예약자정보(CRS에서 예약한 경우 필수 예약자)

    @Id
    @Column
    private Date createDate;

    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private PnrDataHistoryType pnrDataHistoryType;
}