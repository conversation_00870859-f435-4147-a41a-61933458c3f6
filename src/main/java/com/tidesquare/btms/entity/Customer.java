package com.tidesquare.btms.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;

import java.util.SortedSet;

import org.hibernate.Hibernate;

import com.tidesquare.btms.constant.SearchAuth;

@Getter
@Setter
@Entity
@Table(name = "Customer")
@NoArgsConstructor
public class Customer extends User {
	public Customer(Long id) {
		super(id);
	}

	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean isEmailReceive = true;

	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean isSmsReceiveYn = true;

	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean isAdmin;

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "agreementId")
	private Agreement agreement;

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "customerPassportId")
	private CustomerPassport customerPassport;

	public CustomerPassport getCustomerPassport() {
		if (Hibernate.isInitialized(this.customerPassport)) {
			return this.customerPassport;
		}
		this.customerPassport = new CustomerPassport(this.customerPassport.getId());
		return this.customerPassport;
	}

	@OneToMany(fetch = FetchType.LAZY)
	@JoinColumn(name = "userId")
	private SortedSet<CustomerVisa> customerVisas;

	public SortedSet<CustomerVisa> getCustomerVisas() {
		if (Hibernate.isInitialized(this.customerVisas)) {
			return this.customerVisas;
		}
		this.customerVisas = null;
		return this.customerVisas;
	}

	@OneToMany(fetch = FetchType.LAZY)
	@JoinColumn(name = "userId")
	private SortedSet<CustomerAirline> customerAirlines;

	public SortedSet<CustomerAirline> getCustomerAirlines() {
		if (Hibernate.isInitialized(this.customerAirlines)) {
			return this.customerAirlines;
		}
		this.customerAirlines = null;
		return this.customerAirlines;
	}

	@Column(columnDefinition = "BOOLEAN")
	private Boolean isAdminAdd;

	@Column(length = 20)
	private String ssoUserId;

	@OneToMany(fetch = FetchType.LAZY)
	@JoinColumn(name = "userId")
	private SortedSet<MileageInfo> mileageInfos;

	public SortedSet<MileageInfo> getMileageInfos() {
		if (Hibernate.isInitialized(this.mileageInfos)) {
			return this.mileageInfos;
		}
		this.mileageInfos = null;
		return this.mileageInfos;
	}

	@Enumerated(EnumType.STRING)
	@Column(nullable = false, length = 5)
	private SearchAuth searchAuth = SearchAuth.AUTHA;
}