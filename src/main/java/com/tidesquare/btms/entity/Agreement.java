package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "Agreement")
public class Agreement {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "agreementId")
    private Long id;

    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private boolean isAgreeService = true; // 이용약관 동의여부 (필수)

    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private boolean isAgreePrivacy = true; // 개인정보 수집 및 이용에 대한 안내 동의여부 (필수)

    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private boolean isAgreePrivacyId = true; // 고유식별정보 처리에 대한 안내 동의여부 (필수)

    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private boolean isAgreePrivacyProvide = true; // 개인정보 제3자 제공 안내 동의여부 (필수)

    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private boolean isAgreePrivacyMarketing = true; // 마케팅 및 광고에 활용 동의여부 (선택)

    private Date agreeDate = new Date();
}