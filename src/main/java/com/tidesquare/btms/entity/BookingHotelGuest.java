package com.tidesquare.btms.entity;

import org.hibernate.Hibernate;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "BOOKING_HOTEL_GUEST")
public class BookingHotelGuest implements Comparable<BookingHotelGuest> {

    @Id
    @Column(name = "BOOKING_HOTEL_ID")
    private long bookingHotelId;

    @Id
    @Column(name = "ROOM_INDEX")
    private int roomIndex;

    @Id
    @Column(name = "GUEST_ORDER_SEQ")
    private int guestOrderSeq;

    @Column(name = "AGE_GUBUN")
    private String ageGubun;

    @Column(name = "FIRST_NAME")
    private String firstName;

    @Column(name = "LAST_NAME")
    private String lastName;

    @Column(name = "GUEST_NAME")
    private String guestName;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "GUEST_USER_ID")
    private Customer guestUser;

    public Customer getGuestUser() {
        if (Hibernate.isInitialized(this.guestUser)) {
            return this.guestUser;
        }
        this.guestUser = new Customer(this.guestUser.getId());
        return this.guestUser;
    }

    @Override
    public int compareTo(BookingHotelGuest o) {
        if (this.getRoomIndex() != o.getRoomIndex()) {
            return this.getRoomIndex() - o.getRoomIndex();
        }
        return this.getGuestOrderSeq() - o.getGuestOrderSeq();
    }
}
