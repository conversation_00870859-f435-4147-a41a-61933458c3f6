package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "TravelAgencyUser")
@NoArgsConstructor
public class TravelAgencyUser extends Customer {
	public TravelAgencyUser(Long id) {
		super(id);
	}

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "menuRoleId")
	private MenuRole travelAgencyMenuRole;

	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private Boolean isUse = true;

	@Column
	private Date approvalDate;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "approvalUserId")
	private User approvalUser;

	@Column(length = 20)
	private String sineCode4Amadeus;

	@Column(length = 20)
	private String sineCode4Sabre;

	@Column(length = 20)
	private String sineCode4SabreDomestic;
}