package com.tidesquare.btms.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Lob;
import jakarta.persistence.Table;

import org.hibernate.annotations.Nationalized;

import jakarta.persistence.Column;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "TRAVEL_FARE_RULE_RECORD")
@NoArgsConstructor
@AllArgsConstructor
public class TravelFareRuleRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    Long id;

    @Column(name = "TRAVELID")
    private Long travelId;

    @Lob
    @Nationalized
    @Column(name = "FARERULESTRING")
    private String fareRuleStr;
}
