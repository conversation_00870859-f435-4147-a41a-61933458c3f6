package com.tidesquare.btms.entity;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

import org.hibernate.Hibernate;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;
import org.hibernate.annotations.UpdateTimestamp;

import com.tidesquare.btms.constant.Role;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "MenuRole")
public class MenuRole {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "menuRoleId")
    private Long id;

    @Column(nullable = false, length = 200)
    private String name;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 30)
    private Role role;

    @Column(length = 1000)
    private String description;

    @Column
    @CreationTimestamp(source = SourceType.VM)
    private Date createDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "creator_userId")
    private User creator;

    public User getCreator() {
        if (Hibernate.isInitialized(this.creator)) {
            return this.creator;
        }
        this.creator = new User(this.creator.getId());
        return this.creator;
    }

    @Column
    @UpdateTimestamp(source = SourceType.VM)
    private Date modifyDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "modifier_userId")
    private User modifier;

    public User getModifier() {
        if (Hibernate.isInitialized(this.modifier)) {
            return this.modifier;
        }
        this.modifier = new User(this.modifier.getId());
        return this.modifier;
    }
}