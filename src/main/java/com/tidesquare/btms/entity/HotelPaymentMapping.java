package com.tidesquare.btms.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import org.hibernate.Hibernate;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;

@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "hotel_payment_mapping")
public class HotelPaymentMapping {

	public HotelPaymentMapping(Long paymentId) {
		this.paymentId = paymentId;
	}

	@Id
	@Column(name = "PAYMENT_ID")
	private Long paymentId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "RESERVATION_ID")
	private BookingHotel bookingHotel;

	public BookingHotel getBookingHotel() {
		if (Hibernate.isInitialized(this.bookingHotel)) {
			return this.bookingHotel;
		}
		this.bookingHotel = new BookingHotel(this.bookingHotel.getId());
		return this.bookingHotel;
	}
}