package com.tidesquare.btms.entity;

import java.util.SortedSet;

import org.hibernate.Hibernate;

import com.tidesquare.btms.constant.ExceptTimeType;
import com.tidesquare.btms.constant.SeatClass;
import com.tidesquare.btms.constant.TravelRuleType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "travel_rule")
public class TravelRule {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "TRAVEL_RULE_ID")
    private Long travelRuleId;

    @Column(name = "WORKSPACE_ID")
    private Long workspaceId;

    @Enumerated(EnumType.STRING)
    @Column(name = "TRAVEL_RULE_TYPE")
    private TravelRuleType travelRuleType;

    @Column(name = "TRAVEL_LIMIT_DAY")
    private Integer travelLimitDay;

    @Enumerated(EnumType.STRING)
    @Column(name = "BASE_SEAT_TYPE_CODE")
    private SeatClass baseSeatTypeCode;

    @Column(name = "EXCEPT_BORDING_TIME")
    private Integer exceptBordingTime;

    @Enumerated(EnumType.STRING)
    @Column(name = "EXCEPT_SEAT_TYPE_CODE")
    private SeatClass exceptSeatTypeCode;

    @Column(name = "ETC")
    private String etc;

    @Enumerated(EnumType.STRING)
    @Column(name = "EXCEPT_TIME_TYPE")
    private ExceptTimeType exceptTimeType;

    @Column(name = "IS_EXCEPT_USE")
    private Boolean isExceptUse;

    @OneToMany
    @JoinColumn(name = "TRAVEL_RULE_ID")
    private SortedSet<ExceptCity> exceptCities;

    public SortedSet<ExceptCity> getExceptCities() {
        if (Hibernate.isInitialized(this.exceptCities)) {
            return this.exceptCities;
        }
        this.exceptCities = null;
        return this.exceptCities;
    }
}
