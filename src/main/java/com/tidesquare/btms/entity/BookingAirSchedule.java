package com.tidesquare.btms.entity;

import java.util.Date;

import org.hibernate.Hibernate;

import com.tidesquare.btms.constant.SeatClass;
import com.tidesquare.btms.entity.converter.SeatClassConverter;
import com.tidesquare.btms.service.AirlineService;
import com.tidesquare.btms.service.AirportService;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "BookingAirSchedule")
public class BookingAirSchedule implements Comparable<BookingAirSchedule> {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "bookingAirScheduleId")
	private Long id;

	@Column
	private Long bookingAirId;

	@Column
	private int scheduleSeqNo;

	@Column
	private Long airlineId;

	@Transient
	private Airline airline;

	public Airline getAirline() {
		if (this.airlineId == null) {
			return null;
		}

		return AirlineService.findByIdStatic(this.airlineId);
	}

	@Column(length = 20)
	private String airlineFlightNo;

	@Column
	private Date reserveDate;

	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean isVia = false;

	@Column(length = 10)
	private String userFareBasisCd;

	@Column(length = 10)
	private String agencyFareBasisCd;

	@Column
	private Long opAirlineId;

	@Transient
	private Airline opAirline;

	public Airline getOpAirline() {
		if (this.opAirlineId == null) {
			return null;
		}

		return AirlineService.findByIdStatic(this.opAirlineId);
	}

	@Column(length = 1000)
	private String opAirlineText;

	@Column
	private Date fromDate;

	@Column
	private Long fromAirportId;

	@Transient
	private Airport fromAirport;

	public Airport getFromAirport() {
		if (this.fromAirportId == null) {
			return null;
		}

		return AirportService.findByIdStatic(this.fromAirportId);
	}

	@Column
	private Date toDate;

	@Column
	private Long toAirportId;

	@Transient
	private Airport toAirport;

	public Airport getToAirport() {
		if (this.toAirportId == null) {
			return null;
		}

		return AirportService.findByIdStatic(this.toAirportId);
	}

	@Column(name = "seatTypeCodeId")
	@Convert(converter = SeatClassConverter.class)
	private SeatClass seatType;

	@Column(length = 10)
	private String bookingClassCode;

	@Column(length = 10)
	private String gdsBookingStatusCode;

	@Column(length = 4)
	private String leadTime; // HHMM형식

	@Column(length = 4)
	private String groundTime; // HHMM형식

	@Column(nullable = true)
	private int flightMile;

	@Column(nullable = true)
	private int seatCountAdult;

	@Column(nullable = true)
	private int seatCountYoung;

	@Column(nullable = true)
	private int seatCountBaby;

	@Column
	private Date modifyDate;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(nullable = true)
	private User modifier;

	public User getModifier() {
		if (Hibernate.isInitialized(this.modifier)) {
			return this.modifier;
		}
		this.modifier = new User(this.modifier.getId());
		return this.modifier;
	}

	@Column(length = 10)
	private String cnxType;

	@Column(length = 10)
	private String breakPointYn;

	@Column(length = 50)
	private String baggageAllow;

	// 수기 예약 정보 추가
	@Column(length = 400)
	private String note; // 비고

	@Column(length = 20)
	private String departureTerminal;

	@Column(length = 20)
	private String arrivalTerminal;

	@Override
	public int compareTo(BookingAirSchedule o) {
		return this.getId().compareTo(o.getId());
	}

	@Transient
	private String totalTime; // HHMM형식
}