package com.tidesquare.btms.entity;

import org.hibernate.Hibernate;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;

import com.tidesquare.btms.constant.SectionType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.SortedSet;

@Getter
@Setter
@Entity
@Table(name = "CompareAirSchedule")
public class CompareAirSchedule {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "compareAirScheduleId")
	private Long id;

	@OneToMany(fetch = FetchType.LAZY)
	@JoinColumn(name = "compareAirScheduleId")
	private SortedSet<CompareScheduleDetail> compareScheduleDetails;

	public SortedSet<CompareScheduleDetail> getCompareScheduleDetails() {
		if (Hibernate.isInitialized(this.compareScheduleDetails)) {
			return this.compareScheduleDetails;
		}
		this.compareScheduleDetails = null;
		return this.compareScheduleDetails;
	}

	@Column(columnDefinition = "BOOLEAN")
	private Boolean isMasterSchedule;

	@Column
	private Date lastTicketDate;

	// 출발일 기준 발권일수 ex)출발일 기준 20일
	@Column(length = 3)
	private Integer lastTicketDay;

	// 1인 총 금액 (발권금액 + 발권수수료)
	@Column
	private Long totalAmount;

	// 1인 발권금액 (항공운임 + 세금)
	@Column
	private Long ticketAmount;

	// 1인 항공운임 (Q-Charge 포함)
	@Column
	private Long fareAmount;

	// 1인 세금 (유류할증료 포함)
	@Column
	private Long taxAmount;

	// 1인 발권수수료
	@Column
	private Long tasfAmount;

	// 비교견적 그룹 아이디
	@Column
	private Long compareAirGroupId;

	@Column(columnDefinition = "BOOLEAN")
	private Boolean isCorporateFare;

	@Column(columnDefinition = "BOOLEAN")
	private Boolean isRuleViolation;

	@Enumerated(EnumType.STRING)
	@Column(nullable = false, length = 20)
	private SectionType sectionType;

	@Column(length = 2)
	private String representTravelIndex;

	@Column(length = 100)
	private String baggageAllowance;

	@Column
	@CreationTimestamp(source = SourceType.VM)
	private Date createDate;

	@Column
	private long bookingAirId;
}
