package com.tidesquare.btms.entity;

import com.tidesquare.btms.constant.ChargeType;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "PG_PAYMENT_CHARGE")
@NoArgsConstructor
public class PaymentCharge {
    @Id
    @Column(name = "payment_id")
    private Long paymentId;

    @Id
    @Column(name = "charge_type")
    @Enumerated(EnumType.STRING)
    private ChargeType chargeType; //요금구분

    @Column(name = "amount")
    private Double amount; // 요금

    @Id
    @Column(name = "booking_air_id")
    private Long bookingAirId;

    @Id
    @Column(name = "ticket_number")
    private String ticketNumber;
}
