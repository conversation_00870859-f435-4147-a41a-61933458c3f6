package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "CompareFlightDetail")
public class CompareFlightDetail implements Comparable<CompareFlightDetail> {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "CompareFlightDetailId")
	private Long id;

	// 여정별스퀘줄Seq
	@Column(length = 2)
	private Integer scheduleSeqNo;

	// 항공사코드
	@Column(length = 2)
	private String airlineCode;

	// 항공사
	@Column(length = 50)
	private String airlineName;

	// 실제운항항공사
	@Column(length = 50)
	private String operatingAirlineCode;

	// 실제운항항공사
	@Column(length = 50)
	private String operatingAirlineName;

	// 항공편
	@Column(length = 4)
	private String airlineFlightNo;

	// 출발공항
	@Column(length = 3)
	private String fromAirportCode;

	// 출발공항
	@Column(length = 50)
	private String fromAirportName;

	// 출발일시
	@Column
	private Date fromDate;

	// 도착공항
	@Column(length = 3)
	private String toAirportCode;

	// 도착공항
	@Column(length = 50)
	private String toAirportName;

	// 도착일시
	@Column
	private Date toDate;

	// 소요시간(시간)
	@Column(length = 4)
	private String leadHour; // HHMM형식

	// 소요시간(분)
	@Column(length = 4)
	private String leadMin; // HHMM형식

	// 경유대기시간(시간)
	@Column(length = 4)
	private String groundHour;

	// 경유대기시간(분)
	@Column(length = 4)
	private String groundMin;

	// 예약 좌석등급 코드 (seatClass)
	@Column(length = 4)
	private String bookingClassCode;

	// 예약 좌석등급명
	@Column(length = 20)
	private String seatClassName;

	// 예약 좌석수
	@Column(length = 4)
	private Integer numberOfSeats;

	// 예약확정
	@Column(columnDefinition = "BOOLEAN")
	private Boolean gdsBookingStatusCode;

	// 소요일자
	@Column(length = 2)
	private Integer dateVariation;

	@Column
	private Long compareScheduleDetailId;

	@Override
	public int compareTo(CompareFlightDetail o) {
		return this.getId().compareTo(o.getId());
	}
}
