package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;

import java.util.Date;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "BOOKING_HISTORY")
public class BookingHistory {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "bookingHistoryId")
	private Long id;

	@Column(name = "travelId", nullable = false)
	private Long travelId;

	@Column
	private Long bookingAirId;

	@Column(length = 50)
	private String fieldName;

	@Column(length = 50)
	private String fieldKrName;

	@Column(length = 255)
	private String originValue;

	@Column(length = 255)
	private String changeValue;

	@Column(name = "modifierId")
	private Long modifierId;

	@Column
	@CreationTimestamp(source = SourceType.VM)
	private Date modifyDate;
}
