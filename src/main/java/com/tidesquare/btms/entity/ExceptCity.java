package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "except_city")
public class ExceptCity {
    @Id
    @Column(name = "TRAVEL_RULE_ID")
    private Long travelRuleId;

    @Id
    @Column(name = "POSITION_ID")
    private Long positionId;

    @Id
    @Column(name = "CITY_ID")
    private Long cityId;

    @Column(name = "CITY_CODE")
    private String cityCode;

    @Column(name = "CITY_NAME")
    private String cityName;
}
