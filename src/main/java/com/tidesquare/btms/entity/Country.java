package com.tidesquare.btms.entity;

import com.tidesquare.btms.constant.Continent;
import com.tidesquare.btms.entity.converter.ContinentConverter;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "Country")
@NoArgsConstructor
public class Country {
    public Country(Long id) {
        this.id = id;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "countryId")
    private Long id;

    @Column(nullable = false, length = 2)
    private String code2;

    @Column(nullable = false, length = 3)
    private String code3;

    @Column(name = "continentCodeId")
    @Convert(converter = ContinentConverter.class)
    private Continent continent;

    @Column(nullable = false, length = 100)
    private String name;

    @Column(length = 100)
    private String nameEng;

    @Column
    private int displayOrder;

    @Column(columnDefinition = "FLOAT")
    private Double westLongitude;

    @Column(columnDefinition = "FLOAT")
    private Double eastLongitude;

    @Column(columnDefinition = "FLOAT")
    private Double northLatitude;

    @Column(columnDefinition = "FLOAT")
    private Double southLatitude;

    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private Boolean isVisaNecessary = true;

    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private Boolean isUse = true;
}