package com.tidesquare.btms.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "IdentificationRequest")
public class IdentificationRequest {

    public enum Means {
        Email,
        Mobile
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column
    private String verificationCode;

    @Column
    @Enumerated(EnumType.STRING)
    private Means verificationMeans;

    @Column
    private String verificationMeansAccount;

    @Column
    private Date requestDate;

    @Column
    @CreationTimestamp(source = SourceType.VM)
    private Date createDate;

}
