package com.tidesquare.btms.entity;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "AIRPAYINFO")
public class AirPayInfo {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "AIRPAYINFOID")
    private Long id;

    @Column(name = "ISAMOUNTVIEW")
    private Boolean isAmountView;

    @Column(name = "isIssueFeeView")
    private Boolean isIssueFeeView;

    @Column(name = "ISTAXANDAMOUNTVIEW")
    private Boolean isTaxAndAmountView;

    @Column(name = "ISTAXVIEW")
    private Boolean isTaxView;

    @Column(name = "ISINVOICEVIEW")
    private Boolean isInvoiceView;

    @Column(name = "ISETICKETVIEW")
    private Boolean isETicketView;

    @Column(name = "ISUSE")
    private Boolean isUse;

    private Long travelId;

    private Long bookingAirTicketId;

    @Column(length = 30, name = "TICKETNO")
    private String ticketNo;

    @Transient
    private Boolean isAmadeusView;

    @Transient
    private Double cardAmount;

    @Transient
    private String airFareTypeValue;

    //    @OneToMany(fetch = FetchType.LAZY, mappedBy = "airPayInfo")
    // @OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    // @JoinColumn(name = "airPayInfoId")
    // private List<AirPayInfoDetail> airPayInfoDetails = new ArrayList<>();

    @Column(name = "CREATEDATE")
    @CreationTimestamp(source = SourceType.VM)
    private Date createDate;

    // @ManyToOne(fetch = FetchType.LAZY)
    // private User creator;

    @Column(name = "MODIFYDATE")
    @UpdateTimestamp(source = SourceType.VM)
    private Date modifyDate;

    // @JsonIgnore
    // @ManyToOne(fetch = FetchType.LAZY)
    // private User modifier;
}