package com.tidesquare.btms.entity;

import com.tidesquare.btms.constant.CompanyType;
import com.tidesquare.btms.constant.InvoiceLanguageType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;
import org.hibernate.annotations.UpdateTimestamp;

@Getter
@Setter
@Entity
@Table(name = "INVOICE_FORM")
@NoArgsConstructor
public class InvoiceForm {

    public InvoiceForm(Long invoiceFormId) {
        this.invoiceFormId = invoiceFormId;
    }

    @Id
    @Column(name = "invoice_form_id")
    private Long invoiceFormId;

    @Column(name = "company_type")
    @Enumerated(EnumType.STRING)
    private CompanyType companyType;

    @Column(name = "name")
    private String name;

    @Column(name = "language_type")
    @Enumerated(EnumType.STRING)
    private InvoiceLanguageType languageType;

    @Column(name = "display_order")
    private Long displayOrder;

    @Column(name = "is_use")
    private boolean isUse = true;

    @Column(name = "create_date")
    @CreationTimestamp(source = SourceType.VM)
    private Date createDate;

    @Column(name = "creator_user_id")
    private Long creatorUserId;

    @Column(name = "modify_date")
    @UpdateTimestamp(source = SourceType.VM)
    private Date modifyDate;

    @Column(name = "modifier_user_id")
    private Long modifyUserId;
}
