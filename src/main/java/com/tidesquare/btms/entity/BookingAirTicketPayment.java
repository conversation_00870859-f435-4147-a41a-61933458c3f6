package com.tidesquare.btms.entity;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;

import com.tidesquare.btms.constant.AirFareType;
import com.tidesquare.btms.constant.PaymentPGType;
import com.tidesquare.btms.constant.PaymentType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "BookingAirTicketPayment")
public class BookingAirTicketPayment {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "bookingAirTicketPaymentId")
	private Long id;

	@Enumerated(EnumType.STRING)
	@Column(length = 8, name = "paymentType")
	private PaymentType paymentType; // 지불유형

	@Column(columnDefinition = "FLOAT", name = "cashAmount")
	private Double cashAmount = 0d; // 현금지불액

	@Column(length = 2, name = "cardType")
	private String cardType; // 카드유형

	@Column(columnDefinition = "FLOAT", name = "cardAmount")
	private Double cardAmount = 0d; // 카드결제액

	@Column(length = 50, name = "agreeNumber")
	private String agreeNumber; // 카드승인번호

	@Column(length = 10, name = "cardCd")
	private String cardCd;

	@Column(length = 20, name = "cardNumber")
	private String cardNumber; // 카드번호

	@Column(length = 10, name = "validYm")
	private String validYm; // 카드유효기간

	@Column(length = 10, name = "installmentsMonths")
	private String installmentsMonths; //할부개월

	@Enumerated(EnumType.STRING)
	@Column(length = 10, name = "airFareType")
	private AirFareType airFareType; // 항공요금구분

	@Enumerated(EnumType.STRING)
	@Column(name = "PGTYPE")
	private PaymentPGType pgType; //PG구분

	@CreationTimestamp(source = SourceType.VM)
	@Column(name = "paymentDate")
	private Date paymentDate; // 결제일자

	// @ManyToOne(fetch = FetchType.LAZY)
	// @JoinColumn(name = "AGENCYUSER")
	// private TravelAgencyUser agencyUser; //결제 처리 관리자

	@Column(length = 2, name = "tasfStatus")
	private String tasfStatus; //tasfStatus V or N

	@Column(name = "APPROVAL_DATE", length = 20)
	private String approvalDate; //gds승인yyyymmddhhmmss
}
