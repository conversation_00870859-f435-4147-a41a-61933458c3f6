package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "Airport")
@NoArgsConstructor
public class Airport {
    public Airport(Long id) {
        this.id = id;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "airportId")
    private Long id;

    @Column(unique = true, nullable = false, length = 5)
    private String code;

    @Column(length = 100)
    private String name;

    @Column(length = 200)
    private String nameEng;

    @Column(length = 200)
    private String nameSynonym1;

    @Column(length = 200)
    private String nameSynonym2;

    @Column
    private Long cityId;

    @Column(columnDefinition = "FLOAT")
    private Double latitude;

    @Column(columnDefinition = "FLOAT")
    private Double longitude;

    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private Boolean isUse = true;
}