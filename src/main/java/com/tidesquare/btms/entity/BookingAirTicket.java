package com.tidesquare.btms.entity;

import com.tidesquare.btms.constant.CommissionAmountNoteType;
import com.tidesquare.btms.constant.GDSType;
import com.tidesquare.btms.constant.UpAmountNoteType;
import com.tidesquare.btms.constant.VoidType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Optional;
import java.util.SortedSet;

import org.hibernate.Hibernate;

@Getter
@Setter
@Entity
@Table(name = "BookingAirTicket")
public class BookingAirTicket implements Comparable<BookingAirTicket> {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "bookingAirTicketId")
	private Long id;

	@Column(name = "ticketAirlineId")
	private Long ticketAirlineId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "manager")
	private User manager; // 2020.02.03.kulc78 - DSR 티켓생성 담당자

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ticketer")
	private User ticketer; // 발권자 - 마지막 담당자

	@Column(length = 30, nullable = false)
	private String ticketNo;

	@Column(length = 30, nullable = false)
	private String conjunctionNo;

	@Column(length = 20)
	private String pnrNo;

	@Enumerated(EnumType.STRING)
	@Column(nullable = false, length = 20)
	private VoidType voidType;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(nullable = true)
	private User passenger;

	@Column(length = 30)
	private String originTicketNo;

	@Column(length = 30)
	private String exchangeTicketNo;

	@Column(length = 50)
	private String exchangeTicketInfo;

	@Column
	private Date ticketDate;

	@Column
	private Date refundDate; // 환불일시

	@Column(length = 10)
	private String creationLocation; // 생성위치

	@Column(length = 8)
	private String ticketYmd;

	@Column(length = 400)
	private String fareCalcInfo;

	@Column
	private Date fromTicketDate;

	@Column
	private Date toTicketDate;

	@Column(name = "fromAirportCode", length = 3)
	private String fromAirportCode;

	@Column(name = "toAirportCode", length = 3)
	private String toAirportCode;

	@Column(columnDefinition = "FLOAT")
	private Double fareAmount = 0d;

	@Column(columnDefinition = "FLOAT")
	private Double dcAmount = 0d;

	@Column(columnDefinition = "FLOAT")
	private Double taxAmount = 0d;

	@Column(columnDefinition = "FLOAT")
	private Double netAmount; // NET = FARE - DC

	@Column(columnDefinition = "FLOAT")
	private Double commissionAmount = 0d; // 취급수수료 = TASF + VAT
											// TAST == 0.0 ==> NET*fAirComRate(해외) or NET*airComRate(국내)
											// ==>(NET+TAX)*fAirComRate(해외) or (NET+TAX)*airComRate(국내)
											// commissionAmount = commissionTasfAmount + commissionVatAmount
	@Column(columnDefinition = "FLOAT")
	private Double commissionTasfAmount = 0d; // TASF
	@Column(columnDefinition = "FLOAT")
	private Double commissionVatAmount = 0d; // VAT

	@Column(columnDefinition = "FLOAT")
	private Double uncutCommissionAmount = 0d; // 절삭전수수료

	@Column(columnDefinition = "FLOAT")
	private Double dsrCommissionAmount = 0d;// DSR 수수료

	@Column(columnDefinition = "FLOAT")
	private Double ticketAmount = 0d; // 발권금액 = Fare - DC + Tax

	@Column(length = 100)
	private String firstName;

	@Column(length = 100)
	private String lastName;

	@Column(columnDefinition = "FLOAT")
	private Double cardAmount = 0d; // 카드발권금액 (Payment로 삭제될 수 있음)

	@Column(columnDefinition = "FLOAT")
	private Double cashAmount = 0d; // 현금발권금액 (Payment로 삭제될 수 있음)

	@Column(columnDefinition = "FLOAT")
	private Double tasfAmount = 0d;// 항공요금과 별도로 계산

	@Column(length = 100)
	private String endorsement;

	@Column(length = 100)
	private String receiveEmail;

	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private Boolean isTicketSend = false;

	@Column
	private Date ticketSendDate;

	@Column(length = 2000)
	private String ticketRemark;

	// @OneToMany(fetch = FetchType.LAZY)
	// @JoinColumn(name = "bookingAirTicketId")
	// private List<BookingAirTicketSchedule> bookingAirTicketSchedules;

	// @OneToMany(fetch = FetchType.LAZY)
	// @JoinColumn(name = "bookingAirTicketId")
	// private List<BookingAirTicketTax> bookingAirTicketTaxes;

	@OneToMany(fetch = FetchType.LAZY)
	@JoinColumn(name = "bookingAirTicketId")
	private SortedSet<BookingAirTicketPayment> bookingAirTicketPayments;

	public SortedSet<BookingAirTicketPayment> getBookingAirTicketPayments() {
		if (Hibernate.isInitialized(this.bookingAirTicketPayments)) {
			return this.bookingAirTicketPayments;
		}
		this.bookingAirTicketPayments = null;
		return this.bookingAirTicketPayments;
	}

	// @OneToMany(fetch = FetchType.LAZY)
	// @JoinColumn(name = "bookingAirTicketId")
	// private List<BookingAirTicketRefund> bookingAirTicketRefunds;

	@Column(name = "BOOKINGAIRID")
	private Long bookingAirId;

	@Column(length = 300)
	private String eticket;

	// 세금계산서 발행월
	@Column(length = 6)
	private String taxInvoiceMonth;

	@Column(length = 20, name = "GROUP_NUMBER")
	private String groupNumber;

	@Column(length = 20, name = "OFFICE_ID")
	private String officeId;

	// 결제금액 = 발권금액 + 취급수수료 + VAT + 업금액 = 판매금액과 동일
	@Transient
	public double getPaymentAmount() {
		return ticketAmount
				+ Optional.ofNullable(commissionAmount).orElse(Double.valueOf(0))
				+ Math.round(Optional.ofNullable(commissionVatAmount).orElse(Double.valueOf(0)))
				+ Optional.ofNullable(upAmount).orElse(Double.valueOf(0));
	}

	@Column(columnDefinition = "FLOAT")
	private Double receivableAmount = 0d; // 미수액 = 판매금액 - 입금액 or 환불액

	// 인보이스 발행 개수
	@Transient
	private int invoicePublishCount;

	// 티켓 수기 등록 추가 Column ------------------
	@Enumerated(EnumType.STRING)
	@Column(length = 20)
	private GDSType gdsType;

	@Column(columnDefinition = "FLOAT")
	private Double upAmount = 0d; // 업금액

	@Enumerated(EnumType.STRING)
	@Column(length = 20)
	private UpAmountNoteType upAmountNoteType; // 업금액 비고

	@Column(columnDefinition = "FLOAT")
	private Double wvrAmount = 0d; // WVR 금액

	@Enumerated(EnumType.STRING)
	@Column(length = 20)
	private CommissionAmountNoteType commissionAmountNoteType; // 취급수수로 비고

	@Column(columnDefinition = "FLOAT")
	private Double totalAmount = 0d; // 판매금액

	@Column(columnDefinition = "FLOAT")
	private Double commissionRate = 0d; // 취급수수료율

	@Column(length = 2000)
	private String memo; // 메모

	@Column(name = "attachFileId")
	private Long attachFileId;

	@Column(length = 300)
	private String eticket4Direct;

	@Column(columnDefinition = "FLOAT")
	private Double originCommissionAmount = 0d;// 수수료 = TASF

	@Column(name = "COMPANY_ID")
	private Long companyId;

	@Override
	public int compareTo(BookingAirTicket o) {
		return this.getId().compareTo(o.getId());
	}
}
