package com.tidesquare.btms.entity;

import lombok.Getter;
import lombok.Setter;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "CityMain")
public class CityMain {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "citytMainId")
    public Long id;

    @Column
    public int sectionId;

    @Column
    public String citySection;

    @Column
    private Long cityId;

    @Column(length = 100)
    private String cityCode;

    @Column(length = 100)
    private String name;

    @Column(length = 100)
    private String nameEng;

    @Column
    private int displayOrder;

    @Column
    private Long countryId;

    @Column(length = 100)
    private String countryName;

    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private Boolean isOverseas = true;

    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private Boolean isUse = true;

    @Column(length = 100)
    private String domesticAreaCode;

    @Column(length = 100)
    private String domesticAreaCodeName;

}