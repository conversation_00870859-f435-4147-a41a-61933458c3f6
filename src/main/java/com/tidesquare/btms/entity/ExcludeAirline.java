package com.tidesquare.btms.entity;

import com.tidesquare.btms.service.AirlineService;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "EXCLUDEAIRLINE")
public class ExcludeAirline implements Comparable<ExcludeAirline> {
    @Id
    @Column(name = "companyid")
    private Long companyId;

    @Id
    @Column(name = "airlineid")
    private Long airlineId;

    @Transient
    private Airline airline;

    public Airline getAirline() {
        if (this.airlineId == null) {
            return null;
        }

        return AirlineService.findByIdStatic(this.airlineId);
    }

    @Override
    public int compareTo(ExcludeAirline o) {
        return this.getAirlineId().compareTo(o.getAirlineId());
    }
}
