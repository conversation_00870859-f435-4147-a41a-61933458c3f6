package com.tidesquare.btms.entity;

import lombok.Getter;
import lombok.Setter;

import java.util.SortedSet;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.Hibernate;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import com.tidesquare.btms.constant.GDSType;
import com.tidesquare.btms.constant.InflowBookingType;
import com.tidesquare.btms.constant.SectionType;
import com.tidesquare.btms.utils.DateUtil;
import com.tidesquare.btms.utils.StringUtils;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.Lob;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "BookingAir")
public class BookingAir implements Comparable<BookingAir> {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "bookingAirId")
	private Long id;

	@Enumerated(EnumType.STRING)
	@Column(length = 10)
	private InflowBookingType inflowBookingType;

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "travelId")
	private Travel travel; // 발권자 - 마지막 담당자

	public Travel getTravel() {
		if (Hibernate.isInitialized(this.travel)) {
			return this.travel;
		}
		this.travel = new Travel(this.travel.getId());
		return this.travel;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "manager")
	private User manager; // 담당자 - 최초 CRS에서 예약한 예약자

	public User getManager() {
		if (Hibernate.isInitialized(this.manager)) {
			return this.manager;
		}
		this.manager = new User(this.manager.getId());
		return this.manager;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "bookingAirLowestId")
	private BookingAirLowest bookingAirLowest;

	public BookingAirLowest getBookingAirLowest() {
		if (Hibernate.isInitialized(this.bookingAirLowest)) {
			return this.bookingAirLowest;
		}
		this.bookingAirLowest = new BookingAirLowest(this.bookingAirLowest.getId());
		return this.bookingAirLowest;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ticketer")
	private User ticketer; // 발권자 - 마지막 담당자

	public User getTicketer() {
		if (Hibernate.isInitialized(this.ticketer)) {
			return this.ticketer;
		}
		this.ticketer = new User(this.ticketer.getId());
		return this.ticketer;
	}

	@Enumerated(EnumType.STRING)
	@Column(nullable = false, length = 20)
	private SectionType sectionType;

	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private Boolean isAppointReturnDay = true;

	@Column(length = 10)
	private String stayTerm;

	@Column
	private Date bookingDate;

	@Column(length = 20)
	private String pnrNo;

	@Column(length = 100)
	private String otherPnrNo;

	@Lob
	@Column
	private String pnrData;

	@Column(length = 1000)
	private String managerRemark;

	@Column
	private Date ticketDate;

	@Column
	private Date viewTicketDate;

	@Enumerated(EnumType.STRING)
	@Column(nullable = false, length = 20)
	private GDSType gdsType;

	@Column
	private Long ticketAirlineId;

	@OneToMany(fetch = FetchType.LAZY)
	@JoinColumn(name = "bookingAirId")
	private SortedSet<BookingAirTraveler> bookingAirTravelers;

	public SortedSet<BookingAirTraveler> getBookingAirTravelers() {
		if (Hibernate.isInitialized(this.bookingAirTravelers)) {
			return this.bookingAirTravelers;
		}
		this.bookingAirTravelers = null;
		return this.bookingAirTravelers;
	}

	@OneToMany(fetch = FetchType.LAZY)
	@JoinColumn(name = "bookingAirId")
	private SortedSet<BookingAirSchedule> bookingAirSchedules;

	public SortedSet<BookingAirSchedule> getBookingAirSchedules() {
		if (Hibernate.isInitialized(this.bookingAirSchedules)) {
			return this.bookingAirSchedules;
		}
		this.bookingAirSchedules = null;
		return this.bookingAirSchedules;
	}

	@OneToMany(fetch = FetchType.LAZY)
	@JoinColumn(name = "bookingAirId")
	private SortedSet<BookingAirTicket> bookingAirTickets;

	public SortedSet<BookingAirTicket> getBookingAirTickets() {
		if (Hibernate.isInitialized(this.bookingAirTickets)) {
			return this.bookingAirTickets;
		}

		this.bookingAirTickets = null;
		return this.bookingAirTickets;
	}

	@Column(columnDefinition = "FLOAT")
	private Double reserveAmount;

	@Column(updatable = false)
	@CreationTimestamp
	private Date createDate;

	@Column
	@UpdateTimestamp
	private Date modifyDate;

	@Column(length = 20)
	private String iataCode; // 수기 예약 > IATA 코드

	@Column(length = 9)
	private String guessAmount; // 예상요금

	@Column(length = 20)
	private String ticketRequest;

	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private Boolean isApisUpdateFailed = false;

	@Column(name = "IS_DOME_AUTOMATIC_TICKET", nullable = false, columnDefinition = "BOOLEAN")
	private Boolean isDomesticAutomaticTicket = false;

	@Column(name = "OFFICE_ID", length = 20)
	private String officeId;

	@Column(length = 100)
	private String otherPnrNoBackup;

	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private Boolean viewTicketDateModified = false;

	@Column
	private Long managerDepartment;

	@Column(name = "ORDER_ID", length = 40, nullable = false)
	private String orderID;

	@Column(name = "ORDER_KEY", length = 40, nullable = false)
	private String orderKey;

	@Override
	public int compareTo(BookingAir o) {
		return this.getId().compareTo(o.getId());
	}

	public Map<Integer, List<BookingAirSchedule>> getBookingAirScheduleMap() {
		SortedSet<BookingAirSchedule> allBookingAirSchedules = this.getBookingAirSchedules();
		if (allBookingAirSchedules == null) {
			return null;
		}

		Map<Integer, List<BookingAirSchedule>> bookingAirScheduleMap = new HashMap<>();
		List<BookingAirSchedule> bookingAirSchedules = null;
		int seq = 1;

		for (BookingAirSchedule bookingAirSchedule : allBookingAirSchedules) {
			if (!bookingAirSchedule.isVia()) { //경유여부
				bookingAirSchedules = new ArrayList<>();
				bookingAirScheduleMap.put(seq++, bookingAirSchedules);
			}
			bookingAirSchedules.add(bookingAirSchedule);
		}

		for (Integer key : bookingAirScheduleMap.keySet()) {
			List<BookingAirSchedule> list = bookingAirScheduleMap.get(key);
			String totalTime = "";

			for (int i = 0; i < list.size(); i++) {
				BookingAirSchedule bookingAirSchedule = list.get(i);
				if (bookingAirSchedule.isVia() && !StringUtils.isNullOrEmpty(bookingAirSchedule.getGroundTime())) {
					totalTime = DateUtil.plusHHMM(totalTime, bookingAirSchedule.getGroundTime());
				}
				if (!StringUtils.isNullOrEmpty(bookingAirSchedule.getLeadTime())) {
					totalTime = DateUtil.plusHHMM(totalTime, bookingAirSchedule.getLeadTime());
				}
			}
			list.get(0).setTotalTime(totalTime);
		}
		return bookingAirScheduleMap;
	}

}