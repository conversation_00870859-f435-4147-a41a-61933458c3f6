package com.tidesquare.btms.entity;

import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "TRAVELMODIFYREQUEST")
@NoArgsConstructor
public class TravelModifyRequest {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "TRAVELMODIFYREQUESTID")
    private Long travelModifyRequestId;

    @Column(name = "PARENTTRAVELMODIFYREQUESTID")
    private Long parentTravelModifyRequestId;

    @Column(name = "TRAVELID")
    private Long travelId;

    @Column(name = "REQUESTINFO")
    private String requestInfo;

    @Column(name = "ANSWER")
    private String answer;

    @Column(name = "CREATOR_USERID")
    private Long creatorUserId;

    @Column(name = "CREATEDATE")
    @CreationTimestamp(source = SourceType.VM)
    private Date createDate;

    @Column(name = "MODIFIER_USERID")
    private Long modifierUserId;

    @Column(name = "MODIFYDATE")
    @UpdateTimestamp(source = SourceType.VM)
    private Date modifyDate;

    @Column(name = "ISNEW")
    private Boolean isNew = true;
}
