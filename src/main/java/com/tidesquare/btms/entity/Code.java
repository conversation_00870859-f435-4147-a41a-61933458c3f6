package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "Code")
@NoArgsConstructor
public class Code {
	public Code(Long id) {
		this.id = id;
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "codeId")
	private Long id;

	@Column(nullable = false, length = 100)
	private String groupCode;

	@Column(nullable = false, length = 100)
	private String code;

	@Column(nullable = false)
	private String name;

	@Column(length = 1000)
	private String description;

	@Column(nullable = false)
	private int displayOrder;

	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private Boolean isUse = true;
}