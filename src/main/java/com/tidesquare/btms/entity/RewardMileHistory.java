package com.tidesquare.btms.entity;

import java.util.Date;

import org.hibernate.Hibernate;
import org.hibernate.annotations.CreationTimestamp;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "RewardMileHistory")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RewardMileHistory {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "id")
  private Long id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "companyId")
  private Company company;

  public Company getCompany() {
    if (Hibernate.isInitialized(this.company)) {
      return this.company;
    }
    this.company = new Company(this.company.getId());
    return this.company;
  }

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "userId")
  private User user;

  public User getUser() {
    if (Hibernate.isInitialized(user)) {
      return this.user;
    }
    this.user = new User(this.user.getId());
    return this.user;
  }

  @Column
  @Min(0)
  private Integer totalMiles;

  @Column
  private Integer usedMiles;

  @Column
  private String reason;

  @Column
  @CreationTimestamp
  private Date usedDate;
}
