package com.tidesquare.btms.entity;

import com.tidesquare.btms.constant.RewardMileType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "RewardMileSetting")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RewardMileSetting {

  @Id
  @Column(name = "companyId")
  private Long companyId;

  @Enumerated(EnumType.STRING)
  @Column
  @Builder.Default
  private RewardMileType rewardMileType = RewardMileType.KE;

  @Column
  @Builder.Default
  private Float ratio = 0f;

  @Column
  @Builder.Default
  private Integer maximumAccumulatedMiles = null;

  @Column
  @Builder.Default
  private Boolean isUse = false;
}
