package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Inheritance;
import jakarta.persistence.InheritanceType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

import org.hibernate.Hibernate;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;
import org.hibernate.annotations.UpdateTimestamp;

import com.tidesquare.btms.constant.Gender;
import com.tidesquare.btms.constant.JoinAuthType;
import com.tidesquare.btms.constant.JoinStatus;
import com.tidesquare.btms.constant.Role;
import com.tidesquare.btms.constant.UserType;

@Getter
@Setter
@Entity
@Table(name = "BtmsUser")
@Inheritance(strategy = InheritanceType.JOINED)
@NoArgsConstructor
public class User {
	public User(Long id) {
		this.id = id;
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "userId")
	private Long id;

	@Enumerated(EnumType.STRING)
	@Column(name = "userType", length = 31)
	private UserType userType;

	@Column(name = "countryId")
	private Long countryId;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(nullable = false, name = "workspaceId")
	private Workspace workspace;

	public Workspace getWorkspace() {
		if (Hibernate.isInitialized(this.workspace)) {
			return this.workspace;
		}
		this.workspace = new Workspace(this.workspace.getId());
		return this.workspace;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "positionId")
	private Position position;

	public Position getPosition() {
		if (Hibernate.isInitialized(this.position)) {
			return this.position;
		}
		this.position = new Position(this.position.getId());
		return this.position;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "departmentId")
	private Department department;

	public Department getDepartment() {
		if (Hibernate.isInitialized(this.department)) {
			return this.department;
		}
		this.department = new Department(this.department.getId());
		return this.department;
	}

	@Column(nullable = false, length = 100)
	private String loginId;

	@Column(nullable = false, length = 100)
	private String password;

	@Column(nullable = false, length = 100)
	private String oldPassword;

	@Enumerated(EnumType.STRING)
	@Column(nullable = false, length = 30)
	private Role role;

	@Column(nullable = false, length = 100)
	private String name;

	@Enumerated(EnumType.STRING)
	@Column(length = 10)
	private Gender gender;

	@ColumnTransformer(read = "DOC_DECRYPT(birthday)", write = "DOC_ENCRYPT(?)")
	@Column(length = 40)
	private String birthday;

	@ColumnTransformer(read = "DOC_DECRYPT(cellPhoneNumber)", write = "DOC_ENCRYPT(?)")
	@Column(length = 100)
	private String cellPhoneNumber;

	@ColumnTransformer(read = "DOC_DECRYPT(phoneNumber)", write = "DOC_ENCRYPT(?)")
	@Column(length = 100)
	private String phoneNumber;

	@ColumnTransformer(read = "DOC_DECRYPT(employeeNo)", write = "DOC_ENCRYPT(?)")
	@Column(length = 128)
	private String employeeNo;

	@ColumnTransformer(read = "DOC_DECRYPT(email)", write = "DOC_ENCRYPT(?)")
	@Column(nullable = false, length = 250)
	private String email;

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "profileImageAttachFileId", nullable = true)
	private AttachFile profileImageAttachFile;

	@Column
	private Date joinDate;

	@Enumerated(EnumType.STRING)
	@Column(length = 10)
	private JoinAuthType joinAuthType;

	@Enumerated(EnumType.STRING)
	@Column(length = 10)
	private JoinStatus joinStatus;

	@Column(length = 20)
	private Date leaveDate;

	@Column
	private Long leaveProcessUserId;

	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private int incorrectPasswordCount;

	@Column
	private Date passwordUpdateDate;

	@Column
	private Date nextPasswordUpdateDate;

	@Column(length = 10)
	private String accountingCode;

	@Column(length = 10)
	private String newPasswordCode;

	@Column(length = 12)
	private String skypassMemberNo;

	@Column(length = 100)
	private String skypassMemberLastNm;

	@Column
	private Boolean skypassUse;

	@Column
	@CreationTimestamp(source = SourceType.VM)
	private Date createDate;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "creator_userId")
	private User creator;

	public User getCreator() {
		if (Hibernate.isInitialized(this.creator)) {
			return this.creator;
		}
		this.creator = new User(this.creator.getId());
		return this.creator;
	}

	@Column
	@UpdateTimestamp(source = SourceType.VM)
	private Date modifyDate;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "modifier_userId")
	private User modifier;

	public User getModifier() {
		if (Hibernate.isInitialized(this.modifier)) {
			return this.modifier;
		}
		this.modifier = new User(this.modifier.getId());
		return this.modifier;
	}
}