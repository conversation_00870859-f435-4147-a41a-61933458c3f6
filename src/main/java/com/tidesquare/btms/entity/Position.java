package com.tidesquare.btms.entity;

import java.util.Date;

import org.hibernate.Hibernate;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "Position")
@NoArgsConstructor
public class Position {
	public Position(Long id) {
		this.id = id;
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "positionId")
	private Long id;

	@Column
	private Long companyId;

	@Column(nullable = false, length = 100)
	private String name;

	@Column(nullable = false)
	private int displayOrder;

	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private Boolean isUse = true;

	@Column
	@CreationTimestamp(source = SourceType.VM)
	private Date createDate;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "creator_userId")
	private User creator;

	public User getCreator() {
		if (Hibernate.isInitialized(this.creator)) {
			return this.creator;
		}
		this.creator = new User(this.creator.getId());
		return this.creator;
	}

	@Column
	@UpdateTimestamp(source = SourceType.VM)
	private Date modifyDate;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "modifier_userId")
	private User modifier;

	public User getModifier() {
		if (Hibernate.isInitialized(this.modifier)) {
			return this.modifier;
		}
		this.modifier = new User(this.modifier.getId());
		return this.modifier;
	}
}