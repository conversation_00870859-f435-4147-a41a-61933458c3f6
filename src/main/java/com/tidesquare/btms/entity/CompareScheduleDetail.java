package com.tidesquare.btms.entity;

import org.hibernate.Hibernate;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.SortedSet;

@Getter
@Setter
@Entity
@Table(name = "CompareScheduleDetail")
public class CompareScheduleDetail implements Comparable<CompareScheduleDetail> {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "CompareScheduleDetailId")
	private Long id;

	// 여정SEQ
	@Column(length = 2)
	private Integer itinerarySeq;

	// 총시간
	@Column(length = 2)
	private String totalHour;

	// 총시간
	@Column(length = 2)
	private String totalMin;

	// 총 비행시간 시
	@Column(length = 2)
	private String flightHour;

	// 총 비행시간 분
	@Column(length = 2)
	private String flightMin;

	// 경유횟수
	@Column(length = 2)
	private Integer stopoverNo;

	@Column(length = 10)
	private String departureTerminal; // 출발터미널

	@Column(length = 10)
	private String arrivalTerminal; // 도착터미널

	@Column(columnDefinition = "BOOLEAN")
	private Boolean seatWaiting; // 좌석 확정/대기

	@OneToMany(fetch = FetchType.LAZY)
	@JoinColumn(name = "CompareScheduleDetailId")
	private SortedSet<CompareFlightDetail> flightDetails;

	public SortedSet<CompareFlightDetail> getFlightDetails() {
		if (Hibernate.isInitialized(this.flightDetails)) {
			return this.flightDetails;
		}
		this.flightDetails = null;
		return this.flightDetails;
	}

	@Column
	private Long compareAirScheduleId;

	@Override
	public int compareTo(CompareScheduleDetail o) {
		return this.getId().compareTo(o.getId());
	}
}
