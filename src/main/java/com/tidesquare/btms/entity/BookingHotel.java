package com.tidesquare.btms.entity;

import java.util.Date;
import java.util.SortedSet;

import org.hibernate.Hibernate;
import org.hibernate.annotations.CreationTimestamp;

import com.tidesquare.btms.constant.BookingHotelStatus;
import com.tidesquare.btms.entity.converter.BookingHotelStatusConverter;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(name = "BOOKING_HOTEL")
public class BookingHotel {

    public BookingHotel(Long id) {
        this.id = id;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "BOOKING_HOTEL_ID")
    private Long id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "COMPANY_ID")
    private Company company;

    public Company getCompany() {
        if (Hibernate.isInitialized(this.company)) {
            return this.company;
        }
        this.company = new Company(this.company.getId());
        return this.company;
    }

    @Column(name = "WORKSPACE_ID")
    private Long workspaceId;

    @Column(name = "STATUS_CODE_ID")
    @Convert(converter = BookingHotelStatusConverter.class)
    private BookingHotelStatus status;

    @Column(name = "BOOKING_DATE")
    private Date bookingDate;

    @Column(name = "HOTEL_INFLOW_PATH")
    private String hotelInflowPath;

    @Column(name = "VIOLATION_REASON")
    private String violationReason;

    @Column(name = "RESERVER_USER_ID")
    private Long reserverUserId;

    @Column(name = "RESERVER_NAME")
    private String reserverName;

    @Column(name = "RESERVER_PHONE_NUMBER")
    private String reserverPhoneNumber;

    @Column(name = "RESERVER_EMAIL")
    private String reserverEmail;

    @Column(name = "APPROVAL_MEMO")
    private String approvalMemo;

    @Column(name = "IS_APPROVAL")
    private Boolean isApproval;

    @Column(name = "APPROVAL_USER_ID")
    private Long approvalUserId;

    @Column(name = "APPROVAL_DATE")
    private Date approvalDate;

    @Column(name = "CREATE_DATE", updatable = false)
    @CreationTimestamp
    private Date createDate;

    @Column(name = "HOTEL_MASTER_ID")
    private String hotelMasterId;

    @Column(name = "HOTEL_NAME_KR")
    private String hotelNameKr;

    @Column(name = "HOTEL_NAME_EN")
    private String hotelNameEn;

    @Column(name = "BOOKING_ID")
    private Long bookingId;

    @Column(name = "CHECK_IN_YMD")
    private String checkInYmd;

    @Column(name = "CHECK_OUT_YMD")
    private String checkOutYmd;

    @Column(name = "ROOM_INFO")
    private String roomInfo;

    @Column(name = "SALE_PRICE")
    private Double salePrice;

    @Column(name = "BOOKING_CODE")
    private String bookingCode;

    @Column(name = "HOTEL_CATEGORY")
    private String hotelCategory;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "manager")
    private TravelAgencyUser manager;

    public TravelAgencyUser getManager() {
        if (Hibernate.isInitialized(this.manager)) {
            return this.manager;
        }
        this.manager = new TravelAgencyUser(this.manager.getId());
        return this.manager;
    }

    @Column(name = "NAT_NM")
    private String natNm;

    @Column(name = "END_CITY_NM")
    private String endCityNm;

    @Column(name = "ROOM_CNT")
    private Integer roomCnt;

    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "BOOKING_HOTEL_ID")
    private SortedSet<BookingHotelGuest> bookingHotelGuests;

    public SortedSet<BookingHotelGuest> getBookingHotelGuests() {
        if (Hibernate.isInitialized(this.bookingHotelGuests)) {
            return this.bookingHotelGuests;
        }
        this.bookingHotelGuests = null;
        return this.bookingHotelGuests;
    }

    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "BOOKING_ID")
    private SortedSet<DocumentNumber> documentNumbers;

    public SortedSet<DocumentNumber> getDocumentNumbers() {
        if (Hibernate.isInitialized(this.documentNumbers)) {
            return this.documentNumbers;
        }
        this.documentNumbers = null;
        return this.documentNumbers;
    }
}
