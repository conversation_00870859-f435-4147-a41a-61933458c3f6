package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import org.hibernate.Hibernate;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;

import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "CompanyAccount")
public class CompanyAccount {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CompanyAccountId")
    private Long id;

    @Column(name = "companyId")
    private Long companyId;

    @Column(length = 10)
    private String customType;

    @Column(length = 10)
    private String sBankCd;

    @Column(length = 100)
    private String sBankNm;

    @ColumnTransformer(read = "DOC_DECRYPT(sBankAccnt)", write = "DOC_ENCRYPT(?)")
    @Column(length = 100)
    private String sBankAccnt;

    @ColumnTransformer(read = "DOC_DECRYPT(sDepositors)", write = "DOC_ENCRYPT(?)")
    @Column(length = 100)
    private String sDepositors;

    @Column(length = 10)
    private String fBankCd;

    @Column(length = 100)
    private String fBankNm;

    @ColumnTransformer(read = "DOC_DECRYPT(fBankAccnt)", write = "DOC_ENCRYPT(?)")
    @Column(length = 100)
    private String fBankAccnt;

    @ColumnTransformer(read = "DOC_DECRYPT(fBankDepositors)", write = "DOC_ENCRYPT(?)")
    @Column(length = 100)
    private String fBankDepositors;

    @Column()
    @CreationTimestamp(source = SourceType.VM)
    private Date createDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "creator_userId")
    private TravelAgencyUser creator;

    public TravelAgencyUser getCreator() {
        if (Hibernate.isInitialized(this.creator)) {
            return this.creator;
        }
        this.creator = new TravelAgencyUser(this.creator.getId());
        return this.creator;
    }
}