package com.tidesquare.btms.entity;

import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;

import com.tidesquare.btms.constant.BookingType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "DOCUMENT_NUMBER")
public class DocumentNumber implements Comparable<DocumentNumber> {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", updatable = false, nullable = false)
    private Long id;

    @Column(name = "DOCUMENT_NO", length = 100, nullable = false)
    private String documentNo;

    @Column(name = "CREATOR_USERID")
    private Long creatorUserId;

    @Column(name = "CREATE_DATE", updatable = false)
    @CreationTimestamp(source = SourceType.VM)
    private Date createDate;

    @Column(name = "ORDER_NO")
    private Long orderNo;

    @Enumerated(EnumType.STRING)
    @Column(name = "BOOKING_TYPE", nullable = false, length = 10)
    private BookingType bookingType;

    @Column(name = "BOOKING_ID")
    private Long bookingId;

    @Override
    public int compareTo(DocumentNumber o) {
        return this.getOrderNo().compareTo(o.getOrderNo());
    }
}
