package com.tidesquare.btms.entity;

import org.hibernate.Hibernate;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "BOOKINGAIR_ATTACHFILE")
@NoArgsConstructor
@AllArgsConstructor
public class BookingAirAttachFile {
    @Id
    @Column(name = "BOOKINGAIRID")
    private Long bookingAirId;

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ATTACHFILEID")
    private AttachFile attachFile;

    public AttachFile getAttachFile() {
        if (Hibernate.isInitialized(this.attachFile)) {
            return this.attachFile;
        }

        this.attachFile = new AttachFile(this.attachFile.getId());
        return this.attachFile;
    }
}
