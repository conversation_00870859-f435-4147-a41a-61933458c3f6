package com.tidesquare.btms.entity;

import java.util.Date;

import org.hibernate.Hibernate;

import com.tidesquare.btms.constant.TravelBookingType;
import com.tidesquare.btms.constant.TravelStatus;
import com.tidesquare.btms.entity.converter.TravelStatusConverter;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "TRAVELSTATUSHISTORY")
public class TravelStatusHistory {

    @Id
    @Column
    private Long travelId;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 10)
    private TravelBookingType travelBookingType;

    @Column(name = "statusCodeId")
    @Convert(converter = TravelStatusConverter.class)
    private TravelStatus status;

    @Column(length = 200)
    private String modifyInfo;

    @Id
    @Column
    private Date modifyDate;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(nullable = true)
    private User modifier;

    public User getModifier() {
        if (Hibernate.isInitialized(this.modifier)) {
            return this.modifier;
        }
        this.modifier = new User(this.modifier.getId());
        return this.modifier;
    }
}