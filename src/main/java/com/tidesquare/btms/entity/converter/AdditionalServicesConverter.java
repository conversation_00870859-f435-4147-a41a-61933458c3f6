package com.tidesquare.btms.entity.converter;

import java.util.ArrayList;
import java.util.List;

import jakarta.persistence.AttributeConverter;

import com.tidesquare.btms.constant.AdditionalService;

public class AdditionalServicesConverter implements AttributeConverter<List<AdditionalService>, String> {

    @Override
    public String convertToDatabaseColumn(List<AdditionalService> entityValue) {
        if (entityValue == null) {
            return "";
        }
        if (entityValue.isEmpty()) {
            return "";
        }

        return entityValue.stream().map(AdditionalService::name).reduce((a, b) -> a + "," + b).get();
    }

    @Override
    public List<AdditionalService> convertToEntityAttribute(String dbValue) {
        if (dbValue == null || dbValue.isEmpty()) {
            return new ArrayList<AdditionalService>();
        }

        return new ArrayList<AdditionalService>() {
            {
                for (String value : dbValue.split(",")) {
                    add(AdditionalService.valueOf(value));
                }
            }
        };
    }

}
