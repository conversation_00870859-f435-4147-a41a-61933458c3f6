package com.tidesquare.btms.entity.converter;

import com.tidesquare.btms.constant.CardType;

import jakarta.persistence.AttributeConverter;

public class CardTypeConverter implements AttributeConverter<CardType, Long> {

    @Override
    public Long convertToDatabaseColumn(CardType entityValue) {
        if (entityValue == null) {
            return null;
        }
        return entityValue.getCodeId();
    }

    @Override
    public CardType convertToEntityAttribute(Long dbValue) {
        return CardType.fromCodeId(dbValue);
    }

}
