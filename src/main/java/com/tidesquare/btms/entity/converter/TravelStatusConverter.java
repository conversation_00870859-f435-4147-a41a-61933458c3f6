package com.tidesquare.btms.entity.converter;

import com.tidesquare.btms.constant.TravelStatus;

import jakarta.persistence.AttributeConverter;

public class TravelStatusConverter implements AttributeConverter<TravelStatus, Long> {

    @Override
    public Long convertToDatabaseColumn(TravelStatus entityValue) {
        if (entityValue == null) {
            return null;
        }
        return entityValue.getCodeId();
    }

    @Override
    public TravelStatus convertToEntityAttribute(Long dbValue) {
        return TravelStatus.fromCodeId(dbValue);
    }

}
