package com.tidesquare.btms.entity.converter;

import com.tidesquare.btms.constant.CardCompanyCode;

import jakarta.persistence.AttributeConverter;

public class CardCompanyCodeConverter implements AttributeConverter<CardCompanyCode, String> {

    @Override
    public String convertToDatabaseColumn(CardCompanyCode entityValue) {
        return CardCompanyCode.toCode(entityValue);
    }

    @Override
    public CardCompanyCode convertToEntityAttribute(String dbValue) {
        return CardCompanyCode.fromCode(dbValue);
    }

}
