package com.tidesquare.btms.entity.converter;

import com.tidesquare.btms.constant.BookingHotelStatus;

import jakarta.persistence.AttributeConverter;

public class BookingHotelStatusConverter implements AttributeConverter<BookingHotelStatus, Long> {

    @Override
    public Long convertToDatabaseColumn(BookingHotelStatus entityValue) {
        if (entityValue == null) {
            return null;
        }
        return entityValue.getCodeId();
    }

    @Override
    public BookingHotelStatus convertToEntityAttribute(Long dbValue) {
        return BookingHotelStatus.fromCodeId(dbValue);
    }

}
