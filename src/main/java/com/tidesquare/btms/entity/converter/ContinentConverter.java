package com.tidesquare.btms.entity.converter;

import com.tidesquare.btms.constant.Continent;

import jakarta.persistence.AttributeConverter;

public class ContinentConverter implements AttributeConverter<Continent, Long> {

    @Override
    public Long convertToDatabaseColumn(Continent entityValue) {
        if (entityValue == null) {
            return null;
        }
        return entityValue.getCodeId();
    }

    @Override
    public Continent convertToEntityAttribute(Long dbValue) {
        return Continent.fromCodeId(dbValue);
    }

}
