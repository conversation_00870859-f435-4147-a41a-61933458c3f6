package com.tidesquare.btms.entity.converter;

import com.tidesquare.btms.constant.AirComCuttingUnit;

import jakarta.persistence.AttributeConverter;

public class AirComCuttingUnitConverter implements AttributeConverter<AirComCuttingUnit, Long> {

    @Override
    public Long convertToDatabaseColumn(AirComCuttingUnit entityValue) {
        if (entityValue == null) {
            return null;
        }
        return entityValue.getCodeId();
    }

    @Override
    public AirComCuttingUnit convertToEntityAttribute(Long dbValue) {
        return AirComCuttingUnit.fromCodeId(dbValue);
    }

}
