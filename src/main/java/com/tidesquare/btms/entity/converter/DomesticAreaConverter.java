package com.tidesquare.btms.entity.converter;

import com.tidesquare.btms.constant.DomesticArea;

import jakarta.persistence.AttributeConverter;

public class DomesticAreaConverter implements AttributeConverter<DomesticArea, Long> {

    @Override
    public Long convertToDatabaseColumn(DomesticArea entityValue) {
        if (entityValue == null) {
            return null;
        }
        return entityValue.getCodeId();
    }

    @Override
    public DomesticArea convertToEntityAttribute(Long dbValue) {
        return DomesticArea.fromCodeId(dbValue);
    }

}
