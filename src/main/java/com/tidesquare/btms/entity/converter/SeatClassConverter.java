package com.tidesquare.btms.entity.converter;

import com.tidesquare.btms.constant.SeatClass;

import jakarta.persistence.AttributeConverter;

public class SeatClassConverter implements AttributeConverter<SeatClass, Long> {

    @Override
    public Long convertToDatabaseColumn(SeatClass entityValue) {
        if (entityValue == null) {
            return null;
        }
        return entityValue.getCodeId();
    }

    @Override
    public SeatClass convertToEntityAttribute(Long dbValue) {
        return SeatClass.fromCodeId(dbValue);
    }

}
