package com.tidesquare.btms.entity;

import org.hibernate.annotations.ColumnTransformer;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "Traveler")
public class Traveler implements Comparable<Traveler> {
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "id")
	private Long id;

	@Column(name = "travelId")
	private Long travelId;

	@Column(name = "travelerUserId")
	private Long travelerUserId;

	@Column(name = "email")
	@ColumnTransformer(read = "DOC_DECRYPT(email)", write = "DOC_ENCRYPT(?)")
	private String email;

	@Column(name = "name")
	private String name;

	@Column(name = "lastName")
	private String lastName; // if isReserver = true => lastName = null

	@Column(name = "firstName")
	private String firstName; // if isReserver = true => firstName = null

	@Column(name = "cellPhoneNumber")
	@ColumnTransformer(read = "DOC_DECRYPT(cellPhoneNumber)", write = "DOC_ENCRYPT(?)")
	private String cellPhoneNumber;

	@Column(name = "isReserver")
	private boolean isReserver;

	@Column(name = "accountingCode")
	private String accountingCode; // if travelerUserId is null => accountingCode = null

	@Override
	public int compareTo(Traveler o) {
		return this.getId().compareTo(o.getId());
	}
}