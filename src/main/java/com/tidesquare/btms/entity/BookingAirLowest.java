package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import org.hibernate.Hibernate;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;

import com.tidesquare.btms.constant.SectionType;

import java.util.Date;
import java.util.SortedSet;

@Getter
@Setter
@Entity
@Table(name = "BookingAirLowest")
@NoArgsConstructor
public class BookingAirLowest {
    public BookingAirLowest(Long id) {
        this.id = id;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "bookingAirLowestId")
    private Long id;

    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "bookingAirLowestId")
    private SortedSet<BookingAirLowestDetail> bookingAirLowestDetails;

    public SortedSet<BookingAirLowestDetail> getBookingAirLowestDetails() {
        if (Hibernate.isInitialized(this.bookingAirLowestDetails)) {
            return this.bookingAirLowestDetails;
        }

        this.bookingAirLowestDetails = null;
        return this.bookingAirLowestDetails;
    }

    @Column
    private Date lastTicketDate;

    //출발일 기준 발권일수 ex)출발일 기준 20일
    @Column(length = 3)
    private Integer lastTicketDay;

    // 1인 총 금액 (발권금액 + 발권수수료)
    @Column
    private Long totalAmount;

    // 1인 발권금액 (항공운임 + 세금)
    @Column
    private Long ticketAmount;

    // 1인 항공운임 (Q-Charge 포함)
    @Column
    private Long fareAmount;

    // 1인 세금 (유류할증료 포함)
    @Column
    private Long taxAmount;

    // 1인 발권수수료
    @Column
    private Long tasfAmount;

    @Column(columnDefinition = "BOOLEAN")
    private Boolean isCorporateFare;

    @Column(columnDefinition = "BOOLEAN")
    private Boolean isRuleViolation;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private SectionType sectionType;

    @Column(length = 2)
    private String representTravelIndex;

    @Column(length = 100)
    private String baggageAllowance;

    @CreationTimestamp(source = SourceType.VM)
    private Date createDate;
}
