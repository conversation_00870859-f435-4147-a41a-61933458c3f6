package com.tidesquare.btms.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import org.hibernate.Hibernate;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;
import org.hibernate.annotations.UpdateTimestamp;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "CustomerAirline")
@NoArgsConstructor
public class CustomerAirline implements Comparable<CustomerAirline> {
	public CustomerAirline(Long id) {
		this.id = id;
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "customerPassportId")
	private Long id;

	@Column
	private Long userId;

	@Column
	private Long airlineId;

	@ColumnTransformer(read = "DOC_DECRYPT(memberNo)", write = "DOC_ENCRYPT(?)")
	@Column(nullable = false, length = 100)
	private String memberNo;

	@Column
	@CreationTimestamp(source = SourceType.VM)
	private Date createDate;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "creator_userId")
	private User creator;

	public User getCreator() {
		if (Hibernate.isInitialized(this.creator)) {
			return this.creator;
		}
		this.creator = new User(this.creator.getId());
		return this.creator;
	}

	@Column
	@UpdateTimestamp(source = SourceType.VM)
	private Date modifyDate;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "modifier_userId")
	private User modifier;

	public User getModifier() {
		if (Hibernate.isInitialized(this.modifier)) {
			return this.modifier;
		}
		this.modifier = new User(this.modifier.getId());
		return this.modifier;
	}

	@Override
	public int compareTo(CustomerAirline o) {
		return this.getId().compareTo(o.getId());
	}
}