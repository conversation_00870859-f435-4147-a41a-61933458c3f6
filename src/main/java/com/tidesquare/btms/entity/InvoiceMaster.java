package com.tidesquare.btms.entity;

import com.tidesquare.btms.constant.InvoiceReservationType;
import com.tidesquare.btms.constant.InvoiceViewType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import com.tidesquare.btms.constant.InvoiceLanguageType;

import java.util.Date;

import org.hibernate.Hibernate;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;
import org.hibernate.annotations.UpdateTimestamp;

@Getter
@Setter
@Entity
@Table(name = "INVC_MASTER")
@NoArgsConstructor
public class InvoiceMaster {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "invoice_id")
    private Long invoiceId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "template_id")
    private InvoiceTemplate invoiceTemplate;

    public InvoiceTemplate getInvoiceTemplate() {
        if (Hibernate.isInitialized(this.invoiceTemplate)) {
            return this.invoiceTemplate;
        }
        this.invoiceTemplate = new InvoiceTemplate(this.invoiceTemplate.getInvoiceTemplateId());
        return this.invoiceTemplate;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "company_id")
    private Company company;

    public Company getCompany() {
        if (Hibernate.isInitialized(this.company)) {
            return this.company;
        }
        this.company = new Company(this.company.getId());
        return this.company;
    }

    @Column(name = "reservation_type")
    @Enumerated(EnumType.STRING)
    private InvoiceReservationType reservationType;

    @Column(name = "language_type")
    @Enumerated(EnumType.STRING)
    private InvoiceLanguageType languageType;

    @Column(name = "view_type")
    @Enumerated(EnumType.STRING)
    private InvoiceViewType viewType;

    @Column(name = "is_refund_bill")
    private boolean isRefundBill;

    @Column(name = "file_path")
    private String filePath;

    @Column(name = "create_date")
    @CreationTimestamp(source = SourceType.VM)
    private Date createDate;

    @Column(name = "creator_user_id")
    private Long creatorUserId;

    @Column(name = "modify_date")
    @UpdateTimestamp(source = SourceType.VM)
    private Date modifyDate;

    @Column(name = "modifier_user_id")
    private Long modifyUserId;

    @Column(name = "is_old_version")
    private Boolean isOldVersion;

    @Column(name = "is_deleted")
    private Boolean isDelete = false;

}
