package com.tidesquare.btms.entity;

import org.hibernate.Hibernate;

import com.tidesquare.btms.constant.ManagerType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "CompanyBtmsManager")
public class BtmsManager {
    @Id
    @Column
    private Long companyId;

    @Column(nullable = true)
    private int displayOrder;

    @Id
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private ManagerType managerType;

    @Id
    @Column(nullable = false, columnDefinition = "BOOLEAN")
    private Boolean isOverseas = true;

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(nullable = true, name = "travelAgencyUserId")
    private TravelAgencyUser travelAgencyUser;

    public TravelAgencyUser getTravelAgencyUser() {
        if (Hibernate.isInitialized(this.travelAgencyUser)) {
            return this.travelAgencyUser;
        }
        this.travelAgencyUser = new TravelAgencyUser(this.travelAgencyUser.getId());
        return this.travelAgencyUser;
    }
}