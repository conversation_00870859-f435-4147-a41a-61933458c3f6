package com.tidesquare.btms.entity;

import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;

import com.tidesquare.btms.constant.GDSType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "CompanyFare")
public class CompanyFare {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "companyFareId")
    private Long id;

    @Column
    private Long companyId;

    @Column
    private Long airlineId;

    @Column(length = 50)
    private String code;

    @Enumerated(EnumType.STRING)
    @Column(name = "gdsType", length = 20)
    private GDSType gdsType;

    @Column
    @CreationTimestamp(source = SourceType.VM)
    private Date createDate;
}