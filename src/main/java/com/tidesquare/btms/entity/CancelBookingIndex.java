package com.tidesquare.btms.entity;

import com.tidesquare.btms.constant.CancelBookingIndexType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "cancel_booking_index")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CancelBookingIndex {
  @Id
  @Enumerated(EnumType.STRING)
  @Column(name = "type")
  private CancelBookingIndexType type;

  @Column(name = "index_value")
  private Long indexValue;
}
