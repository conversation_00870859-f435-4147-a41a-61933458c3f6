package com.tidesquare.btms.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@Entity
@Table(name = "Stella_Search_Fare_Log")
public class StellaSearchFareLog {
    @Id
    @Column(name = "user_id")
    private Long userId;

    @Id
    @Column(name = "time")
    private Date time;

    @Column
    private String body;

    @Column
    private String response;
}