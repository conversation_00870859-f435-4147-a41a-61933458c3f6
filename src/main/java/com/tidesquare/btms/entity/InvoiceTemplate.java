package com.tidesquare.btms.entity;

import com.tidesquare.btms.constant.InvoicePublishType;
import com.tidesquare.btms.constant.InvoiceServiceType;
import com.tidesquare.btms.constant.InvoiceTemplateType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;
import org.hibernate.annotations.UpdateTimestamp;

@Getter
@Setter
@Entity
@Table(name = "INVC_TEMPLATE")
@NoArgsConstructor
public class InvoiceTemplate {
    public InvoiceTemplate(Long invoiceTemplateId) {
        this.invoiceTemplateId = invoiceTemplateId;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "template_id")
    private Long invoiceTemplateId;

    @Column(name = "invoice_service_type")
    @Enumerated(EnumType.STRING)
    private InvoiceServiceType invoiceServiceType;

    @Column(name = "invoice_template_type")
    @Enumerated(EnumType.STRING)
    private InvoiceTemplateType invoiceTemplateType;

    @Column(name = "name")
    private String name;

    @Column(name = "create_date")
    @CreationTimestamp(source = SourceType.VM)
    private Date createDate;

    @Column(name = "creator_user_id")
    private Long creatorUserId;

    @Column(name = "modify_date")
    @UpdateTimestamp(source = SourceType.VM)
    private Date modifyDate;

    @Column(name = "modifier_user_id")
    private Long modifyUserId;

    @Column(name = "invoice_publish_type")
    @Enumerated(EnumType.STRING)
    private InvoicePublishType invoicePublishType;
}
