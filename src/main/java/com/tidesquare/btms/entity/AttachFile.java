package com.tidesquare.btms.entity;

import com.tidesquare.btms.constant.AttachFileType;
import com.tidesquare.btms.utils.StringUtils;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;

@Getter
@Setter
@Entity
@Table(name = "AttachFile")
@NoArgsConstructor
public class AttachFile {
    public AttachFile(Long id) {
        this.id = id;
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "attachFileId")
    private Long id;

    @Enumerated(EnumType.STRING)
    @Column(length = 30)
    private AttachFileType attachFileType;

    @Column
    private String fileUploadPath;

    @Column
    private String originFileName;

    @Column
    private String tempFileName;

    @Column
    private Long fileSize;

    @Column(length = 2000)
    private String s3BucketPath;

    @Column
    @CreationTimestamp(source = SourceType.VM)
    private Date createDate;

    public String getFileUploadPath() {
        if (StringUtils.isNullOrEmpty(this.fileUploadPath)) {
            return this.fileUploadPath;
        }
        if (this.fileUploadPath.startsWith("/app/upload/")) {
            return this.fileUploadPath.substring(12);
        }
        return this.fileUploadPath;
    }
}