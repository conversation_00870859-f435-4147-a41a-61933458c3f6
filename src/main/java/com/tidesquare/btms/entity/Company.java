package com.tidesquare.btms.entity;

import com.tidesquare.btms.constant.AirComCuttingUnit;
import com.tidesquare.btms.constant.AirComMathType;
import com.tidesquare.btms.constant.CommissionCalculationType;
import com.tidesquare.btms.constant.CompanyType;
import com.tidesquare.btms.constant.Constants;
import com.tidesquare.btms.constant.DomesticSaleComRateType;
import com.tidesquare.btms.entity.converter.AirComCuttingUnitConverter;
import com.tidesquare.btms.entity.embeddable.Address;
import com.tidesquare.btms.entity.embeddable.AirEmSetting;
import com.tidesquare.btms.entity.embeddable.BtmsSetting;
import com.tidesquare.btms.entity.embeddable.CompanyBusinessInfo;
import com.tidesquare.btms.entity.embeddable.CompanyTravelInfo;

import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.PrimaryKeyJoinColumn;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import org.hibernate.Hibernate;
import org.hibernate.annotations.ColumnTransformer;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.SourceType;

import java.util.Date;
import java.util.SortedSet;

@Getter
@Setter
@Entity
@Table(name = "Company")
@NoArgsConstructor
public class Company {
	public Company(Long id) {
		this.id = id;
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "companyId")
	private Long id;

	@Enumerated(EnumType.STRING)
	@Column(name = "companyType", length = 10)
	private CompanyType companyType; // 거래처 유형

	@Column(columnDefinition = "BOOLEAN")
	private boolean isSamsungPG = false;

	@Column(length = 10, updatable = false)
	private String siteCode = ""; // 타이드스퀘어 거래처 코드

	@Column(length = 10)
	private String erpSiteCode = ""; // 은행입금 관련 거래처 코드

	@Column(length = 30, updatable = false)
	private String name;

	@ColumnTransformer(read = "DOC_DECRYPT(businessNum)", write = "DOC_ENCRYPT(?)")
	@Column(length = 100, updatable = false)
	private String businessNum = "";

	@Column(length = 100, updatable = false)
	private String representativeName = "";

	@ColumnTransformer(read = "DOC_DECRYPT(companyTelNo)", write = "DOC_ENCRYPT(?)")
	@Column(length = 100, updatable = false)
	private String companyTelNo = "";

	@ColumnTransformer(read = "DOC_DECRYPT(companyFaxNo)", write = "DOC_ENCRYPT(?)")
	@Column(length = 100, updatable = false)
	private String companyFaxNo = "";

	@Embedded
	private BtmsSetting btmsSetting = new BtmsSetting();

	@Embedded
	private AirEmSetting airEmSetting = new AirEmSetting();

	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean isExcludeAirUse = false;

	// 해외 이코노미 항공 수수료
	@Column(columnDefinition = "FLOAT")
	private double economyAirComRate = 0d;

	// 해외 비즈니스 항공 수수료
	@Column(columnDefinition = "FLOAT")
	private double businessAirComRate = 0d;

	// 국내 항공 수수료
	@Column(columnDefinition = "FLOAT")
	private double domesticAirComRate = 0d;

	// 국내 판매 수수료
	@Column(columnDefinition = "FLOAT")
	private double domesticSaleComRate = 0d;

	// S/FARE OR 판매총액
	@Enumerated(EnumType.STRING)
	@Column(length = 20)
	private DomesticSaleComRateType domesticSaleComRateType = DomesticSaleComRateType.SFARE;

	// 월말 결제 여부
	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean isMonthEndPayment = false;

	// 세금계산서 발행 VAT 설정 여부 (Default: false)
	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean isTaxInvoiceVat = false;

	// 항공 수수료 절삭 타입
	@Enumerated(EnumType.STRING)
	@Column(length = 20)
	private AirComMathType airComMathType = AirComMathType.ROUND_HALF_UP;

	// 항공 수수료 절삭 단위
	@Column(name = "comCuttingUnitCodeId")
	@Convert(converter = AirComCuttingUnitConverter.class)
	private AirComCuttingUnit comCuttingUnit = AirComCuttingUnit.NEAREST_1;

	// 항공 수수료 VAT 절삭 타입
	@Enumerated(EnumType.STRING)
	@Column(length = 20)
	private AirComMathType airComVatMathType = AirComMathType.ROUND_HALF_UP;

	// 항공 수수료 VAT 절삭 단위
	@Column(name = "comVatCuttingUnitCodeId")
	@Convert(converter = AirComCuttingUnitConverter.class)
	private AirComCuttingUnit comVatCuttingUnit = AirComCuttingUnit.NEAREST_1;

	@Enumerated(EnumType.STRING)
	@Column(length = 20)
	private CommissionCalculationType commissionCalculationType = CommissionCalculationType.NET;

	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean isDeleted = false;

	@Embedded
	private Address address = new Address();

	@OneToMany(fetch = FetchType.LAZY)
	@JoinColumn(name = "companyId")
	private SortedSet<CompanyContactPoint> companyContactPoints;

	public SortedSet<CompanyContactPoint> getCompanyContactPoints() {
		if (Hibernate.isInitialized(this.companyContactPoints)) {
			return this.companyContactPoints;
		}
		this.companyContactPoints = null;
		return this.companyContactPoints;
	}

	@OneToMany(fetch = FetchType.LAZY)
	@JoinColumn(name = "companyId")
	private SortedSet<CompanyMemo> companyMemos;

	public SortedSet<CompanyMemo> getCompanyMemos() {
		if (Hibernate.isInitialized(this.companyMemos)) {
			return this.companyMemos;
		}
		this.companyMemos = null;
		return this.companyMemos;
	}

	@Column()
	@CreationTimestamp(source = SourceType.VM)
	private Date createDate;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "creator_userId")
	private TravelAgencyUser creator;

	public TravelAgencyUser getCreator() {
		if (Hibernate.isInitialized(this.creator)) {
			return this.creator;
		}
		this.creator = new TravelAgencyUser(this.creator.getId());
		return this.creator;
	}

	@OneToMany(fetch = FetchType.LAZY)
	@JoinColumn(name = "companyId")
	private SortedSet<Workspace> workspaces;

	public SortedSet<Workspace> getWorkspaces() {
		if (Hibernate.isInitialized(this.workspaces)) {
			return this.workspaces;
		}
		this.workspaces = null;
		return this.workspaces;
	}

	@OneToMany(fetch = FetchType.LAZY)
	@JoinColumn(name = "companyId")
	private SortedSet<BtmsManager> btmsManagers;

	public SortedSet<BtmsManager> getBtmsManagers() {
		if (Hibernate.isInitialized(this.btmsManagers)) {
			return this.btmsManagers;
		}
		this.btmsManagers = null;
		return this.btmsManagers;
	}

	@Column(nullable = false, columnDefinition = "BOOLEAN")
	private boolean isAgency = false;

	@OneToMany(fetch = FetchType.LAZY)
	@JoinColumn(name = "companyId")
	private SortedSet<ExcludeAirline> excludeAirlines;

	public SortedSet<ExcludeAirline> getExcludeAirlines() {
		if (Hibernate.isInitialized(this.excludeAirlines)) {
			return this.excludeAirlines;
		}
		this.excludeAirlines = null;
		return this.excludeAirlines;
	}

	@Column(columnDefinition = "FLOAT")
	private double airYearlyVolume = 0d;

	@Column(columnDefinition = "FLOAT")
	private double hotelYearlyVolume = 0d;

	@Embedded
	private CompanyTravelInfo companyTravelInfo = new CompanyTravelInfo();

	@Embedded
	private CompanyBusinessInfo companyBusinessInfo = new CompanyBusinessInfo();

	@Column(name = "INVOICE_FORM_ID")
	private Long invoiceFormId;

	@Column(name = "INVOICE_UNIT_TEMPLATE_ID")
	private Long invoiceUnitTemplateId;

	@Column(name = "INVOICE_BUNDLE_TEMPLATE_ID")
	private Long invoiceBundleTemplateId;

	@Column(name = "REFUND_INVC_UNIT_TEMPLATE_ID")
	private Long refundInvoiceUnitTemplateId;

	@Column(name = "REFUND_INVC_BUNDLE_TEMPLATE_ID")
	private Long refundInvoiceBundleTemplateId;

	@Column(name = "HOTEL_INVC_UNIT_TEMPLATE_ID")
	private Long hotelInvoiceUnitTemplateId;

	@Column(name = "HOTEL_INVC_BUNDLE_TEMPLATE_ID")
	private Long hotelInvoiceBundleTemplateId;

	@Column(name = "IS_CUSTOM")
	private boolean isCustom = false;

	@Column(name = "IS_GROUP")
	private boolean isGroup = false;

	@Column(name = "GROUP_CODE")
	private String groupCode = "";

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PARENT_ID", nullable = true)
	private Company parent;

	public Company getParent() {
		if (Hibernate.isInitialized(this.parent)) {
			return this.parent;
		}
		this.parent = new Company(this.parent.getId());
		return this.parent;
	}

	@OneToOne(fetch = FetchType.LAZY, optional = true)
	@PrimaryKeyJoinColumn()
	private HomepageSetting homepageSetting;

	public HomepageSetting getHomepageSetting() {
		if (Hibernate.isInitialized(this.homepageSetting)) {
			return this.homepageSetting;
		}
		this.homepageSetting = new HomepageSetting(this.homepageSetting.getCompanyId());
		return this.homepageSetting;
	}

	public boolean isKakaoCompany() {
		return this.siteCode != null && this.siteCode.equals(Constants.KAKAO_SITE_CODE);
	}
}