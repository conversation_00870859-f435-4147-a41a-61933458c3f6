package com.tidesquare.btms.exception;

public enum ErrorCode {
    E400("Bad Request"),
    E400_EMAIL_EXISTED("Email already exists"), // Client đang check mã code này
    E400_FLIGHT_SETTING_LIMIT_REACHED("Flight setting limit reached"),
    E400_INVALID_JSON_FORMAT("Invalid JSON format"),
    E400_INVALID_OTP("Invalid OTP"), // Client đang check mã code này
    E400_OTP_EXPIRED("OTP expired"), // Client đang check mã code này
    E401("Unauthorized"),
    E403("Forbidden"),
    E403_TRAVEL_FORBIDDEN("You do not have permission to access this resource."),
    E403_NOT_PERMISSION_CANCEL_PNR("You do not have permission to cancel this PNR"),
    E404("Not Found"),
    E404_PNR_NOT_FOUND("PNR not found"),
    E404_TRAVEL_NOT_FOUND("Travel Not Found"),
    E404_TRAVEL_NOT_CANCEL_RESERVATION("You can only cancel a reservation if it is in progress."),
    E404_COMPANY_REWARD_MILE_SETTING_NOT_FOUND("Company not found"),
    E404_USER_NOT_FOUND("User not found"),
    E404_COMPANY_NOT_FOUND("Company not found"),
    E404_COMPANY_DOES_NOT_USE_REWARD_MILE("Applies only to mileage registered business partners. Please select the mileage type and save."),
    E404_COMPANY_DONE_NOT_USE_NORMAL_REWARD_TYPE("Company does not use normal reward type"),
    E409_CONFICT_CANCEL_PNR("PRN is canceled"),
    E409_STATUS_CANNOT_BE_CANCELLED("Status cannot be cancelled"),
    E409_TICKET_RESERVATION_CANNOT_CANCEL("Ticketed reservations cannot be cancelled."),
    E409_USER_NOT_IN_COMPANY("User not in this company"),
    E409_TOTALMILES_CANNOT_LESS_THAN_ZERO("Total miles cannot less than 0"),
    E500_CANNOT_CANCEL_PNR("You can not cancel PRN");

    private ErrorCode(String message) {
        this.message = message;
    }

    private String message;

    public String getMessage() {
        return this.message;
    }
}
