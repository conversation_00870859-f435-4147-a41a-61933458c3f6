package com.tidesquare.btms.exception;

import com.tidesquare.btms.common.ApiResponse;
import com.tidesquare.btms.service.i18n.I18nService;

import jakarta.inject.Inject;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.ext.ExceptionMapper;
import jakarta.ws.rs.ext.Provider;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Provider
public class ApiExceptionMapper implements ExceptionMapper<ApiException> {

    @Context
    private HttpHeaders headers;

    @Inject
    private I18nService i18nService;

    @Override
    public Response toResponse(ApiException exception) {
        log.warn("ApiExceptionMapper ", exception);
        ApiResponse<Object> response = new ApiResponse<>();
        String language = this.headers.getHeaderString("X-Btms-Lang");
        response.setMessage(this.i18nService.getMessage(exception.getErrorCode().name(), language));
        response.setErrorCode(exception.getErrorCode().name());
        return Response.status(exception.getHttpStatus()).entity(response).build();
    }
}
