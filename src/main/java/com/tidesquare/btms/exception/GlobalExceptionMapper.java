package com.tidesquare.btms.exception;

import com.tidesquare.btms.common.ApiResponse;

import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import jakarta.ws.rs.ext.ExceptionMapper;
import jakarta.ws.rs.ext.Provider;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Provider
public class GlobalExceptionMapper implements ExceptionMapper<Exception> {

    @Context
    private HttpHeaders headers;

    @Override
    public Response toResponse(Exception exception) {
        log.error("GlobalExceptionMapper toResponse", exception);
        ApiResponse<Object> response = new ApiResponse<>();
        response.setMessage(exception.getMessage());
        return Response.status(Status.INTERNAL_SERVER_ERROR).entity(response).build();
    }
}
