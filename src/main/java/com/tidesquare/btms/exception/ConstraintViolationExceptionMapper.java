package com.tidesquare.btms.exception;

import com.tidesquare.btms.common.ApiResponse;

import jakarta.validation.ConstraintViolationException;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.HttpHeaders;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import jakarta.ws.rs.ext.ExceptionMapper;
import jakarta.ws.rs.ext.Provider;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Provider
public class ConstraintViolationExceptionMapper implements ExceptionMapper<ConstraintViolationException> {

    @Context
    private HttpHeaders headers;

    @Override
    public Response toResponse(ConstraintViolationException exception) {
        log.warn("ConstraintViolationExceptionMapper ", exception);
        ApiResponse<Object> response = new ApiResponse<>();
        response.setMessage(exception.getMessage());
        response.setErrorCode(ErrorCode.E400.name());

        return Response.status(Status.BAD_REQUEST).entity(response).build();
    }
}
