package com.tidesquare.btms.constant;

import lombok.Getter;

public enum DomesticArea {
    SEL(12L), // 서울특별시
    PUS(13L), // 부산광역시
    DAE(14L), // 대구광역시
    CJU(15L), // 제주특별자치도
    INC(16L), // 인천광역시
    USN(17L), // 울산광역시
    QTW(18L), // 대전광역시
    GAW(19L), // 강원도
    GYE(20L), // 경기도
    GSN(21L), // 경상남도
    GSB(22L), // 경상북도
    KWJ(23L), // 광주광역시
    JLN(24L), // 전라남도
    JLB(25L), // 전라북도
    CCN(26L), // 충청남도
    CCB(27L); // 충청북도

    @Getter
    private Long codeId;

    DomesticArea(Long codeId) {
        this.codeId = codeId;
    }

    public static DomesticArea fromCodeId(Long codeId) {
        if (codeId == null) {
            return null;
        }
        if (codeId.equals(SEL.getCodeId())) {
            return SEL;
        }
        if (codeId.equals(PUS.getCodeId())) {
            return PUS;
        }
        if (codeId.equals(DAE.getCodeId())) {
            return DAE;
        }
        if (codeId.equals(CJU.getCodeId())) {
            return CJU;
        }
        if (codeId.equals(INC.getCodeId())) {
            return INC;
        }
        if (codeId.equals(USN.getCodeId())) {
            return USN;
        }
        if (codeId.equals(QTW.getCodeId())) {
            return QTW;
        }
        if (codeId.equals(GAW.getCodeId())) {
            return GAW;
        }
        if (codeId.equals(GYE.getCodeId())) {
            return GYE;
        }
        if (codeId.equals(GSN.getCodeId())) {
            return GSN;
        }
        if (codeId.equals(GSB.getCodeId())) {
            return GSB;
        }
        if (codeId.equals(KWJ.getCodeId())) {
            return KWJ;
        }
        if (codeId.equals(JLN.getCodeId())) {
            return JLN;
        }
        if (codeId.equals(JLB.getCodeId())) {
            return JLB;
        }
        if (codeId.equals(CCN.getCodeId())) {
            return CCN;
        }
        if (codeId.equals(CCB.getCodeId())) {
            return CCB;
        }

        return null;
    }
}
