package com.tidesquare.btms.constant;

import lombok.Getter;

public enum BookingHotelStatus {
    Ready(251L, "가예약"),
    Doing(497L, "품의"),
    Complete(498L, "예약"),
    Can<PERSON>(499L, "취소"),
    Error(503L, "대기"),

    ReservationRequested(247L, "예약요청"), // IsUse False
    ReservationInProgress(248L, "예약중"), // IsUse False
    ReservationCompleted(249L, "예약완료"), // IsUse False
    Voucher(250L, "바우처"); // IsUse False

    @Getter
    private Long codeId;

    @Getter
    private String text;

    BookingHotelStatus(Long codeId, String text) {
        this.codeId = codeId;
        this.text = text;
    }

    public static BookingHotelStatus fromCodeId(Long codeId) {
        if (codeId == null) {
            return null;
        }
        if (codeId.equals(Ready.getCodeId())) {
            return Ready;
        }
        if (codeId.equals(Doing.getCodeId())) {
            return Doing;
        }
        if (codeId.equals(Complete.getCodeId())) {
            return Complete;
        }
        if (codeId.equals(Cancel.getCodeId())) {
            return Cancel;
        }
        if (codeId.equals(Error.getCodeId())) {
            return Error;
        }

        if (codeId.equals(ReservationRequested.getCodeId())) {
            return ReservationRequested;
        }
        if (codeId.equals(ReservationInProgress.getCodeId())) {
            return ReservationInProgress;
        }
        if (codeId.equals(ReservationCompleted.getCodeId())) {
            return ReservationCompleted;
        }
        if (codeId.equals(Voucher.getCodeId())) {
            return Voucher;
        }

        return null;
    }
}
