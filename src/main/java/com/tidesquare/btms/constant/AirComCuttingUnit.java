package com.tidesquare.btms.constant;

import lombok.Getter;

public enum AirComCuttingUnit {
    NEAREST_1(407L, 0),
    NEAREST_10(399L, -1),
    NEAREST_100(400L, -2),
    NEAREST_1000(401L, -3),
    NEAREST_10000(402L, -4),
    NEAREST_100000(403L, -5);

    @Getter
    private Long codeId;

    @Getter
    private int scale;

    AirComCuttingUnit(Long codeId, int scale) {
        this.codeId = codeId;
        this.scale = scale;
    }

    public static AirComCuttingUnit fromCodeId(Long codeId) {
        if (codeId == null) {
            return null;
        }
        if (codeId.equals(NEAREST_1.getCodeId())) {
            return NEAREST_1;
        }
        if (codeId.equals(NEAREST_10.getCodeId())) {
            return NEAREST_10;
        }
        if (codeId.equals(NEAREST_100.getCodeId())) {
            return NEAREST_100;
        }
        if (codeId.equals(NEAREST_1000.getCodeId())) {
            return NEAREST_1000;
        }
        if (codeId.equals(NEAREST_10000.getCodeId())) {
            return NEAREST_10000;
        }
        if (codeId.equals(NEAREST_100000.getCodeId())) {
            return NEAREST_100000;
        }

        return null;
    }
}