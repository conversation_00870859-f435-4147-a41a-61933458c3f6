package com.tidesquare.btms.constant;

public class Constants {

	// empty string
	public static final String EMPTY_STRING = "";

	// file upload
	// public static final String UPLOAD_SERVER_DIRECTORY = "/app/btms/upload";

	public static final String DEFAULT_YES = "Y";
	// DATE
	public static final String DATE_FORMAT_ONLY_TIME = "HH:mm";
	public static final String DATE_FORMAT = "yyyy-MM-dd";
	public static final String DATE_TIME_FORMAT_HHMM = "yyyy-MM-dd HH:mm";
	public static final String DATE_NODASH_FORMAT = "yyyyMMdd";
	public static final String DATE_ALL_NODASH_FORMAT = "yyyyMMddHHmmss";
	public static final String DATE_NODASH_FULL_FORMAT = "yyyyMMdd HH:mm:ss";
	public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
	public static final String DATE_NODASH_FORMAT_DDMMMYYYY = "dd MMM yyyy";

	// Elasticsearch
	public static final String DECIMAL_POINT_ONE_FORMAT = "%.1f";
	public static final String TEXT_LIKE_FORMAT = "*%s*";
	public static final String INDEX_NAME = "btms";
	public static final String TYPE_AIRLINE = "airline";
	public static final String TYPE_AIRPORT = "airport";
	public static final String TYPE_CITY = "city";
	public static final String TYPE_COUNTRY = "country";

	/*
	 * public static final String DAY_OF_THE_WEEK_IS_MONDAY = "월"; public static
	 * final String DAY_OF_THE_WEEK_IS_TUESDAY = "화"; public static final String
	 * DAY_OF_THE_WEEK_IS_WEDNESDAY = "수"; public static final String
	 * DAY_OF_THE_WEEK_IS_THURSDAY = "목"; public static final String
	 * DAY_OF_THE_WEEK_IS_FRIDAY = "금"; public static final String
	 * DAY_OF_THE_WEEK_IS_SATURDAY = "토"; public static final String
	 * DAY_OF_THE_WEEK_IS_SUNDAY = "일";
	 */

	// Room Type
	public static final String ROOM_TYPE_IS_SINGLE = "SINGLE";
	public static final String ROOM_TYPE_IS_TWIN = "TWIN";
	public static final String ROOM_TYPE_IS_DOUBLE = "DOUBLE";
	public static final String ROOM_TYPE_IS_TRIPLE = "TRIPLE";

	// Room Type Name
	public static final String ROOM_TYPE_IS_SINGLE_NAME = "1인실";
	public static final String ROOM_TYPE_IS_TWIN_NAME = "2인실(트윈요청)";
	public static final String ROOM_TYPE_IS_DOUBLE_NAME = "2인실(더블요청)";
	public static final String ROOM_TYPE_IS_TRIPLE_NAME = "3인실";

	// Country ID
	public static final Long COUNTRY_ID_IS_KOREAN = 23L;

	// Code ID
	// 항공 예약상태 코드
	/*
	 * public static final Long STATUS_CODE_AIR_IS_BOOKING_DOING = 392L; // 예약품의중
	 * public static final Long STATUS_CODE_AIR_IS_BOOKING_ONGOING = 393L; // 예약진행
	 * public static final Long STATUS_CODE_AIR_IS_BOOKING_COMPLETE = 394L; // 예약완료
	 * public static final Long STATUS_CODE_AIR_IS_TICKET_CHECKING = 395L; // 발권확인중
	 * public static final Long STATUS_CODE_AIR_IS_TICKET_COMPLETE = 396L; // 발권완료
	 * public static final Long STATUS_CODE_AIR_IS_BOOKING_CANCEL = 397L; // 예약취소
	 */

	// 호텔 예약상태 코드
	public static final Long STATUS_CODE_HOTEL_IS_BOOKING_READY = 251L; // 가예약
	public static final Long STATUS_CODE_HOTEL_IS_BOOKING_DOING = 497L; // 품의
	public static final Long STATUS_CODE_HOTEL_IS_BOOKING_COMPLETE = 498L; // 예약
	public static final Long STATUS_CODE_HOTEL_IS_BOOKING_CANCEL = 499L; // 취소
	public static final Long STATUS_CODE_HOTEL_IS_BOOKING_ERROR = 503L; // 대기

	// Currency Type
	public static final String CURRENCY_TYPE_IS_KRW = "KRW";
	public static final String CURRENCY_TYPE_IS_USD = "USD";

	// Email/SMS 수신 자동 발송
	public static final Boolean AUTO_SEND_EMAIL_SMS = false;

	// 국가코드
	public static final String COUNTRY_CODE_IS_KOREA = "KOR";

	// Triple DES Key value
	public static final byte[] DES_KEY_DATA = { 0x00, 0x02, 0x02, 0x01, 0x02, 0x06, 0x1f, 0x0e };

	public static final String TRAVELER_ADD = "ADD";
	public static final String TRAVELER_MODIFY = "MODIFY";
	public static final String TRAVELER_REMOVE = "REMOVE";
	public static final String USER_TYPE = "travelAgencyUser";

	// TEST
	// public static final String ERP_DEPOSIT_SITE_ID = "b2b"; //ERP API연동 ID
	// public static final String ERP_DEPOSIT_SITE_PASSWORD = "tideb2b0!"; //ERP
	// API연동 Password
	public static final String ERP_DEPOSIT_SITE_ID = "tourvis_biz"; // ERP API연동 ID
	public static final String ERP_DEPOSIT_SITE_PASSWORD = "tidebtms0!"; // ERP API연동 Password
	public static final String ERP_DEPOSIT_DIV_CODE_CORPORATION = "100";// ERP API연동 사업장번호 - 법인
	public static final String ERP_DEPOSIT_DIV_CODE_COMMERCIAL = "130"; // ERP API연동 사업장번호 - 상용
	public static final String ERP_DEPOSIT_SERVICE_TYPE = "B2B"; // ERP API연동 서비스타입
	public static final String ERP_DEPOSIT_MASTER_PK_PREFIX = "BTMSM_"; // ERP API연동 참조관리번호 PREFIX
	public static final String ERP_DEPOSIT_DETAIL_PK_PREFIX = "BTMSD_"; // ERP API연동 참조관리순번 PREFIX

	// 거래처코드 없는 거래처 CompanyId
	public static final Long COMPANY_ID_WITHOUT_SITE_CODE = 999L;
	public static final Long WORKSPACE_ID_WITHOUT_SITE_CODE = 768977L;

	public static final double DOMESTIC_TICKET_COMMISSION = 1000;

	// 현대카드 회사 정보
	public static final Long COMPANY_IS_HYUNDAI_CARD = 1001L;
	public static final Long COMPANY_IS_HYUNDAI_CAPITAL = 1002L;
	public static final Long COMPANY_IS_HYUNDAI_COMMERCIAL = 1003L;

	// 현대카드 회사 카드정보
	public static final String CARD_INFO_HYUNDAI_CARD = "24D9D33EF642FDAB7F13A6397619311B";
	public static final String CARD_INFO_HYUNDAI_CAPITAL = "C6C11A867F342F666CC0953883E72D32";
	public static final String CARD_INFO_HYUNDAI_COMMERCIAL = "BEF58B89AB4CCA3BB4BF64F65A32FC87";

	// 이니시스 그룹아이디
	public static final String INICIS_GROUP_ID_IS_CORPORATION_GDS = "TASF732665"; // 법인 GDS GID
	public static final String INICIS_PASSWORD_IS_CORPORATION_GDS = "1111"; // 법인 GDS Password

	public static final String INICIS_MERCHANT_ID_IS_CORPORATION_SABRE = "TASF732665"; // 법인 세이버 MID
	public static final String INICIS_MERCHANT_ID_IS_CORPORATION_AMADEUS = "TZ9A000001";// 법인 아마데우스 MID

	public static final String INICIS_GROUP_ID_IS_CORPORATION_ADMIN = "TIdesq17GI"; // 법인 ADMIN GID
	public static final String INICIS_PASSWORD_IS_CORPORATION_ADMIN = "1111"; // 법인 ADMIN Password

	public static final String INICIS_MERCHANT_ID_IS_CORPORATION_GROUP = "tidebiz001"; // 법인단체 MID
	public static final String INICIS_MERCHANT_ID_IS_CORPORATION_AIR = "tidebiz002"; // 법인항공 MID
	public static final String INICIS_MERCHANT_ID_IS_CORPORATION_HOTEL = "tidebiz003"; // 법인호텔 MID
	public static final String INICIS_MERCHANT_ID_IS_CORPORATION_VISA = "tidebiz004"; // 법인비자 MID
	public static final String INICIS_MERCHANT_ID_IS_CORPORATION_SAMSUNG = "tidebiz005";// 법인삼성 MID

	public static final String INICIS_GROUP_ID_IS_COMMERCIAL_GDS = "TASF173105"; // 상용 GDS GID
	public static final String INICIS_PASSWORD_IS_COMMERCIAL_GDS = "1111"; // 상용 GDS Password

	public static final String INICIS_MERCHANT_ID_IS_COMMERCIAL_SABRE = "731052LD39"; // 상용 세이버 MID
	public static final String INICIS_MERCHANT_ID_IS_COMMERCIAL_AMADEUS = "TSE11131GA"; // 상용 아마데우스 MID

	public static final String INICIS_GROUP_ID_IS_COMMERCIAL_ADMIN = "Gtstourvis"; // 상용 ADMIN GID
	public static final String INICIS_PASSWORD_IS_COMMERCIAL_ADMIN = "1111"; // 상용 ADMIN Password

	public static final String INICIS_MERCHANT_ID_IS_COMMERCIAL_AIR = "tstourvis2"; // 상용 항공
	public static final String INICIS_MERCHANT_ID_IS_COMMERCIAL_GROUP = "tstourvis4"; // 상용 단체
	public static final String INICIS_MERCHANT_ID_IS_COMMERCIAL_VISA = "tstourvis5"; // 상용 비자/보험
	public static final String INICIS_MERCHANT_ID_IS_COMMERCIAL_DIRECT = "tstourvis7"; // 상용 직가맹

	// 커스텀된 거래처 코드 관리
	public static String KAKAO_SITE_CODE = "D76";

	// dsp manager user id
	// 운영
	public static long DSR_MANAGER_USER_ID_PROD = 938641L;
	// 개발 이평화매니저 : 905377L
	public static long DSR_MANAGER_USER_ID_DEV = 905377L;
	// 로컬 이평화매니저 : 792100L
	// public static long DSR_MANAGER_USER_ID_LOCAL = 792100L;
	public static long DSR_MANAGER_USER_ID_LOCAL = 39L; // 테스트용 agency 계정

	// endregion

	// 인보이스 파일 다운로드 주소
	public static String HOTEL_INVOICE_FILE_DOWNLOAD_URL = "/hotel/invoice/email/file/download";

	// PCC/OID
	public static String AMADEUS_ONLINE_OID = "SELK138AR";
	public static String SABRE_ONLINE_PCC = "K8V8";
	public static String AMADEUS_OFFLINE_OID = "SELK131GA";
	public static String SABRE_OFFLINE_PCC = "LD38";
	public static String KE_ONLINE_OID = "SELK138AR";
	public static String OZ_ONLINE_PCC = "SELOZ34C2";
	public static String KE_OFFLINE_OID = "SELK131GA";
	public static String OZ_OFFLINE_PCC = "SELOZ312M";

	// Agency
	public static String AGENCY_COMPANY_SITE_CODE = "A67";
	public static Long AGENCY_COMPANY_ID = 37L;
	public static Long AGENCY_WORKSPACE_ID = 764586L;
}