package com.tidesquare.btms.constant;

import lombok.Getter;

public enum GubunType {
    DEPOSIT("입금"), REFUND("환불"), MULTI_DEPOSIT("입금"), MULTI_REFUND("환불"), GROUP_DEPOSIT("입금"), GROUP_REFUND("환불"), VISA_DEPOSIT("입금"), VISA_REFUND("환불"), HOTEL_DEPOSIT("입금"), HOTEL_REFUND("환불"), DOMESTIC_DEPOSIT("입금"),
    DOMESTIC_REFUND("환불"), INSURANCE_DEPOSIT("입금"), INSURANCE_REFUND("환불"), ETC_SALES_DEPOSIT("입금"), ETC_SALES_REFUND("환불");

    @Getter
    private String text;

    GubunType(String text) {
        this.text = text;
    }
}
