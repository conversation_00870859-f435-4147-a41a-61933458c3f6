package com.tidesquare.btms.constant;

import java.math.RoundingMode;
import lombok.Getter;

public enum AirComMathType {
    ROUND_CEILING(RoundingMode.CEILING), // 올림
    ROUND_FLOOR(RoundingMode.FLOOR), // 내림
    ROUND_HALF_UP(RoundingMode.UP); // 반올림

    @Getter
    private RoundingMode roundingMode;

    AirComMathType(RoundingMode roundingMode) {
        this.roundingMode = roundingMode;
    }
}