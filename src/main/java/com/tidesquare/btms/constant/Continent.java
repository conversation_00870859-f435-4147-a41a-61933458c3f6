package com.tidesquare.btms.constant;

import lombok.Getter;

public enum Continent {
    AS(1L),
    EU(2L),
    AF(3L),
    OC(4L),
    NA(5L),
    SA(6L),
    AN(7L); // IsUse False

    @Getter
    private Long codeId;

    Continent(Long codeId) {
        this.codeId = codeId;
    }

    public static Continent fromCodeId(Long codeId) {
        if (codeId == null) {
            return null;
        }
        if (codeId.equals(AS.getCodeId())) {
            return AS;
        }
        if (codeId.equals(EU.getCodeId())) {
            return EU;
        }
        if (codeId.equals(AF.getCodeId())) {
            return AF;
        }
        if (codeId.equals(OC.getCodeId())) {
            return OC;
        }
        if (codeId.equals(NA.getCodeId())) {
            return NA;
        }
        if (codeId.equals(SA.getCodeId())) {
            return SA;
        }
        if (codeId.equals(AN.getCodeId())) {
            return AN;
        }

        return null;
    }
}