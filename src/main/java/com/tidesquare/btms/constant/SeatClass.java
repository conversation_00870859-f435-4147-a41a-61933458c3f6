package com.tidesquare.btms.constant;

import lombok.Getter;

public enum SeatClass {
    M(315L, "일반석"), // 일반석
    W(316L, "프리미엄이코노미석"), // 프리미엄이코노미석
    C(317L, "비즈니스석"), // 비즈니스석
    F(318L, "일등석"), // 일등석
    Y(319L, "이코노미"); // 이코노미 IsUse False

    @Getter
    private Long codeId;

    @Getter
    private String text;

    SeatClass(Long codeId, String text) {
        this.codeId = codeId;
        this.text = text;
    }

    public static SeatClass fromCodeId(Long codeId) {
        if (codeId == null) {
            return null;
        }
        if (codeId.equals(M.getCodeId())) {
            return M;
        }
        if (codeId.equals(W.getCodeId())) {
            return W;
        }
        if (codeId.equals(C.getCodeId())) {
            return C;
        }
        if (codeId.equals(F.getCodeId())) {
            return F;
        }
        if (codeId.equals(Y.getCodeId())) {
            return Y;
        }

        return null;
    }
}