package com.tidesquare.btms.constant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

import lombok.Getter;

public enum CardCompanyCode {

    CODE_01("하나외환"), CODE_02("우리"), CODE_03("롯데"), CODE_04("현대"), CODE_06("국민"), CODE_11("BC"), CODE_12("삼성"), CODE_14("신한"), CODE_15("한미"), CODE_16("NH"), CODE_17("하나SK"), CODE_21("해외VISA"), CODE_22("해외마스터"), CODE_23("해외JCB"),
    CODE_24("해외AMEX"), CODE_26("중국은련"), CODE_32("광주"), CODE_33("전북"), CODE_34("하나"), CODE_35("산업카드"), CODE_41("NH"), CODE_43("씨티"), CODE_44("우리"), CODE_48("신협체크"), CODE_51("수협"), CODE_52("제주"), CODE_54("MG새마을금고체크"), CODE_55("케이뱅크"),
    CODE_56("카카오뱅크"), CODE_71("우체국체크"), CODE_95("저축은행체크");

    @Getter
    private String text;

    CardCompanyCode(String text) {
        this.text = text;
    }

    @JsonCreator
    public static CardCompanyCode fromCode(String code) {
        if (code == null) {
            return null;
        }
        return CardCompanyCode.valueOf("CODE_" + code);
    }

    public static String toCode(CardCompanyCode code) {
        if (code == null) {
            return null;
        }
        return code.toCode();
    }

    @JsonValue
    public String toCode() {
        return this.name().replace("CODE_", "");
    }
}