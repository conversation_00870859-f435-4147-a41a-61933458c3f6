package com.tidesquare.btms.constant;

import lombok.Getter;

public enum TravelStatus {
    Requested(392L, "예약요청중 입니다.", "예약"),
    Rejected(393L, "반려 되었습니다.", "반려"),
    Approved(394L, "승인 되었습니다.", "승인"),
    TicketingConfirmed(395L, "", "발권확인중"), // IsUse False
    Completed(396L, "완료 처리되었습니다.", "완료"),
    Cancelled(397L, "취소 처리 되었습니다.", "취소"),

    ReservationRequested(209L, "", "예약요청"), // IsUse False
    ReservationInProgress(210L, "", "예약중"), // IsUse False
    ReservationCompleted(211L, "", "예약완료"), // IsUse False
    TicketingRequested(212L, "", "발권요청"), // IsUse False
    TicketingCompleted(213L, "", "발권완료"), // IsUse False
    ReservationCancelled(214L, "", "취소"), // IsUse False
    TicketingInProgress(348L, "", "발권중"), // IsUse False
    ReservationCancelling(349L, "", "취소중"); // IsUse False

    @Getter
    private Long codeId;

    @Getter
    private String modifyInfo;

    @Getter
    private String text;

    TravelStatus(Long codeId, String modifyInfo, String text) {
        this.codeId = codeId;
        this.modifyInfo = modifyInfo;
        this.text = text;
    }

    public static TravelStatus fromCodeId(Long codeId) {
        if (codeId == null) {
            return null;
        }
        if (codeId.equals(Requested.getCodeId())) {
            return Requested;
        }
        if (codeId.equals(Rejected.getCodeId())) {
            return Rejected;
        }
        if (codeId.equals(Approved.getCodeId())) {
            return Approved;
        }
        if (codeId.equals(TicketingConfirmed.getCodeId())) {
            return TicketingConfirmed;
        }
        if (codeId.equals(Completed.getCodeId())) {
            return Completed;
        }
        if (codeId.equals(Cancelled.getCodeId())) {
            return Cancelled;
        }

        if (codeId.equals(ReservationRequested.getCodeId())) {
            return ReservationRequested;
        }
        if (codeId.equals(ReservationInProgress.getCodeId())) {
            return ReservationInProgress;
        }
        if (codeId.equals(ReservationCompleted.getCodeId())) {
            return ReservationCompleted;
        }
        if (codeId.equals(TicketingRequested.getCodeId())) {
            return TicketingRequested;
        }
        if (codeId.equals(TicketingCompleted.getCodeId())) {
            return TicketingCompleted;
        }
        if (codeId.equals(ReservationCancelled.getCodeId())) {
            return ReservationCancelled;
        }
        if (codeId.equals(ReservationCancelled.getCodeId())) {
            return ReservationCancelled;
        }
        if (codeId.equals(TicketingInProgress.getCodeId())) {
            return TicketingInProgress;
        }
        if (codeId.equals(ReservationCancelling.getCodeId())) {
            return ReservationCancelling;
        }

        return null;
    }
}
