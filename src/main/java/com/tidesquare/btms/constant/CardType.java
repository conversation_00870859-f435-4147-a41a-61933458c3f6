package com.tidesquare.btms.constant;

import lombok.Getter;

public enum CardType {
    CARD_0015(246L), // 문화누리카드
    CARD_0014(245L), // 산업은행카드
    CARD_0013(244L), // 수협카드
    CARD_0012(243L), // 제주은행
    CARD_0011(242L), // 광주은행
    CARD_0010(241L), // 전북JB카드
    CARD_0009(240L), // 하나SK카드
    CARD_0008(239L), // 외환카드
    CARD_0007(238L), // 현대카드
    CARD_0006(237L), // NH카드
    CARD_0005(236L), // BC카드
    CARD_0004(235L), // 씨티카드
    CARD_0003(234L), // 삼성카드
    CARD_0002(233L), // 국민카드
    CARD_0001(232L), // 신한카드
    CARD_0000(231L), // 롯데카드
    JC(76L), // Japan Credit Bureau
    CB(75L), // Carte Blue
    DS(74L), // Discover
    DC(73L), // Diners Club
    AX(72L), // American Express
    CA(71L), // MasterCard
    VI(70L); // VISA

    @Getter
    private Long codeId;

    CardType(Long codeId) {
        this.codeId = codeId;
    }

    public static CardType fromCodeId(Long codeId) {
        if (codeId == null) {
            return null;
        }
        if (codeId.equals(JC.getCodeId())) {
            return JC;
        }
        if (codeId.equals(CB.getCodeId())) {
            return CB;
        }
        if (codeId.equals(DS.getCodeId())) {
            return DS;
        }
        if (codeId.equals(DC.getCodeId())) {
            return DC;
        }
        if (codeId.equals(AX.getCodeId())) {
            return AX;
        }
        if (codeId.equals(CA.getCodeId())) {
            return CA;
        }
        if (codeId.equals(VI.getCodeId())) {
            return VI;
        }
        if (codeId.equals(CARD_0000.getCodeId())) {
            return CARD_0000;
        }
        if (codeId.equals(CARD_0001.getCodeId())) {
            return CARD_0001;
        }
        if (codeId.equals(CARD_0002.getCodeId())) {
            return CARD_0002;
        }
        if (codeId.equals(CARD_0003.getCodeId())) {
            return CARD_0003;
        }
        if (codeId.equals(CARD_0004.getCodeId())) {
            return CARD_0004;
        }
        if (codeId.equals(CARD_0005.getCodeId())) {
            return CARD_0005;
        }
        if (codeId.equals(CARD_0006.getCodeId())) {
            return CARD_0006;
        }
        if (codeId.equals(CARD_0007.getCodeId())) {
            return CARD_0007;
        }
        if (codeId.equals(CARD_0008.getCodeId())) {
            return CARD_0008;
        }
        if (codeId.equals(CARD_0009.getCodeId())) {
            return CARD_0009;
        }
        if (codeId.equals(CARD_0010.getCodeId())) {
            return CARD_0010;
        }
        if (codeId.equals(CARD_0011.getCodeId())) {
            return CARD_0011;
        }
        if (codeId.equals(CARD_0012.getCodeId())) {
            return CARD_0012;
        }
        if (codeId.equals(CARD_0013.getCodeId())) {
            return CARD_0013;
        }
        if (codeId.equals(CARD_0014.getCodeId())) {
            return CARD_0014;
        }
        if (codeId.equals(CARD_0015.getCodeId())) {
            return CARD_0015;
        }

        return null;
    }
}
