package com.tidesquare.btms.common;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ApiResponse<T> {
	private T data;
	private String message;
	@JsonProperty(value = "error_code")
	private String errorCode;

	public static <U> ApiResponse<U> fromData(U data) {
		ApiResponse<U> response = new ApiResponse<U>();
		response.setData(data);
		return response;
	}
}
