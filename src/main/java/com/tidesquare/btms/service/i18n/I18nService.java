package com.tidesquare.btms.service.i18n;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.MissingResourceException;
import java.util.ResourceBundle;

import jakarta.enterprise.context.ApplicationScoped;

@ApplicationScoped
public class I18nService {

    private Map<String, ResourceBundle> bundles;

    public I18nService() {
        this.bundles = new HashMap<>();
        this.bundles.put("kr", ResourceBundle.getBundle("messages", Locale.of("kr")));
        this.bundles.put("en", ResourceBundle.getBundle("messages", Locale.of("en")));
    }

    public String getMessage(String key, String language, Object... args) {
        ResourceBundle bundle = this.bundles.get(language);
        if (bundle == null) {
            bundle = this.bundles.get("en");
        }

        try {
            String message = bundle.getString(key);
            return MessageFormat.format(message, args);
        } catch (MissingResourceException e) {
            return key;
        }
    }
}