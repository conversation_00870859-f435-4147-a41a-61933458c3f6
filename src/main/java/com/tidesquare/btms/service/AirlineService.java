package com.tidesquare.btms.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tidesquare.btms.entity.Airline;
import com.tidesquare.btms.repository.AirlineRepo;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@Startup
@ApplicationScoped
public class AirlineService {
    private Map<String, Airline> codeToAirlineMap; // When isUse True
    private Map<Long, Airline> idToAirlineMap;

    @Inject
    private AirlineRepo airlineRepo;

    private static AirlineService instance;

    public AirlineService() {
        instance = this;
    }

    /**
     * init before run class to optimize query: findById, findAll
     */
    @PostConstruct
    void init() {
        List<Airline> airlines = this.airlineRepo.findAll();
        codeToAirlineMap = new HashMap<>();
        idToAirlineMap = new HashMap<>();
        for (Airline airline : airlines) {
            if (airline.getIsUse()) {
                codeToAirlineMap.put(airline.getCode(), airline);
            }
            idToAirlineMap.put(airline.getId(), airline);
        }
    }

    public Airline findById(Long id) {
        return this.idToAirlineMap.get(id);
    }

    public Airline findByCode(String code) { // When isUse True
        return this.codeToAirlineMap.get(code);
    }

    public Airline findOrInsertByCode(String code) { // When isUse True
        Airline airline = this.codeToAirlineMap.get(code);
        if (airline == null) {
            airline = new Airline();
            airline.setCode(code);
            airline.setName(code);
            airline.setNameEng(code);
            airline.setIsUse(true);
            this.airlineRepo.insert(airline);
            codeToAirlineMap.put(airline.getCode(), airline);
            idToAirlineMap.put(airline.getId(), airline);
        }
        return airline;
    }

    public static Airline findByIdStatic(Long id) {
        return instance.findById(id);
    }
}
