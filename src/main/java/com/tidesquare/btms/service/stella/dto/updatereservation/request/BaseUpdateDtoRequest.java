package com.tidesquare.btms.service.stella.dto.updatereservation.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.tidesquare.btms.service.stella.dto.StayInfo;
import com.tidesquare.btms.service.stella.dto.request.PassengerInfoReq;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
@Builder
public class BaseUpdateDtoRequest {
    String orderKey;
    String updateType;
    String ssCode;
    List<PassengerInfoReq> passengerInfos;
    StayInfo stayInfo;
}
