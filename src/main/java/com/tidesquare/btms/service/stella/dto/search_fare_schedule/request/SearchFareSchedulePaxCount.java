package com.tidesquare.btms.service.stella.dto.search_fare_schedule.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class SearchFareSchedulePaxCount {
    private Integer adultCount;
    private Integer childCount;
    private Integer infantCount;
}
