package com.tidesquare.btms.service.stella.dto.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.tidesquare.btms.constant.SectionType;

import java.io.IOException;

public class JourneyTypeSerializer extends JsonSerializer<SectionType> {
    // Java to JSON
    @Override
    public void serialize(SectionType value, JsonGenerator gen, SerializerProvider serializerProvider) throws IOException {
        String transformed = transform(value);
        gen.writeString(transformed);
    }

    private String transform(SectionType input) {
        switch (input) {
        case SectionType.OneWay:
            return "OW";
        case SectionType.RoundTrip:
            return "RT";
        case SectionType.MultiCity:
            return "MD";
        default:
            return null;
        }
    }
}
