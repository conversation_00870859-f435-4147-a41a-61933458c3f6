package com.tidesquare.btms.service.stella.dto;

import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
@NoArgsConstructor
@AllArgsConstructor
public class Contact {
    private String contactType;
    private String email;
    private Name name;
    @Builder.Default
    private List<PhoneType> phoneTypes = new ArrayList<>();

    public String getPhoneNumber() {
        if (this.phoneTypes == null || this.phoneTypes.isEmpty()) {
            return null;
        }

        for (PhoneType phoneType : phoneTypes) {
            if (phoneType.getNumber() != null) {
                return phoneType.getNumber();
            }
        }

        return null;
    }
}
