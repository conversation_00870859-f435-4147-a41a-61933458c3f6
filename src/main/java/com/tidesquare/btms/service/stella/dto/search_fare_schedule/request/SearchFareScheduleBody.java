package com.tidesquare.btms.service.stella.dto.search_fare_schedule.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tidesquare.btms.constant.GDSType;
import com.tidesquare.btms.constant.SeatClass;
import com.tidesquare.btms.service.stella.dto.request.JourneyReq;
import com.tidesquare.btms.service.stella.dto.serializer.CabinClassListSerializer;
import com.tidesquare.btms.service.stella.dto.serializer.ProviderListSerializer;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class SearchFareScheduleBody {
    private String ssCode;
    private List<String> ampCopCode;
    private List<String> bfmCopCode;
    private List<String> airlines;
    private List<JourneyReq> journeyInfos;
    @JsonSerialize(using = CabinClassListSerializer.class)
    private List<SeatClass> cabinClass;
    private SearchFareSchedulePaxCount paxCount;
    @JsonSerialize(using = ProviderListSerializer.class)
    private List<GDSType> provider;
    private String pairType;
    private Long recommendCount;
}
