package com.tidesquare.btms.service.stella.dto.response;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.tidesquare.btms.constant.GDSType;
import com.tidesquare.btms.constant.SectionType;
import com.tidesquare.btms.service.stella.Constant;
import com.tidesquare.btms.service.stella.dto.AgencyItem;
import com.tidesquare.btms.service.stella.dto.FareBasisRule;
import com.tidesquare.btms.service.stella.dto.Fares;
import com.tidesquare.btms.service.stella.dto.Freessr;
import com.tidesquare.btms.service.stella.dto.PNRStatus;
import com.tidesquare.btms.service.stella.dto.serializer.JourneyTypeDeserializer;
import com.tidesquare.btms.service.stella.dto.serializer.ProviderDeserializer;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class JourneyRes {
    private String airline;
    private String arrAirport;
    private String arrivalDate;
    private String departureDate;
    private String deptAirport;
    private String journeyKey;
    @JsonDeserialize(using = JourneyTypeDeserializer.class)
    private SectionType journeyType;
    private String pairKey;
    private String arrAirportName;
    private String deptAirportName;
    private String fareKey;
    private Fares fares;
    private String flightType;
    private String orderId;
    private String passportUpdateTime;
    private String payStatus;
    private String payStatusName;
    private String pnrNumber;
    private PNRStatus pnrStatus;
    private String pnrYN;
    @JsonDeserialize(using = ProviderDeserializer.class)
    private GDSType provider;
    private List<SegmentRes> segments = new ArrayList<>();
    private String ssrPurchaseTime;
    private String stayInfoRegTime;
    private int stops;
    private int tripCount;
    private Boolean virtualRT;
    private Boolean v3Reservation;
    private String airlineName;
    private List<String> alliances = new ArrayList<>();
    private List<Freessr> freessrs = new ArrayList<>();
    private String parentOrderId;
    private String resultCode;
    private String resultMessage;
    private Long recommendScore;
    private String airlineLimitDate;
    private String airlineLimitTime;
    private String fareLimitDate;
    private String fareLimitTime;
    private List<AgencyItem> agencyItems = new ArrayList<>();
    private List<FareBasisRule> fareBasisRules = new ArrayList<>();
    private Double tasfAmount; // only for fe, does not exist in api response

    public String getBaggageAllowance() {
        return String.join(", ", this.freessrs.stream().map(freessr -> freessr.getBaggageAllowance()).filter(baggageAllowance -> !baggageAllowance.isEmpty()).toList());
    }

    public boolean isOverseas() {
        return !Constant.allDomesticAirports.contains(this.deptAirport) || !Constant.allDomesticAirports.contains(this.arrAirport);
    }
}
