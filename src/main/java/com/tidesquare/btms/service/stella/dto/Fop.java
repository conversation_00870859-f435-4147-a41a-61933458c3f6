package com.tidesquare.btms.service.stella.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class Fop {
    private String sfpAccount;
    private Long sfpAmount;
    private String sfpBcdCodeDesc;
    private Long sfpId;
    private String sfpObjTbName;
    private String sfpPaymentType2;
    private Long sfpPcdId;
    private Long sfpPndId;
    private Long sfpPndRevNameSeq;
    private Long sfpPointAmount;
    private Long sfpTrlId;
}
