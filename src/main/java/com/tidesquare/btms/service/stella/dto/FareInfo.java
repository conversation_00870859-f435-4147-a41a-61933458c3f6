package com.tidesquare.btms.service.stella.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
@NoArgsConstructor
@AllArgsConstructor
public class FareInfo {
    private double airFare;
    private double airTax;
    private double changeTax;
    private double fuelChg;
    private double airDiscountAmount;
    private double promotionDiscountAmount;
    private boolean isVirtualFopPromotion;
    private double fopPromotionDiscountAmount;
    private String airPromotionId;
    private String promotionId;
    private String fopPromotionId;
    private double surCharge;
    private double travelAgencyFee;
    private double tkFee;
    private String journeyKey;
    private String designatorCode;
    private String ptcCode;
    private String legacyLabel;
    private double ssrAmount;
    private double legacyPromotionDiscountAmount;
    private double ptrDiscountAmount;
    private String passengerStatus;
    private String currencyCode;
    private String eticketUrl;
    private String fareKey;
    private double promotionAirFare;
    private String ticketNo;

    // private double markupAmount;
    // private double naverTravelClubAmount;
    // private String currencyCode;
}
