package com.tidesquare.btms.service.stella.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class Leg {
    private int flightTime;
    private int waitingTime;
    private String departureTerminal;
    private String deptAirport;
    private String deptAirportName;
    private String deptAirportType;
    private String departureDate;
    private String arrivalTerminal;
    private String arrAirport;
    private String arrAirportName;
    private String arrAirportType;
    private String arrivalDate;
    private String equipmentType;
    private String status;
    private String operatingCarrier;
    private String operatingCarrierName;
    private String operatingFlightNumber;
}
