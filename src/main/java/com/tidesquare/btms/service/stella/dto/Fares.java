package com.tidesquare.btms.service.stella.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.quarkus.runtime.annotations.RegisterForReflection;

import java.util.List;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class Fares {
    private int availableCount;
    private String currencyCode;
    private float totalAirFare;
    private float totalFare;
    private float totalPromotionAmount;
    private float totalPromotionAirFare;
    private float totalPromotionFare;
    private String fareKey;
    private List<String> fareTypes;
    private List<PaxTypeFare> paxTypeFares;

    public boolean isIncludedCorporateFare() {
        for (String fareType : fareTypes) {
            if (fareType.contains("기업운임")) {
                return true;
            }
        }

        return false;
    }
}
