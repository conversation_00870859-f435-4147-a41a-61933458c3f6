package com.tidesquare.btms.service.stella.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class ChangeReqHistory {
    private Long orderId;
    private String messageId;
    private String actionCode;
    private String reqDatetime;
    private String resDatetime;
    private String reqMessage;
    private String resMessage;
}
