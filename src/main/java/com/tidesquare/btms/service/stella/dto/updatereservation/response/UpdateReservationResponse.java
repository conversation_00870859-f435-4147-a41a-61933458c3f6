package com.tidesquare.btms.service.stella.dto.updatereservation.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.tidesquare.btms.service.stella.dto.Contact;
import com.tidesquare.btms.service.stella.dto.response.JourneyRes;
import com.tidesquare.btms.service.stella.dto.response.PassengerInfoRes;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class UpdateReservationResponse {
    private String createDate;
    private String orderKey;
    private String parentOrderKey;
    private int totalFare;
    private int totalAirFare;
    private int totalSsrAmount;
    private int totalAirDiscountAmount;
    private int totalPromotionDiscountAmount;
    private int totalFopPromotionDiscountAmount;
    private int totalPromotionAirFare;
    private int totalFopPromotionAirFare;
    private int totalPromotionFare;
    private int totalFopPromotionFare;
    private int totalAirPromotionFare;
    private int totalAirPromotionAirFare;
    private List<Contact> contacts;
    private List<JourneyRes> journeys;
    private List<PassengerInfoRes> passengerInfos;
}
