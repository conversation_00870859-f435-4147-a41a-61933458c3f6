package com.tidesquare.btms.service.stella.dto.serializer;

import java.io.IOException;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.tidesquare.btms.constant.Gender;

public class GenderSerializer extends JsonSerializer<Gender> {
    // Java to JSON
    @Override
    public void serialize(Gender value, JsonGenerator gen, SerializerProvider serializerProvider) throws IOException {
        String transformed = transform(value);
        gen.writeString(transformed);
    }

    private String transform(Gender input) {
        switch (input) {
        case Gender.Male:
            return "M";
        case Gender.Female:
            return "F";
        default:
            return null;
        }
    }
}
