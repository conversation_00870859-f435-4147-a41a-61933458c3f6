package com.tidesquare.btms.service.stella.dto.cancel_prn;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.tidesquare.btms.service.stella.dto.request.PassengerInfoReq;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class CancelInfo {
    private String actionCode;
    private String cancelReason;
    private String chargeApvDt;
    private String chargeApvNo;
    private Long chargeCardAmount;
    private Long chargeCashAmount;
    private Long chargePointAmount;
    private Long chargeTotalPayAmount;
    private String chargeTransId;
    private String orderId;
    private List<PassengerInfoReq> passengerInfos;
}
