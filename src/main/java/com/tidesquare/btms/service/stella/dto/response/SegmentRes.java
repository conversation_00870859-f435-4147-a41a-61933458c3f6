package com.tidesquare.btms.service.stella.dto.response;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.tidesquare.btms.constant.SeatClass;
import com.tidesquare.btms.service.stella.dto.Leg;
import com.tidesquare.btms.service.stella.dto.serializer.CabinClassDeserializer;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class SegmentRes {
    private String segmentKey;
    private String bookingClass;
    @JsonDeserialize(using = CabinClassDeserializer.class)
    private SeatClass cabinClass;
    private List<Leg> legs = new ArrayList<>();
    private String deptAirport;
    private String deptAirportName;
    private String departureDate;
    private String arrAirport;
    private String arrAirportName;
    private String arrivalDate;
    private String carrierCode;
    private String carrierCodeName;
    private String fareBasisCode;
    private String flightNumber;
    private int flightTime;
    private int waitingTime;
    private String status;
    private List<String> alliances = new ArrayList<>();
    private String fareType;
}
