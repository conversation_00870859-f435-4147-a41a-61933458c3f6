package com.tidesquare.btms.service.stella.dto.serializer;

import java.io.IOException;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.tidesquare.btms.constant.GDSType;

public class ProviderSerializer extends JsonSerializer<GDSType> {
    // Java to JSON
    @Override
    public void serialize(GDSType value, JsonGenerator gen, SerializerProvider serializerProvider) throws IOException {
        String transformed = transform(value);
        gen.writeString(transformed);
    }

    private String transform(GDSType input) {
        switch (input) {
        case GDSType.AMADEUS:
            return "AMP";
        case GDSType.SABRE:
            return "BFM";
        default:
            return null;
        }
    }
}
