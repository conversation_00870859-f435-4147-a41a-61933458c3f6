package com.tidesquare.btms.service.stella.dto.pnr_create;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.tidesquare.btms.service.stella.dto.Contact;
import com.tidesquare.btms.service.stella.dto.response.JourneyRes;
import com.tidesquare.btms.service.stella.dto.response.PassengerInfoRes;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class PNRCreateResponse {
    private String orderKey;
    private String createDate;
    private float totalAirFare;
    private float totalFare;
    private List<Contact> contacts;
    private List<JourneyRes> journeys;
    private List<PassengerInfoRes> passengerInfos;
}