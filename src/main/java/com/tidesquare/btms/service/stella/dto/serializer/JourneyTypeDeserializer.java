package com.tidesquare.btms.service.stella.dto.serializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.tidesquare.btms.constant.SectionType;

import java.io.IOException;

public class JourneyTypeDeserializer extends JsonDeserializer<SectionType> {

    // JSON to Java
    @Override
    public SectionType deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getValueAsString();
        return reverseTransform(value);
    }

    private SectionType reverseTransform(String input) {
        switch (input) {
        case "OW":
            return SectionType.OneWay;
        case "RT":
            return SectionType.RoundTrip;
        case "MD":
            return SectionType.MultiCity;
        default:
            return null;
        }
    }
}