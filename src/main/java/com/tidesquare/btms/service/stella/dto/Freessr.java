package com.tidesquare.btms.service.stella.dto;

import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class Freessr {
    private String journeyKey;
    private String ssrName;
    private List<SsrService> ssrService = new ArrayList<>();
    private String ssrType;
    private String ssrUnit;

    public String getBaggageAllowance() {
        if (this.ssrType.equals("Baggage") && !this.ssrService.isEmpty() && this.ssrService.getFirst().getSsrAmount() == 0) {
            return this.ssrService.getFirst().getSsrValue() + this.ssrUnit;
        }

        return "";
    }
}
