package com.tidesquare.btms.service.stella.dto.response;

import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.tidesquare.btms.service.stella.dto.Contact;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class RetrieveDataRes {
    private List<Contact> contacts = new ArrayList<>();
    private List<JourneyRes> journeys = new ArrayList<>();
    private List<PassengerInfoRes> passengerInfos = new ArrayList<>();
    private float totalAirFare;
    private float totalFare;
    private float totalFopPromotionAirFare;
    private float totalFopPromotionDiscountAmount;
    private float totalFopPromotionFare;
    private float totalPromotionAirFare;
    private float totalPromotionDiscountAmount;
    private float totalLegacyDiscountAmount;
    private float totalPromotionFare;
    private float totalLegacyPromotionFare;
    private float totalLegacyPromotionAirFare;
    private float totalSsrAmount;
    private float prevTotalFare;
    private float currTotalFare;
    private BtmsAdditionalInfoRes btmsAdditionalInfo;
}
