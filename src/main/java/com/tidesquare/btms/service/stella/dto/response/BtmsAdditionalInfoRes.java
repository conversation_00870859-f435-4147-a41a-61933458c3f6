package com.tidesquare.btms.service.stella.dto.response;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class BtmsAdditionalInfoRes {
    private String tsNumber;
    private String companyCd;
    private String groupNumber;
    private List<String> documentNumber = new ArrayList<>();
}
