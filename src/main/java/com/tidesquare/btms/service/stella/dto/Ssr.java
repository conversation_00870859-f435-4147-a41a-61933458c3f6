package com.tidesquare.btms.service.stella.dto;

import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class Ssr {
    private int ssrOrder;
    private String journeyKey;
    private List<String> segmentKey = new ArrayList<>();
    private String ssrName;
    private String ssrType;
    private String ssrUnit;
    private String deptAirport;
    private String arrAirport;
    private List<SsrService> ssrService = new ArrayList<>();
}
