package com.tidesquare.btms.service.stella.dto.search_fare_schedule.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class SearchFareScheduleResponse {
    private Long completedAirline;
    private Long totalAirline;
    private List<SearchFareScheduleJourneyInfo> journeyInfos;
}
