package com.tidesquare.btms.service.stella.dto.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.tidesquare.btms.constant.SeatClass;

import java.io.IOException;

public class CabinClassSerializer extends JsonSerializer<SeatClass> {
    // Java to JSON
    @Override
    public void serialize(SeatClass value, JsonGenerator gen, SerializerProvider serializerProvider) throws IOException {
        String transformed = transform(value);
        gen.writeString(transformed);
    }

    private String transform(SeatClass input) {
        switch (input) {
        case SeatClass.M:
            return "Y";
        case SeatClass.W:
            return "P";
        case SeatClass.F:
            return "F";
        case SeatClass.C:
            return "C";
        default:
            return null;
        }
    }
}
