package com.tidesquare.btms.service.stella.dto.serializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.tidesquare.btms.constant.SeatClass;

import java.io.IOException;

public class CabinClassDeserializer extends JsonDeserializer<SeatClass> {

    // JSON to Java
    @Override
    public SeatClass deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getValueAsString();
        return reverseTransform(value);
    }

    private SeatClass reverseTransform(String input) {
        switch (input) {
        case "Y":
            return SeatClass.M;
        case "P":
            return SeatClass.W;
        case "F":
            return SeatClass.F;
        case "C":
            return SeatClass.C;
        default:
            return null;
        }
    }
}