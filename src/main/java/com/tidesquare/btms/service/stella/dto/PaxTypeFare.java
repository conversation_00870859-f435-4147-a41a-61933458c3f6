package com.tidesquare.btms.service.stella.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.*;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class PaxTypeFare {
    private Long airDiscountAmount;
    private Long airFare;
    private Long airFareUsedSorting;
    private Long airTax;
    private Long fopPromotionDiscountAmount;
    private Long fuelChg;
    private Long legacyPromotionDiscountAmount;
    private Long naverTravelClubAmount;
    private String paxType;
    private String promotionDiscountAmount;
    private String promotionYn;
    private Boolean selectedFare;
    private Long ssrAmount;
    private Long tkFee;
    private Boolean virtualFopPromotion;
    private String fopCardCode;
    private String fopCardName;
    private String fopPromotionId;
    private String promotionType;
    private String legacyPromotionCode;
    private String legacyPromotionName;
    private String legacyLabel;
    private Boolean isSelectedFare;
}
