package com.tidesquare.btms.service.stella.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class FareBasisRule {
    private List<String> fareBasisCode = new ArrayList<>();
    private List<FareBasisRuleItem> items = new ArrayList<>();
}
