package com.tidesquare.btms.service.stella.dto.serializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.tidesquare.btms.constant.Gender;

import java.io.IOException;

public class GenderDeserializer extends JsonDeserializer<Gender> {

    // JSON to Java
    @Override
    public Gender deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getValueAsString();
        return reverseTransform(value);
    }

    private Gender reverseTransform(String input) {
        switch (input) {
        case "M":
            return Gender.Male;
        case "BFM":
            return Gender.Female;
        default:
            return null;
        }
    }
}