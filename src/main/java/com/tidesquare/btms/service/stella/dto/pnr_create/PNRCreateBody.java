package com.tidesquare.btms.service.stella.dto.pnr_create;

import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.tidesquare.btms.service.stella.dto.Contact;
import com.tidesquare.btms.service.stella.dto.request.JourneyReq;
import com.tidesquare.btms.service.stella.dto.request.PassengerInfoReq;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class PNRCreateBody {
    private String ssCode;
    private String device;
    private List<Contact> contacts;
    private List<JourneyReq> journeys;
    private List<PassengerInfoReq> passengerInfos;
}