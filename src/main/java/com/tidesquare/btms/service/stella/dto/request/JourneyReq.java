package com.tidesquare.btms.service.stella.dto.request;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tidesquare.btms.constant.GDSType;
import com.tidesquare.btms.constant.SectionType;
import com.tidesquare.btms.service.stella.Constant;
import com.tidesquare.btms.service.stella.dto.Fares;
import com.tidesquare.btms.service.stella.dto.Freessr;
import com.tidesquare.btms.service.stella.dto.PNRStatus;
import com.tidesquare.btms.service.stella.dto.serializer.JourneyTypeSerializer;
import com.tidesquare.btms.service.stella.dto.serializer.ProviderSerializer;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class JourneyReq {
    private String airline;
    private String arrAirport;
    private String arrivalDate;
    private String departureDate;
    private String deptAirport;
    private String journeyKey;
    @JsonSerialize(using = JourneyTypeSerializer.class)
    private SectionType journeyType;
    private String pairKey;
    private String arrAirportName;
    private String deptAirportName;
    private String fareKey;
    private Fares fares;
    private String flightType;
    private String orderId;
    private String passportUpdateTime;
    private String payStatus;
    private String payStatusName;
    private String pnrNumber;
    private PNRStatus pnrStatus;
    private String pnrYN;
    @JsonSerialize(using = ProviderSerializer.class)
    private GDSType provider;
    @Builder.Default
    private List<SegmentReq> segments = new ArrayList<>();
    private String ssrPurchaseTime;
    private String stayInfoRegTime;
    private int stops;
    private int tripCount;
    private Boolean virtualRT;
    private Boolean v3Reservation;
    private String airlineName;
    @Builder.Default
    private List<String> alliances = new ArrayList<>();
    @Builder.Default
    private List<Freessr> freessrs = new ArrayList<>();
    private String parentOrderId;
    private String resultCode;
    private String resultMessage;
    private Long recommendScore;

    public String getBaggageAllowance() {
        return String.join(", ", this.freessrs.stream().map(freessr -> freessr.getBaggageAllowance()).filter(baggageAllowance -> !baggageAllowance.isEmpty()).toList());
    }

    public boolean isOverseas() {
        return !Constant.allDomesticAirports.contains(this.deptAirport) || !Constant.allDomesticAirports.contains(this.arrAirport);
    }
}
