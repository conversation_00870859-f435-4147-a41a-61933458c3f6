package com.tidesquare.btms.service.stella.dto.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.tidesquare.btms.constant.GDSType;

import java.io.IOException;
import java.util.List;

public class ProviderListSerializer extends JsonSerializer<List<GDSType>> {
    // Java to JSON
    @Override
    public void serialize(List<GDSType> value, JsonGenerator gen, SerializerProvider serializerProvider) throws IOException {
        gen.writeStartArray();
        for (GDSType item : value) {
            String transformed = transform(item);
            gen.writeString(transformed);
        }
        gen.writeEndArray();
    }

    private String transform(GDSType input) {
        switch (input) {
        case GDSType.AMADEUS:
            return "AMP";
        case GDSType.SABRE:
            return "BFM";
        default:
            return null;
        }
    }
}
