package com.tidesquare.btms.service.stella.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class ChangeAirFareInfo {
    private String transId;
    private float totalAirFareDiff;
    private float totalAirTaxDiff;
    private float totalFuelChgDiff;
    private float totalPromotionDiscountDiff;
}
