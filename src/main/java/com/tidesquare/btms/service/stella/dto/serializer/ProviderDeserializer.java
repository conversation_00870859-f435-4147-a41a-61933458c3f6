package com.tidesquare.btms.service.stella.dto.serializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.tidesquare.btms.constant.GDSType;

import java.io.IOException;

public class ProviderDeserializer extends JsonDeserializer<GDSType> {

    // JSON to Java
    @Override
    public GDSType deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        String value = p.getValueAsString();
        return reverseTransform(value);
    }

    private GDSType reverseTransform(String input) {
        switch (input) {
        case "AMP":
            return GDSType.AMADEUS;
        case "BFM":
            return GDSType.SABRE;
        default:
            return null;
        }
    }
}