package com.tidesquare.btms.service.stella.dto.get_fare_rule;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.tidesquare.btms.service.stella.dto.response.JourneyRes;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class GetFareRuleResponse {
    private List<JourneyRes> journeys;
}
