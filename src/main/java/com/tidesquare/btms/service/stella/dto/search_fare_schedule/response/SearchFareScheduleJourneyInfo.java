package com.tidesquare.btms.service.stella.dto.search_fare_schedule.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.tidesquare.btms.service.stella.dto.response.JourneyRes;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class SearchFareScheduleJourneyInfo {
    private List<JourneyRes> journeys;
}
