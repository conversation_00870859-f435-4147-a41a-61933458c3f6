package com.tidesquare.btms.service.stella.dto.request;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tidesquare.btms.constant.SeatClass;
import com.tidesquare.btms.service.stella.dto.Leg;
import com.tidesquare.btms.service.stella.dto.serializer.CabinClassSerializer;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class SegmentReq {
    private String segmentKey;
    private String bookingClass;
    @JsonSerialize(using = CabinClassSerializer.class)
    private SeatClass cabinClass;
    @Builder.Default
    private List<Leg> legs = new ArrayList<>();
    private String deptAirport;
    private String deptAirportName;
    private String departureDate;
    private String arrAirport;
    private String arrAirportName;
    private String arrivalDate;
    private String carrierCode;
    private String carrierCodeName;
    private String fareBasisCode;
    private String flightNumber;
    private int flightTime;
    private int waitingTime;
    private String status;
    @Builder.Default
    private List<String> alliances = new ArrayList<>();
    private String fareType;
}
