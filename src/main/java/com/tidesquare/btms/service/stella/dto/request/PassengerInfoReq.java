package com.tidesquare.btms.service.stella.dto.request;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tidesquare.btms.constant.Gender;
import com.tidesquare.btms.service.stella.dto.FareInfo;
import com.tidesquare.btms.service.stella.dto.Freessr;
import com.tidesquare.btms.service.stella.dto.Name;
import com.tidesquare.btms.service.stella.dto.Passport;
import com.tidesquare.btms.service.stella.dto.Ssr;
import com.tidesquare.btms.service.stella.dto.serializer.GenderSerializer;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
@NoArgsConstructor
public class PassengerInfoReq {
    private String dateOfBirth;
    @Builder.Default
    private List<FareInfo> fareInfos = new ArrayList<>();
    @Builder.Default
    private List<Freessr> freessrs = new ArrayList<>();
    @Builder.Default
    private List<Ssr> ssrs = new ArrayList<>();
    private Name name;
    @JsonSerialize(using = GenderSerializer.class)
    private Gender gender;
    private String nationality;
    private Passport passport;
    private int passengerIdx;
    private String paxType;

    public Map<String, String> getBaggageAllowanceMap() {
        Map<String, String> baggageAllowanceMap = new HashMap<>();
        for (Freessr freessr : this.freessrs) {
            String journeyKey = freessr.getJourneyKey();
            if (!baggageAllowanceMap.containsKey(journeyKey)) {
                baggageAllowanceMap.put(journeyKey, "");
            }
            String newBaggageAllowance = freessr.getBaggageAllowance();
            if (!newBaggageAllowance.isEmpty()) {
                String oldBaggageAllowance = baggageAllowanceMap.get(journeyKey);
                if (oldBaggageAllowance.isEmpty()) {
                    oldBaggageAllowance = newBaggageAllowance;
                } else {
                    oldBaggageAllowance += (", " + newBaggageAllowance);
                }
                baggageAllowanceMap.put(journeyKey, oldBaggageAllowance);
            }
        }

        return baggageAllowanceMap;
    }
}
