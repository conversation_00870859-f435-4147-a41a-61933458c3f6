package com.tidesquare.btms.service.stella.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@RegisterForReflection
public class SsrService {
    private String paxType;
    private int quantity;
    private int maxQuantity;
    private float ssrAmount;
    private String ssrCode;
    private String ssrValue;
}
