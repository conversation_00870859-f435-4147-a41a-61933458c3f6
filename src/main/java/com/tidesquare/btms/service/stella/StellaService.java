package com.tidesquare.btms.service.stella;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.http.HttpRequest.BodyPublishers;
import java.net.http.HttpResponse.BodyHandlers;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.eclipse.microprofile.config.inject.ConfigProperty;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tidesquare.btms.constant.GDSType;
import com.tidesquare.btms.service.stella.dto.baseclass.RequestBase;
import com.tidesquare.btms.service.stella.dto.cancel_prn.CancelBody;
import com.tidesquare.btms.service.stella.dto.cancel_prn.CancelResponse;
import com.tidesquare.btms.service.stella.dto.get_fare_rule.GetFareRuleBody;
import com.tidesquare.btms.service.stella.dto.get_fare_rule.GetFareRuleResponse;
import com.tidesquare.btms.service.stella.dto.pnr_create.PNRCreateBody;
import com.tidesquare.btms.service.stella.dto.pnr_create.PNRCreateResponse;
import com.tidesquare.btms.service.stella.dto.response.PNRInfoRes;
import com.tidesquare.btms.service.stella.dto.search_fare_schedule.request.SearchFareScheduleBody;
import com.tidesquare.btms.service.stella.dto.search_fare_schedule.response.SearchFareScheduleResponse;
import com.tidesquare.btms.service.stella.dto.updatereservation.request.BaseUpdateDtoRequest;
import com.tidesquare.btms.service.stella.dto.updatereservation.response.UpdateReservationResponse;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.core.MediaType;

@Startup
@ApplicationScoped
public class StellaService {
    @ConfigProperty(name = "STELLA_BASE_URL", defaultValue = "")
    private String baseURL;

    @ConfigProperty(name = "STELLA_ACCESS_TOKEN", defaultValue = "")
    private String token;

    private String version = "0.3";

    private HttpClient httpClient = HttpClient.newBuilder().connectTimeout(Duration.ofSeconds(30)).build();
    private ObjectMapper objectMapper = new ObjectMapper();

    @PostConstruct
    public void init() {
    }

    public PNRInfoRes GetPNRInfo(String pnr, GDSType gdsType, String orderId) {
        String provider;
        if (gdsType.equals(GDSType.AMADEUS)) {
            provider = "AMP";
        } else if (gdsType.equals(GDSType.SABRE)) {
            provider = "BFM";
        } else {
            throw new RuntimeException("gdsType is invalid");
        }
        try {
            String url = this.baseURL + "/v3/admin/directRawPnr?pnr=" + pnr + "&provider=" + provider + "&orderId=";
            if (orderId != null && !orderId.isBlank()) {
                url += orderId;
            }
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .header("Authorization", "Bearer " + token)
                    .GET()
                    .build();
            Long startTime = System.currentTimeMillis();
            HttpResponse<String> response = this.httpClient.send(request, BodyHandlers.ofString());
            String responseStr = response.body();
            JsonNode responseJson = this.objectMapper.readTree(responseStr);
            Long responseTime = System.currentTimeMillis() - startTime;
            System.out.println("method: " + request.method() + ", url: " + this.baseURL + request.uri() + ", "
                    + "responseTime: " + responseTime + "ms");
            System.out.println("responseStr: " + responseJson.toString());
            if (!responseJson.isObject()
                    || !responseJson.has("resultStatus") || !responseJson.get("resultStatus").isBoolean()
                    || !responseJson.get("resultStatus").asBoolean()
                    || !responseJson.has("resultCode") || !responseJson.get("resultCode").isTextual()
                    || !responseJson.get("resultCode").asText().equals("200")
                    || !responseJson.has("response") || !responseJson.get("response").isObject()) {
                if (responseJson.isObject() && responseJson.has("error")
                        && responseJson.get("error").has("errorMessage")
                        && responseJson.get("error").get("errorMessage").isTextual()) {
                    throw new RuntimeException(responseJson.get("error").get("errorMessage").asText());
                }
                throw new RuntimeException("Invalid response structure or content");
            }
            JsonNode responseJsonResponse = responseJson.get("response");
            // if (!responseJsonResponse.has("errorCode") || !responseJsonResponse.get("errorCode").isTextual()
            //         || !responseJsonResponse.get("errorCode").asText().equals("200")) {
            //     throw new RuntimeException("Invalid response structure or content");
            // }
            if (!responseJsonResponse.has("btmsResponse") || !responseJsonResponse.get("btmsResponse").isArray()
                    || responseJsonResponse.get("btmsResponse").isEmpty()) {
                throw new RuntimeException("Invalid response structure or content");
            }
            JsonNode responseJsonResponseBTMSResponse = responseJsonResponse.get("btmsResponse").get(0);
            if (!responseJsonResponseBTMSResponse.isObject()) {
                throw new RuntimeException("Invalid response structure or content");
            }
            PNRInfoRes pnrInfo = this.objectMapper.treeToValue(responseJsonResponseBTMSResponse, PNRInfoRes.class);
            return pnrInfo;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    public List<PNRInfoRes> GetOfflinePNRs(GDSType gdsType) {
        String provider;
        if (gdsType.equals(GDSType.AMADEUS)) {
            provider = "AMP";
        } else if (gdsType.equals(GDSType.SABRE)) {
            provider = "BFM";
        } else {
            throw new RuntimeException("gdsType is invalid");
        }
        try {
            String url = this.baseURL + "/v3/admin/btmsOffPnr?" + "provider=" + provider;
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .header("Authorization", "Bearer " + token)
                    .GET()
                    .build();
            Long startTime = System.currentTimeMillis();
            HttpResponse<String> response = this.httpClient.send(request, BodyHandlers.ofString());
            String responseStr = response.body();
            JsonNode responseJson = this.objectMapper.readTree(responseStr);
            Long responseTime = System.currentTimeMillis() - startTime;
            System.out.println("method: " + request.method() + ", url: " + this.baseURL + request.uri() + ", "
                    + "responseTime: " + responseTime + "ms");
            System.out.println("responseStr: " + responseJson.toString());
            if (!responseJson.isObject()
                    || !responseJson.has("resultStatus") || !responseJson.get("resultStatus").isBoolean()
                    || !responseJson.get("resultStatus").asBoolean()
                    || !responseJson.has("resultCode") || !responseJson.get("resultCode").isTextual()
                    || !responseJson.get("resultCode").asText().equals("200")
                    || !responseJson.has("response") || !responseJson.get("response").isObject()) {
                if (responseJson.isObject() && responseJson.has("error")
                        && responseJson.get("error").has("errorMessage")
                        && responseJson.get("error").get("errorMessage").isTextual()) {
                    throw new RuntimeException(responseJson.get("error").get("errorMessage").asText());
                }
                throw new RuntimeException("Invalid response structure or content");
            }
            JsonNode responseJsonResponse = responseJson.get("response");
            // if (!responseJsonResponse.has("errorCode") || !responseJsonResponse.get("errorCode").isTextual()
            //         || !responseJsonResponse.get("errorCode").asText().equals("200")) {
            //     throw new RuntimeException("Invalid response structure or content");
            // }
            if (!responseJsonResponse.has("btmsResponse") || !responseJsonResponse.get("btmsResponse").isArray()) {
                throw new RuntimeException("Invalid response structure or content");
            }
            JsonNode responseJsonResponseBTMSResponse = responseJsonResponse.get("btmsResponse");
            List<PNRInfoRes> pnrInfos = new ArrayList<>();
            for (int i = 0; i < responseJsonResponseBTMSResponse.size(); i++) {
                PNRInfoRes pnrInfo = this.objectMapper.treeToValue(responseJsonResponseBTMSResponse.get(i),
                        PNRInfoRes.class);
                pnrInfos.add(pnrInfo);
            }
            return pnrInfos;
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    public PNRCreateResponse CreatePNR(PNRCreateBody body) {
        return sendRequestPost("/v3/openapi/pnrCreate", body, PNRCreateResponse.class, null, null);
    }

    public SearchFareScheduleResponse searchFair(SearchFareScheduleBody body) {
        return sendRequestPost("/v3/openapi/searchFareSchedule", body, SearchFareScheduleResponse.class, null, null);
    }

    public UpdateReservationResponse updatedReservation(BaseUpdateDtoRequest request) {
        if (request.getUpdateType().equalsIgnoreCase("UpdatePassport")) {
            return sendRequestPost("/v3/openapi/updateReservation", request, UpdateReservationResponse.class, null, null);
        } else if (request.getUpdateType().equalsIgnoreCase("UpdateStayInfo")) {
            return sendRequestPost("/v3/openapi/updateReservation", request, UpdateReservationResponse.class, null, null);
        }
        return null;
    }

    public CancelResponse cancelPRN(CancelBody cancelBodyRequest) {
        Map<String, String> headers = new HashMap<>();
        headers.put("userId", "TS1299");
        headers.put("userName", "LEEPYEONGHWA");
        headers.put("crePath", "ST");
        return sendRequestPost("/v3/openapi/pnrCancel", cancelBodyRequest, CancelResponse.class, headers, "DirectCancel");
    }

    public GetFareRuleResponse getFareRule(GetFareRuleBody getFareRuleBody) {
        return sendRequestPost("/v3/openapi/getFareRule", getFareRuleBody, GetFareRuleResponse.class, null, null);
    }

    private <T, R> R sendRequestPost(String endpoint, T requestPayload, Class<R> responseType, Map<String, String> headers, String method) {
        try {
            String url = this.baseURL + endpoint;
            String json = null;
            if (requestPayload != null) {
                var requestBuilder = RequestBase.<T>builder()
                        .request(requestPayload)
                        .version(version);
                if (method != null) {
                    requestBuilder.method(method);
                }
                RequestBase<T> request = requestBuilder.build();

                json = objectMapper.writeValueAsString(request);
                System.out.println("body: " + json);
            }
            HttpRequest.Builder httpRequestBuilder = HttpRequest.newBuilder()
                    .uri(URI.create(url))
                    .header("Content-Type", MediaType.APPLICATION_JSON)
                    .header("Authorization", "Bearer " + this.token)
                    .POST(json != null ? BodyPublishers.ofString(json) : null);
            if (headers != null) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    httpRequestBuilder.header(entry.getKey(), entry.getValue());
                }
            }
            HttpRequest request = httpRequestBuilder.build();

            return processResponse(request, responseType);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage());
        } catch (InterruptedException e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    private <T> T processResponse(HttpRequest request, Class<T> responseType) throws IOException, InterruptedException {
        Long startTime = System.currentTimeMillis();
        HttpResponse<String> response = this.httpClient.send(request, BodyHandlers.ofString());
        String responseStr = response.body();
        JsonNode responseJson = this.objectMapper.readTree(responseStr);
        Long responseTime = System.currentTimeMillis() - startTime;
        System.out.println("method: " + request.method() + ", url: " + this.baseURL + request.uri() + ", "
                + "responseTime: " + responseTime + "ms");
        System.out.println("responseStr: " + responseJson.toString());
        if (!responseJson.isObject()
                || !responseJson.has("resultStatus") || !responseJson.get("resultStatus").isBoolean()
                || !responseJson.get("resultStatus").asBoolean()
                || !responseJson.has("resultCode") || !responseJson.get("resultCode").isTextual()
                || !responseJson.get("resultCode").asText().equals("200")
                || !responseJson.has("response") || !responseJson.get("response").isObject()) {
            if (responseJson.isObject() && responseJson.has("error")
                    && responseJson.get("error").has("errorMessage")
                    && responseJson.get("error").get("errorMessage").isTextual()) {
                throw new RuntimeException(responseJson.get("error").get("errorMessage").asText());
            }
            throw new RuntimeException("Invalid response structure or content");
        }
        JsonNode responseJsonResponse = responseJson.get("response");
        return objectMapper.treeToValue(responseJsonResponse, responseType);
    }

    @PreDestroy
    public void preDestroy() throws IOException {
        this.httpClient.close();
    }
}
