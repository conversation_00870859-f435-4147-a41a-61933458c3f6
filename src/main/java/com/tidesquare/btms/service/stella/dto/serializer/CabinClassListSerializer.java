package com.tidesquare.btms.service.stella.dto.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.tidesquare.btms.constant.SeatClass;

import java.io.IOException;
import java.util.List;

public class CabinClassListSerializer extends JsonSerializer<List<SeatClass>> {
    // Java to JSON
    @Override
    public void serialize(List<SeatClass> value, JsonGenerator gen, SerializerProvider serializerProvider) throws IOException {
        gen.writeStartArray();
        for (SeatClass item : value) {
            String transformed = transform(item);
            gen.writeString(transformed);
        }
        gen.writeEndArray();
    }

    private String transform(SeatClass input) {
        switch (input) {
        case SeatClass.M:
            return "Y";
        case SeatClass.W:
            return "P";
        case SeatClass.F:
            return "F";
        case SeatClass.C:
            return "C";
        default:
            return null;
        }
    }
}
