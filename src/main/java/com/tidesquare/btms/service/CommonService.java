package com.tidesquare.btms.service;

import com.tidesquare.btms.entity.Airport;
import com.tidesquare.btms.entity.BookingAirSchedule;
import com.tidesquare.btms.entity.City;
import com.tidesquare.btms.entity.Country;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

import java.util.SortedSet;

@ApplicationScoped
public class CommonService {
    @Inject
    private AirportService airportService;

    @Inject
    private CityService cityService;

    @Inject
    private CountryService countryService;

    private boolean isAmericaSchedule(BookingAirSchedule bookingAirSchedule) {
        Airport toAirport = this.airportService.findById(bookingAirSchedule.getToAirportId());
        if (toAirport != null) {
            City toCity = this.cityService.findById(toAirport.getCityId());
            if (toCity != null) {
                Country toCountry = this.countryService.findById(toCity.getCountryId());
                if (toCountry != null && toCountry.getCode2().equals("US") && toCountry.getCode3().equals("USA")) {
                    return true;
                }
            }
        }

        return false;
    }

    public boolean isAmericaSchedules(SortedSet<BookingAirSchedule> bookingAirSchedules) {
        BookingAirSchedule prevBookingAirSchedule = null;
        for (BookingAirSchedule bookingAirSchedule : bookingAirSchedules) {
            if (!bookingAirSchedule.isVia() && prevBookingAirSchedule != null) {
                if (this.isAmericaSchedule(prevBookingAirSchedule)) {
                    return true;
                }
            }

            prevBookingAirSchedule = bookingAirSchedule;
        }

        return this.isAmericaSchedule(prevBookingAirSchedule);
    }
}
