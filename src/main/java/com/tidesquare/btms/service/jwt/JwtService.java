package com.tidesquare.btms.service.jwt;

import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.jwt.JsonWebToken;

import com.tidesquare.btms.common.UserInfo;
import com.tidesquare.btms.constant.UserType;

import io.smallrye.jwt.auth.principal.JWTParser;
import io.smallrye.jwt.build.Jwt;
import io.smallrye.jwt.build.JwtClaimsBuilder;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.json.JsonNumber;

@ApplicationScoped
public class JwtService {
    @Inject
    private JWTParser parser;

    @ConfigProperty(name = "JWT_SECRET", defaultValue = "")
    private String secretKey;

    @ConfigProperty(name = "JWT_EXPIRE_IN", defaultValue = "0")
    private Long expireInSeconds;

    public UserInfo verifyToken(String token) {
        try {
            JsonWebToken jwt = this.parser.verify(token, this.secretKey);
            Long userId = Long.parseLong(jwt.getSubject());
            Long companyId = ((JsonNumber) jwt.getClaim("company_id")).longValue();
            UserType userType = UserType.valueOf((String) jwt.getClaim("user_type"));
            Boolean isAdmin = null;
            if (jwt.getClaim("is_admin") != null) {
                isAdmin = jwt.getClaim("is_admin").toString().equals("true");
            }
            return new UserInfo(userId, companyId, userType, isAdmin);
        } catch (Exception e) {
            return null;
        }
    }

    public String createToken(UserInfo userInfo) {
        JwtClaimsBuilder jwtClaimsBuilder = Jwt.subject(String.valueOf(userInfo.getUserId()))
                .claim("company_id", userInfo.getCompanyId())
                .claim("user_type", userInfo.getUserType().toString())
                .claim("is_admin", userInfo.getIsAdmin())
                .expiresIn(this.expireInSeconds);

        return jwtClaimsBuilder.signWithSecret(this.secretKey);
    }

}
