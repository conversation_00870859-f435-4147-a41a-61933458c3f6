package com.tidesquare.btms.service.googlemap;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.http.HttpResponse.BodyHandlers;
import java.time.Duration;

import org.eclipse.microprofile.config.inject.ConfigProperty;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.extern.slf4j.Slf4j;

@Startup
@ApplicationScoped
@Slf4j
public class GoogleMapService {
    @ConfigProperty(name = "GOOGLE_MAP_API_KEY", defaultValue = "")
    private String apiKey;

    private String baseURL = "https://maps.googleapis.com";

    private HttpClient httpClient = HttpClient.newBuilder().connectTimeout(Duration.ofSeconds(30)).build();
    private ObjectMapper objectMapper = new ObjectMapper();

    @PostConstruct
    public void init() {
    }

    @PreDestroy
    public void preDestroy() throws IOException {
        this.httpClient.close();
    }

    public Place FindPlaceFromText(String text) {
        try {
            String url = this.baseURL + "/maps/api/place/findplacefromtext/json?fields=geometry&inputtype=textquery&input=" + text + "&key=" + this.apiKey;
            HttpRequest request = HttpRequest.newBuilder().uri(URI.create(url)).GET().build();
            HttpResponse<String> response = this.httpClient.send(request, BodyHandlers.ofString());
            String responseStr = response.body();
            JsonNode responseJson = this.objectMapper.readTree(responseStr);
            if (!responseJson.get("status").asText().equals("OK")) {
                throw new GoogleMapException(responseJson.get("error_message").asText());
            }
            Place place = this.objectMapper.treeToValue(responseJson.get("candidates").get(0), Place.class);
            return place;
        } catch (Exception e) {
            throw new GoogleMapException(e.getMessage());
        }
    }
}
