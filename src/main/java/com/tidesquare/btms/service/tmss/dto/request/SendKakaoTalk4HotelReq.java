package com.tidesquare.btms.service.tmss.dto.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Getter;

@Getter
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SendKakaoTalk4HotelReq implements SendReq<SendKakaoTalk4HotelReq> {

  private String kkoCode;
  private String toId;
  private String toName;
  private String toNumber;

  private String map1;
  private String map2;
  private String map3;
  private String map4;
  private String map5;
  private String map6;
  private String map7;
  private String map8;
  private String map9;
  private String map10;
  private String map11;

  public SendKakaoTalk4HotelReq() {
  }

  public SendKakaoTalk4HotelReq withTo(String toId, String toName, String toNumber) {
    this.toId = toId;
    this.toName = toName;
    this.toNumber = toNumber;
    return this;
  }

  /*
   * MAP1 AS SITE_NAME 사이트명
   * MAP2 AS RSVN_NUMBER
   * MAP3 AS MEMBER_NAME
   * MAP4 AS RSVN_SCHEDULE
   * MAP5 AS RSVN_PASSENGER
   * MAP6 AS MYPAGE_URL
   */
  public SendKakaoTalk4HotelReq withMap(String bookingMemberName, String bookingCode, String hotelName,
      String checkinDate, String checkoutDate, String night, String roomType, String roomCnt, String cancelLimit,
      String sprBookingNo, String myPageUrl) {
    this.map1 = bookingMemberName;
    this.map2 = bookingCode;
    this.map3 = hotelName;
    this.map4 = checkinDate;
    this.map5 = checkoutDate;
    this.map6 = night;
    this.map7 = roomType;
    this.map8 = roomCnt;
    this.map9 = cancelLimit;
    this.map10 = sprBookingNo;
    this.map11 = myPageUrl;
    return this;
  }

  @Override
  public SendKakaoTalk4HotelReq setIdentificationCode(String code) {
    this.kkoCode = code;
    return this;
  }

  public String toJsonString() {
    final StringBuilder sb = new StringBuilder();
    sb.append("{ \"kko_code\": \"").append(kkoCode).append('\"');
    sb.append(", \"to_id\": \"").append(toId).append('\"');
    sb.append(", \"to_name\": \"").append(toName).append('\"');
    sb.append(", \"to_number\": \"").append(toNumber).append('\"');

    if (map1 != null) {
      sb.append(", \"map1\": \"").append(map1).append('\"');
    }
    if (map2 != null) {
      sb.append(", \"map2\": \"").append(map2).append('\"');
    }
    if (map3 != null) {
      sb.append(", \"map3\": \"").append(map3).append('\"');
    }
    if (map4 != null) {
      sb.append(", \"map4\": \"").append(map4).append('\"');
    }
    if (map5 != null) {
      sb.append(", \"map5\": \"").append(map5).append('\"');
    }
    if (map6 != null) {
      sb.append(", \"map6\": \"").append(map6).append('\"');
    }
    if (map7 != null) {
      sb.append(", \"map7\": \"").append(map7).append('\"');
    }
    if (map8 != null) {
      sb.append(", \"map8\": \"").append(map8).append('\"');
    }
    if (map9 != null) {
      sb.append(", \"map9\": \"").append(map9).append('\"');
    }
    if (map10 != null) {
      sb.append(", \"map10\": \"").append(map10).append('\"');
    }
    if (map11 != null) {
      sb.append(", \"map11\": \"").append(map11).append('\"');
    }
    sb.append('}');
    return sb.toString();
  }
}
