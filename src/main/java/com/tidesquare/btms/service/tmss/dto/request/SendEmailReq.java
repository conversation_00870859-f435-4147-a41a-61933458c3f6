package com.tidesquare.btms.service.tmss.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tidesquare.btms.service.tmss.Base64Serializer;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@RegisterForReflection
public class SendEmailReq implements SendReq<SendEmailReq> {

	@JsonProperty(value = "mail_code")
	private String mailCode;

	@JsonProperty(value = "to_id")
	private String toId;

	@JsonProperty(value = "to_email")
	private String toEmail;

	@JsonProperty(value = "to_name")
	private String toName;

	@JsonProperty(value = "from_email")
	private String fromEmail = "<EMAIL>";

	@JsonProperty(value = "from_name")
	private String fromName = "BTMS";

	@JsonProperty(value = "subject")
	private String subject;

	@JsonProperty(required = true, value = "map_content")
	@JsonSerialize(using = Base64Serializer.class)
	private String content;

	@Override
	public SendEmailReq setIdentificationCode(String code) {
		this.mailCode = code;
		return this;
	}

	@Override
	public String toString() {
		return new StringBuilder(super.toString())
				.append("{ mailCode='").append(mailCode).append('\'')
				.append(", toId='").append(toId).append('\'')
				.append(", toEmail='").append(toEmail).append('\'')
				.append(", toName='").append(toName).append('\'')
				.append(", fromEmail='").append(fromEmail).append('\'')
				.append(", fromName='").append(fromName).append('\'')
				.append(", subject='").append(subject).append('\'')
				.append(", content='").append(content).append('\'')
				.append('}').toString();
	}
}