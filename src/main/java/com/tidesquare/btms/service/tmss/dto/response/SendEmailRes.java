package com.tidesquare.btms.service.tmss.dto.response;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Getter
@Setter
@Accessors(chain = true)
public class SendEmailRes {
  private Boolean success;
  private String message;

  @JsonCreator
  public SendEmailRes(@JsonProperty(value = "success", required = true) Boolean success,
      @JsonProperty(value = "message", required = true) String message) {
    this.success = success;
    this.message = message;
  }

  @Override
  public String toString() {
    final StringBuilder sb = new StringBuilder(super.toString());
    sb.append("{ success=").append(success);
    sb.append(", message='").append(message).append('\'');
    sb.append('}');
    return sb.toString();
  }
}
