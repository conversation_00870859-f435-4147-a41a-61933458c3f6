package com.tidesquare.btms.service.tmss.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.tidesquare.btms.service.tmss.Base64Serializer;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SendSMSReq implements SendReq<SendSMSReq> {
  private String mmsCode;
  private String toId;
  private String toName;
  private String toNumber;
  private String fromNumber;
  private String subject;
  private String content;

  @Override
  public SendSMSReq setIdentificationCode(String code) {
    this.mmsCode = code;
    return this;
  }

  @JsonProperty(required = true)
  public String getMmsCode() {
    return mmsCode;
  }

  @JsonProperty(required = true)
  public String getToId() {
    return toId;
  }

  @JsonProperty(required = true)
  public String getToName() {
    return toName;
  }

  @JsonProperty(required = true)
  public String getToNumber() {
    return toNumber;
  }

  public static SendSMSReq sendSMSSendRequest() {
    return new SendSMSReq();
  }

  @JsonProperty(required = true)
  public String getSubject() {
    return subject;
  }

  @JsonProperty(required = true, value = "map_content")
  @JsonSerialize(using = Base64Serializer.class)
  public String getContent() {
    return content;
  }

  @JsonProperty(required = true)
  public String getFromNumber() {
    return fromNumber;
  }

  public SendSMSReq withToId(String toId) {
    this.toId = toId;
    return this;
  }

  public SendSMSReq withToNumber(String toNumber) {
    this.toNumber = toNumber;
    return this;
  }

  public SendSMSReq withFromNumber(String fromNumber) {
    this.fromNumber = fromNumber;
    return this;
  }

  public SendSMSReq withSubject(String subject) {
    this.subject = subject;
    return this;
  }

  public SendSMSReq withContent(String content) {
    this.content = content;
    return this;
  }

}
