package com.tidesquare.btms.service.tmss;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpRequest.BodyPublishers;
import java.net.http.HttpResponse;

import org.eclipse.microprofile.config.inject.ConfigProperty;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tidesquare.btms.service.tmss.dto.request.SendEmailReq;
import com.tidesquare.btms.service.tmss.dto.request.SendKakaoTalk4HotelReq;
import com.tidesquare.btms.service.tmss.dto.request.SendKakaoTalkReq;
import com.tidesquare.btms.service.tmss.dto.request.SendReq;
import com.tidesquare.btms.service.tmss.dto.request.SendSMSReq;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.core.MediaType;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@ApplicationScoped
public class TmssService {
  @ConfigProperty(name = "TMSS_KEY", defaultValue = "")
  private String identificationCode;

  @ConfigProperty(name = "TMSS_URL", defaultValue = "")
  private String targetUri;

  @ConfigProperty(name = "TMSS_KAKAO_URL", defaultValue = "")
  private String kakaoTalkTargetUri;

  @ConfigProperty(name = "TMSS_SMS_CODE", defaultValue = "")
  private String smsCode;

  @ConfigProperty(name = "TMSS_SMS_URL", defaultValue = "")
  private String smsTargetUri;

  private HttpClient client = HttpClient.newHttpClient();
  private ObjectMapper objectMapper = new ObjectMapper();

  public void sendRequest(String target, SendReq<?> sendRequest) {
    try {
      log.info("url: " + target);
      String requestBody = objectMapper.writeValueAsString(sendRequest);
      log.info("body: " + requestBody);
      HttpRequest request = HttpRequest.newBuilder()
          .uri(URI.create(target))
          .header("Content-Type", MediaType.APPLICATION_JSON)
          .POST(BodyPublishers.ofString(requestBody))
          .build();
      HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

      String responseBody = response.body();
      log.info("response: " + responseBody);
      JsonNode jsonNode = objectMapper.readTree(responseBody);
      boolean success = jsonNode.get("success").asBoolean();
      String message = jsonNode.get("message").asText();

      if (!success) {
        throw new RuntimeException(message);
      }

    } catch (Exception ex) {
      throw new RuntimeException(ex.getMessage());
    }
  }

  public void send(SendEmailReq sendRequest) {
    sendRequest.setIdentificationCode(identificationCode);
    sendRequest(targetUri, sendRequest);
  }

  public void sendSms(SendSMSReq sendRequest) {
    sendRequest.setIdentificationCode(smsCode);
    sendRequest(smsTargetUri, sendRequest);
  }

  public void sendSmsKaKaoTalk(SendKakaoTalkReq sendRequest) {
    sendRequest(kakaoTalkTargetUri, sendRequest);
  }

  public void sendSmsKaKaoTalk4Hotel(SendKakaoTalk4HotelReq sendRequest) {
    sendRequest(kakaoTalkTargetUri, sendRequest);
  }
}