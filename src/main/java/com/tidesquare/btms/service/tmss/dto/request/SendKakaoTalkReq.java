package com.tidesquare.btms.service.tmss.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@RegisterForReflection
public class SendKakaoTalkReq implements SendReq<SendKakaoTalkReq> {

  @JsonProperty(value = "kko_code")
  private String kkoCode;
  @JsonProperty(value = "to_id")
  private String toId;
  @JsonProperty(value = "to_name")
  private String toName;
  @JsonProperty(value = "to_number")
  private String toNumber;

  @JsonProperty(value = "map1")
  private String map1;
  @JsonProperty(value = "map2")
  private String map2;
  @JsonProperty(value = "map3")
  private String map3;
  @JsonProperty(value = "map4")
  private String map4;
  @JsonProperty(value = "map5")
  private String map5;
  @JsonProperty(value = "map6")
  private String map6;
  @JsonProperty(value = "map7")
  private String map7;

  public SendKakaoTalkReq() {
  }

  public SendKakaoTalkReq withTo(String toId, String toName, String toNumber) {
    this.toId = toId;
    this.toName = toName;
    this.toNumber = toNumber;
    return this;
  }

  /*
   * MAP1 AS SITE_NAME 사이트명
   * MAP2 AS RSVN_NUMBER
   * MAP3 AS MEMBER_NAME
   * MAP4 AS RSVN_SCHEDULE
   * MAP5 AS RSVN_PASSENGER
   * MAP6 AS MYPAGE_URL
   */
  public SendKakaoTalkReq withMap(String siteName, String travelId, String userName, String scheduleDate,
      String travelerCount, String mypageUrl, String tl) {
    this.map1 = siteName;
    this.map2 = travelId;
    this.map3 = userName;
    this.map4 = scheduleDate;
    this.map5 = travelerCount;
    this.map6 = mypageUrl;
    this.map7 = tl;
    return this;
  }

  @Override
  public SendKakaoTalkReq setIdentificationCode(String code) {
    this.kkoCode = code;
    return this;
  }

  public String toJsonString() {
    final StringBuilder sb = new StringBuilder();
    sb.append("{ \"kko_code\": \"").append(kkoCode).append('\"');
    sb.append(", \"to_id\": \"").append(toId).append('\"');
    sb.append(", \"to_name\": \"").append(toName).append('\"');
    sb.append(", \"to_number\": \"").append(toNumber).append('\"');

    if (map1 != null) {
      sb.append(", \"map1\": \"").append(map1).append('\"');
    }
    if (map2 != null) {
      sb.append(", \"map2\": \"").append(map2).append('\"');
    }
    if (map3 != null) {
      sb.append(", \"map3\": \"").append(map3).append('\"');
    }
    if (map4 != null) {
      sb.append(", \"map4\": \"").append(map4).append('\"');
    }
    if (map5 != null) {
      sb.append(", \"map5\": \"").append(map5).append('\"');
    }
    if (map6 != null) {
      sb.append(", \"map6\": \"").append(map6).append('\"');
    }
    if (map7 != null) {
      sb.append(", \"map7\": \"").append(map7).append('\"');
    }

    sb.append('}');
    return sb.toString();
  }
}
