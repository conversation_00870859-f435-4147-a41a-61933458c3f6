package com.tidesquare.btms.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tidesquare.btms.entity.City;
import com.tidesquare.btms.repository.CityRepo;
import com.tidesquare.btms.utils.StringUtils;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@Startup
@ApplicationScoped
public class CityService {
    private Map<Long, City> idToCityMap;
    private List<City> cities;

    @Inject
    private CityRepo cityRepo;

    private static CityService instance;

    public CityService() {
        instance = this;
    }

    @PostConstruct
    void init() {
        this.cities = this.cityRepo.findAll();
        this.idToCityMap = new HashMap<>();
        for (City city : cities) {
            idToCityMap.put(city.getId(), city);
        }
    }

    public City findById(Long id) {
        return this.idToCityMap.get(id);
    }

    public static City findByIdStatic(Long id) {
        return instance.findById(id);
    }

    public List<City> findByName(String keyword) {
        return this.cities.stream().filter(city -> !StringUtils.isNullOrEmpty(city.getName()) && city.getName().toUpperCase().contains(keyword.toUpperCase())).toList();
    }
}
