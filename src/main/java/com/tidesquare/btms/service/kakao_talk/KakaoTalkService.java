package com.tidesquare.btms.service.kakao_talk;

import java.util.SortedSet;

import com.tidesquare.btms.constant.TravelStatus;
import com.tidesquare.btms.entity.BookingAirSchedule;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.Customer;
import com.tidesquare.btms.entity.Travel;
import com.tidesquare.btms.entity.Traveler;
import com.tidesquare.btms.entity.User;
import com.tidesquare.btms.repository.CustomerRepo;
import com.tidesquare.btms.service.tmss.TmssService;
import com.tidesquare.btms.service.tmss.dto.request.SendKakaoTalkReq;
import com.tidesquare.btms.utils.DateUtil;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Startup
@ApplicationScoped
public class KakaoTalkService {

    @Inject
    private TmssService tmssService;

    @Inject
    private CustomerRepo customerRepo;

    private static final String SITE_NAME = "트립뷰";

    @PostConstruct
    public void init() {

    }

    public void sendOverseasBookingComplete(Company company, Travel travel, Traveler reserver) {
        if (company.getAirEmSetting().isReservFront()) {
            if (reserver != null && reserver.getTravelerUserId() != null) {
                Customer customer = this.customerRepo.findOne(reserver.getTravelerUserId());
                String code = travel.getBookingAir().getTicketDate() != null ? "tourvis_tripview_kakao_004" : "tourvis_tripview_kakao_005";
                this.sendBookingInfo(code, company, travel, customer);
            }
        }
    }

    public void sendAirApprovalOrReject(Company company, Travel travel, Traveler reserver) {
        Boolean isApproval = travel.getStatus().equals(TravelStatus.Approved);
        if ((isApproval && company.getAirEmSetting().isApprovalFront()) || (!isApproval && company.getAirEmSetting().isRejectFront())) {
            if (reserver != null && reserver.getTravelerUserId() != null) {
                Customer customer = this.customerRepo.findOne(reserver.getTravelerUserId());
                // String code = isApproval ? "tourvis_tripview_kakao_002" : "tourvis_tripview_kakao_003";
                String code = isApproval ? "tourvis_tripview_kakao_002" : "tourvis_tripview_kakao_002"; // always tourvis_tripview_kakao_002, don't know why
                sendBookingInfo(code, company, travel, customer);
            }
        }
    }

    public void sendAirCancel(Company company, Travel travel, Traveler reserver) {
        if (reserver != null && reserver.getTravelerUserId() != null) {
            Customer customer = this.customerRepo.findOne(reserver.getTravelerUserId());
            Boolean isOversease = travel.getIsOverseas();
            String code = isOversease ? "tourvis_tripview_kakao_008" : "tourvis_tripview_kakao_013";
            sendBookingInfo(code, company, travel, customer);
        }
    }

    private void sendBookingInfo(String code, Company company, Travel travel, User user) {
        String travelId = travel.getId().toString();
        String travelerCount = travel.getTravelPersonnel() + "명";
        String mypageUrl = company.getBtmsSetting().getUrl() + "/login";

        SortedSet<BookingAirSchedule> bookingAirSchedules = travel.getBookingAir().getBookingAirSchedules();
        String startDate = DateUtil.date2String(bookingAirSchedules.getFirst().getFromDate(), "yyyy-MM-dd");
        String endDate = DateUtil.date2String(bookingAirSchedules.getLast().getToDate(), "yyyy-MM-dd");

        send(code, user.getLoginId(), user.getName(), user.getCellPhoneNumber(), SITE_NAME, travelId, startDate + "~" + endDate, travelerCount, mypageUrl, null);
    }

    private void send(String code, String loginId, String name, String cellPhoneNumber, String siteName, String travelId, String scheduleDate, String travelerCount, String mypageUrl, String tl) {
        SendKakaoTalkReq sendKakaoTalkRequest = new SendKakaoTalkReq();
        sendKakaoTalkRequest.setIdentificationCode(code)
                .withTo(loginId, name, cellPhoneNumber)
                .withMap(siteName, travelId, name, scheduleDate, travelerCount, mypageUrl, tl);
        try {
            this.tmssService.sendSmsKaKaoTalk(sendKakaoTalkRequest);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }
}
