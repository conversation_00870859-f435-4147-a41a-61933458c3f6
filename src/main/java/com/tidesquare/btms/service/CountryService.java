package com.tidesquare.btms.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tidesquare.btms.entity.Country;
import com.tidesquare.btms.repository.CountryRepo;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@Startup
@ApplicationScoped
public class CountryService {
    private Map<Long, Country> idToCountryMap;

    @Inject
    private CountryRepo countryRepo;

    private static CountryService instance;

    public CountryService() {
        instance = this;
    }

    @PostConstruct
    void init() {
        List<Country> countries = this.countryRepo.findAll();
        this.idToCountryMap = new HashMap<>();
        for (Country country : countries) {
            idToCountryMap.put(country.getId(), country);
        }
    }

    public Country findById(Long id) {
        return this.idToCountryMap.get(id);
    }

    public static Country findByIdStatic(Long id) {
        return instance.findById(id);
    }
}
