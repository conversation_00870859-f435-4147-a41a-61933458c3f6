package com.tidesquare.btms.service.email;

import com.github.jknack.handlebars.Helper;
import com.github.jknack.handlebars.Options;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * Created by rucy on 2015. 8. 5.
 */
@Slf4j
public class PaginationHelper implements Helper<String> {

    public static final Helper<String> INSTANCE = new PaginationHelper();

    public static final String NAME = "pagenation";

    @Override
    public CharSequence apply(final String variableName, final Options options) throws IOException {

        Map<String, Object> paginationInfoMap;

        try {
            int currentPageNumber = options.param(0, 1); // parameter. default 0 (page는 0부터 시작)
            log.debug(">>> currentPageNumber : " + currentPageNumber);
            int totalPageCount = options.param(1, 1); // parameter. default 1
            log.debug(">>> totalPageCount : " + totalPageCount);
            int pageGroupCount = options.param(2, 5); // parameter. default 5. max displayed page count
            log.debug(">>> pageGroupCount : " + pageGroupCount);

            int firstPageIdx = ((currentPageNumber / pageGroupCount)) * pageGroupCount + 1; // 첫번째 index
            int lastPageIdx = ((currentPageNumber / pageGroupCount)) * pageGroupCount + pageGroupCount; // 마지막 index

            int previousIdx = lastPageIdx - pageGroupCount; // 이전 index
            int nextIdx = lastPageIdx + 1; // 다음 index

            boolean canGoPrevious = firstPageIdx > 1 ? true : false; // previous 버튼 active 여부
            boolean canGoNext = totalPageCount > lastPageIdx ? true : false; // next 버튼 active 여부

            int displayedLastPage = totalPageCount < lastPageIdx ? totalPageCount : lastPageIdx;

            paginationInfoMap = this
                    .makePaginationInfoMap(canGoPrevious, canGoNext, currentPageNumber, firstPageIdx, totalPageCount, displayedLastPage, previousIdx,
                            nextIdx);

        } catch (Exception e) {
            log.debug(e.getMessage());
            paginationInfoMap = Maps.newHashMap();
        }

        return options.fn(paginationInfoMap);
    }

    private Map<String, Object> makePaginationInfoMap(boolean canGoPrevious, boolean canGoNext, int page, int firstPage, int lastPage,
            int displayedLastPage,
            int previousIdx, int nextIdx) {

        Map<String, Object> paginationInfoMap = Maps.newHashMap();
        List<Map<String, Object>> pageList = Lists.newArrayList();

        for (int i = firstPage; i <= displayedLastPage; i++) {
            Map<String, Object> numberMap = Maps.newHashMap();
            numberMap.put("page", String.valueOf(i));
            numberMap.put("isCurrent", (i == page + 1 ? true : false)); // page + 1 과 비교
            pageList.add(numberMap);
        }

        paginationInfoMap.put("firstPage", firstPage);
        paginationInfoMap.put("lastPage", lastPage);
        paginationInfoMap.put("currentPageNumber", page);
        paginationInfoMap.put("canGoPrevious", canGoPrevious);
        paginationInfoMap.put("previousIdx", previousIdx);
        paginationInfoMap.put("pages", pageList);
        paginationInfoMap.put("canGoNext", canGoNext);
        paginationInfoMap.put("nextIdx", nextIdx);

        return paginationInfoMap;
    }
}
