package com.tidesquare.btms.service.email;

import com.github.jknack.handlebars.Options;
import com.tidesquare.btms.constant.Constants;
import com.tidesquare.btms.constant.DayOfTheWeek;
import com.tidesquare.btms.utils.CommonUtil;
import com.tidesquare.btms.utils.DateUtil;
import com.tidesquare.btms.utils.StringUtils;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Locale;

/**
 * handlebars Util
 * Created by lkc on 2016-09-06.
 */
@Slf4j
@RegisterForReflection
public class HandlebarsCustomHelpers {

	/**
	 * {{#eq this.name "aaa" }} {{else}} {{/eq}} 2개의 값을 넘겨받아 문자열이 동일한지 비교 <p> 'this' 는 해당 context 에 있는 values
	 *
	 * @return CharSequence
	 */
	public CharSequence eq(final Object a,
			final Object b,
			Options opts) {
		try {
			return String.valueOf(a).equals(String.valueOf(b)) ? opts.fn() : opts.inverse();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return "";
	}

	public CharSequence ne(final Object a,
			final Object b,
			Options opts) {
		try {
			return !String.valueOf(a).equals(String.valueOf(b)) ? opts.fn() : opts.inverse();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return "";
	}

	public CharSequence contains(final Object a,
			final Object b,
			Options opts) {
		try {
			return String.valueOf(a).contains(String.valueOf(b)) ? opts.fn() : opts.inverse();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return "";
	}

	public CharSequence gte(final Integer a,
			final Integer b,
			Options opts) {
		try {
			return a >= b ? opts.fn() : opts.inverse();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return "";
	}

	public CharSequence gt(final Number a,
			final Number b,
			Options opts) {
		try {

			return a.doubleValue() > b.doubleValue() ? opts.fn() : opts.inverse();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return "";
	}

	public CharSequence lte(final Integer a,
			final Integer b,
			Options opts) {
		try {
			return a <= b ? opts.fn() : opts.inverse();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return "";
	}

	/**
	 * {{date ["pattern"]}} 날짜를 포멧에 맞는 형식으로 보여주기
	 *
	 * @return String
	 */
	/*
	public String date(Date date,
					   String pattern) {
	
		if (date == null) {
			return StringUtils.EMPTY;
	
		} else {
			if (pattern == null) {
				pattern = Constants.DATE_FORMAT;
			}
			DateFormat sdFormat = new SimpleDateFormat(pattern, Locale.KOREAN);
			String tempDate = sdFormat.format(date);
	
			return tempDate;
		}
	}
	 */

	/**
	 * {{date ["pattern"]}} 시간을 포멧에 맞는 형식으로 보여주기
	 *
	 * @return String
	 */
	public String date(Object date, String pattern) {
		String tempDate = "";
		if (date == null) {
		} else {
			if (pattern == null)
				pattern = Constants.DATE_FORMAT;
			if (date instanceof Date) {
				DateFormat sdFormat = new SimpleDateFormat(pattern, Locale.KOREA);
				tempDate = sdFormat.format(date);
			} else if (date instanceof LocalDateTime) {
				DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
				tempDate = ((LocalDateTime) date).format(formatter);
			} else if (date instanceof String) {
				try {
					tempDate = (String) date;
					if (tempDate.length() > 7) {
						tempDate = DateUtil.getDateTimeToString(DateUtil.getStringToDate((String) date), pattern);
					}
				} catch (ParseException e) {
					e.printStackTrace();
				}
			}
		}
		return tempDate;
	}

	public String timeSelected(int value, Date date, String timeType) {

		if (null == date) {
			return "";
		}
		String result = "";
		String compareValue = value < 10 ? "0" + value : value + "";
		DateFormat sdFormat = new SimpleDateFormat(timeType);
		String time = sdFormat.format(date);

		if (compareValue.equals(time)) {
			result = "selected";
		}

		return result;
	}

	/**
	 * {{now ["pattern"]}} 현재 시간을 포멧에 맞는 형식으로 보여주기
	 *
	 * @return String
	 */
	public String now(String pattern) {

		if (pattern == null) {
			pattern = Constants.DATE_FORMAT;
		}

		return date(new Date(), pattern);
	}

	/**
	 * SelectBox의 value와 compareValue의 값이 일치하는지 체크
	 *
	 * @return String
	 */
	public String selected(String value, String compareValue) {

		String result = "";

		if (!StringUtils.isNullOrBlank(value)) {

			boolean isNumber = StringUtils.isNumeric(compareValue);

			if (isNumber) {

				int intVal = Integer.parseInt(compareValue);

				compareValue = Integer.toString(intVal);
			}
			if (value.equals(compareValue)) {
				result = "selected";
			}
		}
		return result;
	}

	/**
	 * YYYYMMDD 형태의 날짜를 pattern으로 구분해서 반환
	 *
	 * @return String
	 */
	public String stringDateFormat(String ymd, String pattern, String type) {
		String result = "";

		if (!StringUtils.isNullOrBlank(ymd)) {
			ymd = ymd.replaceAll("-", "");

			if (ymd.length() > 5) {
				if ("ymd".equals(type)) {
					result += ymd.substring(0, 4) + pattern;
				}

				result += ymd.substring(4, 6) + pattern;
				result += ymd.substring(6, 8);
			}
		}

		return result;
	}

	/**
	 * YYYYMMDD 형태의 날짜를 MM월 DD일으로 구분해서 반환
	 *
	 * @return String
	 */
	public String StringToMMDD(String ymd) {
		String result = "";

		if (!StringUtils.isNullOrBlank(ymd)) {
			if ("0".equals(ymd.substring(4, 5))) {
				result += ymd.substring(5, 6);
			} else {
				result += ymd.substring(4, 6);
			}
			result += "월 ";
			if ("0".equals(ymd.substring(6, 7))) {
				result += ymd.substring(7, 8);
			} else {
				result += ymd.substring(6, 8);
			}
			result += "일";
		}

		return result;
	}

	/**
	 * 호텔 현재 페이지 정보를 반환
	 *
	 * @return String
	 */
	/*public String currentPage(int index) {
	
	    return String.valueOf((int) Math.ceil((double) (index + 1) / (double) 10));
	}*/

	/**
	 * 초기화면 1페이지 이상은 화면에 보여주지 않도록 한다.
	 *
	 * @return String
	 */
	/*public String pagingDisplay(int index) {
	
	    String result = "";
	
	    int checkCurrentPage = (int) (Math.ceil((double) (index + 1) / (double) 10));
	
	    if (checkCurrentPage > 1) {
	        result = "display:none;";
	    }
	
	    return result;
	}*/
	public String nvl(String value, String defaultValue) {

		if (StringUtils.isNullOrBlank(value) || "NaN".equals(value)) {
			return defaultValue;
		}

		return value;
	}

	/**
	 * {{pagination ["options"]}} 페이징
	 *
	 * @return CharSequence
	 */
	public CharSequence pagination(Options options) throws IOException {

		PaginationHelper paginationHelper = new PaginationHelper();

		return paginationHelper.apply(PaginationHelper.NAME, options);
	}

	/***
	 * {{comma ["num"]}} 천 단위 숫자에 콤마 추가
	 */
	public CharSequence comma(Object num) {

		try {
			if (num instanceof String) {
				return CommonUtil.moneyFormat((String) num);

			} else {
				return CommonUtil.moneyFormat(String.valueOf(num));
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return "";
	}

	/**
	 * 금액을 인원수로 나누어 원화 형태로 반환
	 *
	 * @param value
	 * @param denominator
	 * @return CharSequence
	 */
	public CharSequence divisionComma(Double value, Integer denominator) {
		try {
			double result = value / denominator;
			return comma(result);
		} catch (Exception e) {
			log.error("An error occurred when calculating the amount (divisionComma).", e);
			throw e;
		}
	}

	public CharSequence divisionCommaObject(Object value, Integer denominator) {
		try {
			double result = Double.parseDouble(String.valueOf(value)) / denominator;
			return comma(result);
		} catch (Exception e) {
			log.error("An error occurred when calculating the amount (divisionComma).", e);
			throw e;
		}
	}

	/**
	 * 소수점을 제거
	 *
	 * @return String
	 */
	public String cutDecimalPoint(String value) {

		if (StringUtils.isNullOrBlank(value)) {
			return "";
		}

		return String.valueOf((int) Math.floor(Double.parseDouble(value)));
	}

	/**
	 * 소수점을 제거
	 *
	 * @return String
	 */
	public String doubleToInteger(Object value) {

		if (null == value) {
			return "";
		}
		return String.valueOf((int) Math.floor((Double) value));
	}

	public Integer objectToInteger(Object value) {

		if (value instanceof Integer) {
			return (int) value;

		} else if (value instanceof Double) {
			return (int) Math.floor((Double) value);

		} else if (value instanceof Float) {
			return (int) Math.floor((Float) value);

		} else if (value instanceof BigDecimal) {
			return ((BigDecimal) value).intValue();

		} else if (value instanceof String) {
			return Integer.parseInt((String) value);

		} else {
			return 0;
		}
	}

	/**
	 * 룸타입에 해당하는 룸타입명을 반환
	 *
	 * @param roomType
	 * @return String
	 */
	public String roomTypeName(String roomType) {

		if (Constants.ROOM_TYPE_IS_SINGLE.equals(roomType)) {
			return Constants.ROOM_TYPE_IS_SINGLE_NAME;

		} else if (Constants.ROOM_TYPE_IS_DOUBLE.equals(roomType)) {
			return Constants.ROOM_TYPE_IS_DOUBLE_NAME;

		} else if (Constants.ROOM_TYPE_IS_TWIN.equals(roomType)) {
			return Constants.ROOM_TYPE_IS_TWIN_NAME;

		} else if (Constants.ROOM_TYPE_IS_TRIPLE.equals(roomType)) {
			return Constants.ROOM_TYPE_IS_TRIPLE_NAME;
		}

		return "";
	}

	public String forEach(int from, int to, int interval, Options options) {

		String result = "";

		try {
			for (int i = from; i < to + 1; i += interval) {
				result += options.fn(i);
			}
		} catch (IOException e) {
			e.printStackTrace();
		}

		return result;
	}

	public String forEach4Object(Object from, Object to, int interval, Options options) {
		return forEach(objectToInteger(from), objectToInteger(to), interval, options);
	}

	public String dayOfWeekNameByDate(Date date) {

		if (date == null) {
			return "-";

		} else {
			try {
				DateFormat sdFormat = new SimpleDateFormat(Constants.DATE_NODASH_FORMAT);
				String ymd = sdFormat.format(date);

				return this.getDayOfWeekName(DateUtil.whichDay(ymd));

			} catch (java.text.ParseException e) {
				return "";
			}
		}
	}

	/**
	 * 요일명을 반환
	 *
	 * @return String
	 */
	public String dayOfWeekName(String ymd) {

		if (StringUtils.isNullOrBlank(ymd)) {
			return "";

		} else {
			try {
				return this.getDayOfWeekName(DateUtil.whichDay(ymd.replaceAll("-", "")));

			} catch (java.text.ParseException e) {
				return "";
			}
		}
	}

	public String getDayOfWeekName(int dayOfWeek) {

		switch (dayOfWeek) {
		case 0:
			return DayOfTheWeek.Sunday.getValue();
		case 1:
			return DayOfTheWeek.Monday.getValue();
		case 2:
			return DayOfTheWeek.Tuesday.getValue();
		case 3:
			return DayOfTheWeek.Wednesday.getValue();
		case 4:
			return DayOfTheWeek.Thursday.getValue();
		case 5:
			return DayOfTheWeek.Friday.getValue();
		case 6:
			return DayOfTheWeek.Saturday.getValue();
		default:
			return "";
		}
	}

	public String plus(int value, int plusValue) {

		return String.valueOf(value + plusValue);
	}

	public String plusDouble(Double value, Double plusValue) {

		if (value == null) {
			value = Double.valueOf(0);
		}
		if (plusValue == null) {
			plusValue = Double.valueOf(0);
		}

		return String.valueOf(value.intValue() + plusValue.intValue());
	}

	public String plusObject(Object value, Object plusValue) {

		int plusVal = objectToInteger(value) + objectToInteger(plusValue);

		return String.valueOf(plusVal);
	}

	public CharSequence plusDoubleComma(Object value, Object plusVal) {

		int plusValue = objectToInteger(value) + objectToInteger(plusVal);

		return comma(plusValue);
	}

	public CharSequence plusComma(Object value, Object plusVal) {

		int plusValue = objectToInteger(value) + objectToInteger(plusVal);

		return comma(plusValue);
	}

	public String multiply(Object value,
			Object multiplyValue) {

		return String.valueOf((this.objectToInteger(value) * this.objectToInteger(multiplyValue)));
	}

	public String minus(Object value, Object minusValue) {

		int val = this.objectToInteger(value);
		int minusVal = this.objectToInteger(minusValue);

		return String.valueOf(val - minusVal);
	}

	/**
	 * 두 금액을 뺄셈하여 원화 형태로 반환
	 *
	 * @param value
	 * @param minusValue
	 * @return CharSequence
	 */
	public CharSequence minusComma(Object value, Object minusValue) {

		return comma(Integer.parseInt(cutDecimalPoint(String.valueOf(value))) - Integer
				.parseInt(cutDecimalPoint(String.valueOf(minusValue))));
	}

	/**
	 * 소수점 반올림 하여 원화 형태로 반화
	 *
	 * @param value
	 * @return CharSequence
	 */
	public CharSequence roundComma(Object value) {
		if (value == null) {
			return "0";
		}
		return comma(Math.round(Double.parseDouble(String.valueOf(value))));
	}

	/**
	 * 소수점 반올림
	 *
	 * @param value
	 * @return CharSequence
	 */
	public String roundDouble(Object value) {

		return String.valueOf(Math.round(Double.parseDouble(String.valueOf(value))));
	}

	/**
	 * '*' 로 변경
	 * ex) "Asterisk", 1, 3 => A***risk
	 *
	 * @param originStr  the origin str
	 * @param startIndex the start index
	 * @param endIndex   the end index
	 * @return the string
	 */
	public String asteriskFormat(String originStr, int startIndex, int endIndex) {

		StringBuffer sb = new StringBuffer();
		sb.append(originStr);

		StringBuffer convertSb = new StringBuffer();
		for (int i = startIndex; i < originStr.length(); i++) {
			convertSb.append("*");
		}

		sb = sb.replace(startIndex, endIndex, convertSb.toString());

		return sb.toString();
	}

	/**
	 * 카드번호 가운데 8자리 '*' 처리
	 * ex) 1111222233334444 => 1111-****-****-4444
	 *
	 * @param cardNum the card num
	 * @return the string
	 */
	public String cardNumberAsterisk(String cardNum) {

		int len = cardNum.length();

		StringBuilder sb = new StringBuilder();

		if (len == 16) {
			sb.append(cardNum.substring(0, 4));
			sb.append("-").append("****");
			sb.append("-").append("****");
			sb.append("-").append(cardNum.substring(12));

		} else {
			sb.append(len >= 4 ? cardNum.substring(0, 4) : cardNum.substring(0, len));
			sb.append("-").append("****");
			sb.append("-").append("****");
			sb.append("-").append("****");
		}
		return sb.toString();
	}

	/**
	 * 핸드폰번호 '-' 처리
	 * ex) 01012345678 => 010-1234-5678
	 *
	 * @param cellphoneNumber cellphoneNumber
	 * @param pattern         '-', '.', ...
	 * @return the string
	 */
	public String cellphoneNumberFormat(String cellphoneNumber,
			String pattern) {

		if (StringUtils.isNullOrBlank(cellphoneNumber)) {
			return "";
		} else if (cellphoneNumber.length() < 7) {
			return "";
		}

		int len = cellphoneNumber.length();

		StringBuilder sb = new StringBuilder();
		if (len == 11) {
			sb.append(cellphoneNumber.substring(0, 3));
			sb.append(pattern);
			sb.append(cellphoneNumber.substring(3, 7));
			sb.append(pattern);
			sb.append(cellphoneNumber.substring(7));

		} else {
			sb.append(cellphoneNumber.substring(0, 3));
			sb.append(pattern);
			sb.append(cellphoneNumber.substring(3, 6));
			sb.append(pattern);
			sb.append(cellphoneNumber.substring(6));
		}
		return sb.toString();
	}

	/**
	 * 핸드폰번호 맨앞 3자리 비교
	 *
	 * @param cellphoneNumber
	 * @param compareString
	 * @return CharSequence
	 */
	public CharSequence cellPhoneNumberEq(String cellphoneNumber, String compareString, Options opts) {
		try {
			if (!StringUtils.isNullOrBlank(cellphoneNumber) && cellphoneNumber.length() > 2) {
				return String.valueOf(cellphoneNumber.substring(0, 3)).equals(String.valueOf(compareString)) ? opts
						.fn() : opts.inverse();
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		return "";
	}

	/**
	 * 핸드폰번호를 index(0,1,2) 에 따라 반환한다.
	 * ex) 01012345678 => 1234
	 *
	 * @param cellphoneNumber cellphoneNumber
	 * @param index           0, 1, 2
	 * @return the string
	 */
	public String cellphoneNumberCut(String cellphoneNumber, int index) {
		if (StringUtils.isNullOrBlank(cellphoneNumber)) {
			return "";
		} else if (cellphoneNumber.length() < 10) {
			return "";
		}

		int len = cellphoneNumber.length();
		String firstNumber = "";
		String secondNumber = "";
		String thirdNumber = "";

		if (len == 11) {
			firstNumber = cellphoneNumber.substring(0, 3);
			secondNumber = cellphoneNumber.substring(3, 7);
			thirdNumber = cellphoneNumber.substring(7);

		} else {
			firstNumber = cellphoneNumber.substring(0, 3);
			secondNumber = cellphoneNumber.substring(3, 6);
			thirdNumber = cellphoneNumber.substring(6);
		}

		if (index == 0) {
			return firstNumber;

		} else if (index == 1) {
			return secondNumber;

		} else {
			return thirdNumber;
		}
	}

	/**
	 * String substring
	 * ex) string, 0, 2 => st
	 *
	 * @param originStr
	 * @param startIdx
	 * @param endIdx    else -1 == originStr.substring(startIdx)
	 * @return the string
	 */
	public String trimString(String originStr, int startIdx, int endIdx) {

		String trimString = "";

		if (null != originStr) {
			int strLen = originStr.length();

			if (endIdx == -1) {
				trimString = originStr.substring(startIdx);

			} else {
				if (strLen >= startIdx && strLen >= endIdx) {
					trimString = originStr.substring(startIdx, endIdx);
				}
			}
		}
		return trimString;
	}

	/**
	 * String substring : 0 부터 indexOf(compareStr)
	 * ex) abc.kr, "." => abc
	 *
	 * @param originStr
	 * @param compareStr
	 * @return the string
	 */
	public String substring4IndexOf(String originStr, String compareStr) {

		String trimString = "";

		if (null != originStr) {
			if (originStr.indexOf(compareStr) > -1) {
				trimString = originStr.substring(0, originStr.indexOf(compareStr));

			} else {
				trimString = originStr;
			}
		}
		return trimString;
	}

	/**
	 * Type 별 originValue.substring 비교 후 selected
	 * ex) "2016", "20161101", "Y" => selected
	 *
	 * @param value       비교 값
	 * @param originValue 전체 값 (substring 하기 전)
	 * @param type        'Y', 'M', 'D', 'CELLPHONE', 'PHONE'
	 * @return the string
	 */
	public String selectedByType(Object value, String originValue, String type) {

		String result = "";
		String compareValue = null;

		if (null != value && !StringUtils.isNullOrBlank(originValue)) {

			if ("Y".equals(type) && originValue.length() == 8) {
				compareValue = originValue.substring(0, 4);

			} else if ("M".equals(type) && originValue.length() == 8) {
				compareValue = originValue.substring(4, 6);

				int intVal = Integer.parseInt(compareValue);
				compareValue = Integer.toString(intVal);

			} else if ("D".equals(type) && originValue.length() == 8) {
				compareValue = originValue.substring(6, 8);

				int intVal = Integer.parseInt(compareValue);
				compareValue = Integer.toString(intVal);

			} else if ("CELLPHONE".equals(type) && originValue.length() > 9) {
				compareValue = originValue.substring(0, 3);

			} else if ("PHONE".equals(type) && originValue.length() > 9) {
				compareValue = originValue.substring(0, 3);

			}
			if (String.valueOf(value).equals(compareValue)) {
				result = "selected";

			} else {
				if ("PHONE".equals(type)) {
					compareValue = originValue.substring(0, 2);

					if (String.valueOf(value).equals(compareValue)) {
						result = "selected";
					}
				}
			}
		}
		return result;
	}

	/**
	 * 국번별 처음, 중간, 끝 전화번호 return
	 * ex) "0212344567", "FIRST" => "02"
	 *
	 * @param value    전체 값 (substring 하기 전)
	 * @param position 'FIRST', 'MIDDLE', 'END', 'ALL'
	 * @return the string
	 */
	public String returnPhoneNumber(String value, String position) {

		if (StringUtils.isNullOrBlank(value)) {
			return "";
		}

		String first = value.substring(0, 2);
		String middle = null;
		String end = null;

		if (value.length() < 7) {

			if ("FIRST".equals(position)) {
				if ("02".equals(first)) {
					return "02";
				} else {
					return value.substring(0, 3);
				}
			} else if ("MIDDLE".equals(position)) {
				if ("02".equals(first)) {
					return value.substring(2, value.length());
				} else {
					return value.substring(3, value.length());
				}
			} else if ("END".equals(position)) {
				return "";

			} else if ("ALL".equals(position)) {
				return value;
			}
		}
		int endStartIdx = value.length() - 4;

		if ("02".equals(first)) {
			middle = value.substring(2, endStartIdx);

		} else {
			first = value.substring(0, 3);
			middle = value.substring(3, endStartIdx);

		}
		end = value.substring(endStartIdx);

		if ("FIRST".equals(position)) {
			return first;

		} else if ("MIDDLE".equals(position)) {
			return middle;

		} else if ("END".equals(position)) {
			return end;

		} else if ("ALL".equals(position)) {
			return first + "-" + middle + "-" + end;

		} else {
			return "";
		}
	}

	public CharSequence startTrTag(int checkIndex, int devideIndex, Options options) {

		try {
			if (checkIndex % devideIndex == 0) {
				return options.fn();
			}
			return options.inverse();

		} catch (IOException e) {
			e.printStackTrace();
		}
		return "";
	}

	public CharSequence endTrTag(int checkIndex, int devideIndex, Options options) {

		try {
			if (checkIndex > 0 && checkIndex % devideIndex == 0) {
				return options.fn();
			}

			return options.inverse();

		} catch (IOException e) {
			e.printStackTrace();
		}
		return "";
	}

	public CharSequence time(String time) {

		if (!StringUtils.isNullOrBlank(time) && time.length() > 3) {
			return time.substring(0, 2) + ":" + time.substring(2, 4);
		}

		return "";
	}

	public String carriageReturn(String contents) {

		if (!StringUtils.isNullOrBlank(contents)) {
			return contents.replaceAll("(\\r\\n|\\r|\\n|\\n\\r)", "<br>");
		}

		return "";
	}

	/**
	 * 예약한 전체 호텔룸 개수
	 *
	 * @param singleRoomCount
	 * @param doubleRoomCount
	 * @param twinRoomCount
	 * @param tripleRoomCount
	 * @return String
	 */
	public String hotelTotalRoomCount(int singleRoomCount, int doubleRoomCount, int twinRoomCount, int tripleRoomCount) {

		return String.valueOf(singleRoomCount + doubleRoomCount + twinRoomCount + tripleRoomCount);
	}

	public CharSequence inc(Integer index) {
		if (index == null)
			return "1";
		return String.valueOf(index + 1);
	}

	public String repeatAppendString(int index,
			int j,
			String str) {

		if ((index + 1) % j == 0) {
			return String.valueOf(str);
		}

		return null;
	}

	/**
	 * string yyyy-mm-dd로 형변환
	 * @param ymd
	 * @param pattern
	 * @param type
	 * @return
	 */
	public String stringConvertFormat(String ymd, String pattern, String type) {
		String result = "";

		if (!StringUtils.isNullOrBlank(ymd)) {

			if (ymd.length() > 5) {
				if ("ymd".equals(type)) {
					result += ymd.substring(0, 4) + pattern;
				}

				result += ymd.substring(4, 6) + pattern;
				result += ymd.substring(6, 8);
			}
		}

		return result;
	}

	/**
	 * 회사전화번호 맨앞 2~3자리 비교
	 * @param phoneNumber
	 * @param compareString
	 * @param opts
	 * @return
	 */
	public CharSequence phoneNumberEq(String phoneNumber, String compareString, Options opts) {
		try {
			if (!StringUtils.isNullOrBlank(phoneNumber) && phoneNumber.length() > 2) {
				return String.valueOf(phoneNumber.substring(0, 3)).equals(String.valueOf(compareString)) ? opts
						.fn() : opts.inverse();
			} else if (!StringUtils.isNullOrBlank(phoneNumber) && phoneNumber.length() == 2) {
				return String.valueOf(phoneNumber.substring(0, 2)).equals(String.valueOf(compareString)) ? opts
						.fn() : opts.inverse();
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
		return "";
	}

	public String tlDate(Date bookingDate, Date createDate, String pattern) {

		Date tlDate = new Date((bookingDate != null ? bookingDate : createDate).getTime() + 3l * 1000 * 60 * 60 * 24);

		return this.date(tlDate, pattern);
	}

	/**
	 * 양수에 + 기호 추가
	 * @param number
	 * @return
	 */
	public String positiveSign(int number) {

		if (number > 0) {
			return "+" + number;
		}

		return number + "";
	}

	public String koreanLastName(String name) {

		if (StringUtils.isNullOrBlank(name)) {
			return "";
		}
		return name.substring(0, 1);
	}

	public String koreanFirstName(String name) {

		if (StringUtils.isNullOrBlank(name)) {
			return "";
		}
		return name.substring(1);
	}

	public String inequalitySign(int value) {

		if (value > 0) {
			return "+";

		} else if (value < 0) {
			return "-";

		} else {
			return "";
		}
	}

	public String collectNumberYmd(String collectNumber, String pattern) {

		if (StringUtils.isNullOrBlank(pattern)) {
			pattern = "-";
		}
		if (!StringUtils.isNullOrBlank(collectNumber) && collectNumber.length() > 9) {
			return CommonUtil.stringDateFormat(collectNumber.substring(2, 10), pattern);
		}
		/*if (!StringUtils.isNullOrBlank(collectNumber) && collectNumber.length() > 9 && collectNumber.indexOf("AC") > -1) {
			return CommonUtil.stringDateFormat(collectNumber.replaceAll("AC", "").substring(0, 8), pattern);
		}*/

		return "";
	}

	public String unescapeHtml(String inputData) {
		if (StringUtils.isNullOrBlank(inputData)) {
			return "";
		}
		inputData = inputData.replaceAll("&amp;", "&");
		inputData = inputData.replaceAll("&#39;", "\'");
		inputData = inputData.replaceAll("&quot;", "\"");
		inputData = inputData.replaceAll("&gt;", ">");
		inputData = inputData.replaceAll("&lt;", "<");

		return inputData;
	}

	public CharSequence timeLimit(String timeLimit,
			Options opts) {
		try {
			LocalDateTime now = LocalDateTime.now();
			String formatedNow = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
			String finalTimeLimit = timeLimit + "00";
			Long longNow = Long.parseLong(formatedNow);
			Long longTimeLimit = Long.parseLong(finalTimeLimit);
			if (longNow < longTimeLimit) {
				return opts.fn();
			}
			return opts.inverse();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return "";
	}

	public CharSequence timeLimitTL(String timeLimit,
			Options opts) {
		try {
			LocalDateTime now = LocalDateTime.now();
			String formatedNow = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmm"));
			//			String finalTimeLimit = timeLimit + "1700";
			Long longNow = Long.parseLong(formatedNow);
			Long longTimeLimit = Long.parseLong(timeLimit);
			if (longNow < longTimeLimit) {
				return opts.fn();
			}
			return opts.inverse();
		} catch (IOException e) {
			e.printStackTrace();
		}
		return "";
	}

	public Boolean voidLimitCarc(String ticketDate) {

		DateTimeFormatter formatter = DateTimeFormatter.ISO_LOCAL_DATE_TIME;
		LocalDateTime ticketDateTime = LocalDateTime.parse(ticketDate, formatter);
		LocalDateTime now = LocalDateTime.now();
		LocalDateTime voidLimitTime = LocalDateTime.of(LocalDate.of(ticketDateTime.getYear(), ticketDateTime.getMonth(), ticketDateTime.getDayOfMonth()), LocalTime.of(23, 50));

		if (now.isBefore(voidLimitTime)) {
			return true;
		} else {
			return false;
		}
	}

	public Double divide(Double dividend, Double divisor) {
		return dividend / divisor;
	}
}