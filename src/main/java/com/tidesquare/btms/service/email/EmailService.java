package com.tidesquare.btms.service.email;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.SortedSet;

import org.eclipse.microprofile.config.inject.ConfigProperty;

import com.github.jknack.handlebars.Handlebars;
import com.github.jknack.handlebars.Template;
import com.google.common.collect.ImmutableMap;
import com.tidesquare.btms.constant.BookingAdminApprovalType;
import com.tidesquare.btms.constant.BookingType;
import com.tidesquare.btms.constant.ManagerType;
import com.tidesquare.btms.constant.Role;
import com.tidesquare.btms.constant.TravelStatus;
import com.tidesquare.btms.entity.AttachFile;
import com.tidesquare.btms.entity.BookingAir;
import com.tidesquare.btms.entity.BookingAirSchedule;
import com.tidesquare.btms.entity.BookingAirTraveler;
import com.tidesquare.btms.entity.BtmsManager;
import com.tidesquare.btms.entity.Company;
import com.tidesquare.btms.entity.ContactRequest;
import com.tidesquare.btms.entity.Customer;
import com.tidesquare.btms.entity.DocumentNumber;
import com.tidesquare.btms.entity.IdentificationRequest;
import com.tidesquare.btms.entity.Travel;
import com.tidesquare.btms.entity.TravelAgencyUser;
import com.tidesquare.btms.entity.Traveler;
import com.tidesquare.btms.entity.User;
import com.tidesquare.btms.repository.BookingAirAttachFileRepo;
import com.tidesquare.btms.repository.BtmsManagerRepo;
import com.tidesquare.btms.repository.CustomerRepo;
import com.tidesquare.btms.repository.DocumentNumberRepo;
import com.tidesquare.btms.service.tmss.TmssService;
import com.tidesquare.btms.service.tmss.dto.request.SendEmailReq;

import io.quarkus.runtime.Startup;
import jakarta.annotation.PostConstruct;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Startup
@ApplicationScoped
public class EmailService {

    @ConfigProperty(name = "BTMS_AGENCY_DOMAIN", defaultValue = "")
    String btmsAgencyDomain;

    @ConfigProperty(name = "BTMS_IMG_DOMAIN", defaultValue = "")
    String btmsImgDomain;

    @Inject
    private DocumentNumberRepo documentNumberRepo;

    @Inject
    private BookingAirAttachFileRepo bookingAirAttachFileRepo;

    @Inject
    private CustomerRepo customerRepo;

    @Inject
    private BtmsManagerRepo btmsManagerRepo;

    @Inject
    private TmssService tmssService;

    @ConfigProperty(name = "CORPORATE_EMAIL", defaultValue = "<EMAIL>")
    private String corporateEmail;

    private Handlebars handlebars = new Handlebars();

    private Map<String, String> templateCacheMap = new HashMap<>();

    @PostConstruct
    public void init() {
        this.handlebars.registerHelpers(new HandlebarsCustomHelpers());
    }

    public void sendBookingAirEmail(Company company, Travel travel, Traveler reserver, boolean isRequest) {
        BookingAir bookingAir = travel.getBookingAir();
        List<DocumentNumber> documentNumbers = this.documentNumberRepo.findByBookingIdAndBookingType(travel.getId(), BookingType.AIR);
        List<AttachFile> attachFiles = this.bookingAirAttachFileRepo.getAttachFilesByBookingAirId(bookingAir.getId());

        if (company.getAirEmSetting().isReservFront()) {
            if (reserver != null && reserver.getTravelerUserId() != null) {
                User user = new User(reserver.getTravelerUserId());
                user.setEmail(reserver.getEmail());
                user.setName(reserver.getName());
                user.setRole(Role.ROLE_CUSTOMER);
                this.sendBookingAirEmail(company, travel, reserver, documentNumbers, attachFiles, user, false, false);
            }
        }
        if (company.getAirEmSetting().isReservAdmin()) {
            if (travel.getWorkspaceId() != null) {
                List<Customer> admins = this.customerRepo.findActiveAdminByWorkspaceId(travel.getWorkspaceId());
                for (Customer admin : admins) {
                    this.sendBookingAirEmail(company, travel, reserver, documentNumbers, attachFiles, admin, false, true);
                }
            }
        }
        if (company.getAirEmSetting().isReservAgency()) {
            List<BtmsManager> btmsManagers = this.btmsManagerRepo.findFetchTravelAgencyUser(company.getId(), true, null);
            Set<Long> sentUserIds = new HashSet<>();
            for (BtmsManager btmsManager : btmsManagers) {
                if (btmsManager.getManagerType() != ManagerType.Air && btmsManager.getManagerType() != ManagerType.System_Etc) {
                    continue;
                }
                TravelAgencyUser travelAgencyUser = btmsManager.getTravelAgencyUser();
                if (sentUserIds.contains(travelAgencyUser.getId())) {
                    continue;
                }
                this.sendBookingAirEmail(company, travel, reserver, documentNumbers, attachFiles, travelAgencyUser, false, true);
                sentUserIds.add(travelAgencyUser.getId());
            }
        }
    }

    private void sendBookingAirEmail(Company company, Travel travel, Traveler reserver, List<DocumentNumber> documentNumbers, List<AttachFile> attachFiles, User user, boolean isRequest, boolean isAdmin) {
        BookingAir bookingAir = travel.getBookingAir();
        SortedSet<BookingAirTraveler> bookingAirTravelers = bookingAir.getBookingAirTravelers();
        Map<Integer, List<BookingAirSchedule>> bookingAirScheduleMap = bookingAir.getBookingAirScheduleMap();

        BookingAdminApprovalType bookingAdminApprovalType = company.getBtmsSetting().getBookingAdminApprovalType();
        if (bookingAdminApprovalType == null) {
            bookingAdminApprovalType = BookingAdminApprovalType.NotUsed;
        }

        double fareAmount = 0d;
        double taxAmount = 0d;
        double commissionAmount = 0d;
        for (BookingAirTraveler traveler : bookingAirTravelers) {
            fareAmount += traveler.getFareAmount();
            taxAmount += traveler.getTax();
            commissionAmount += traveler.getCommissionAmount();
        }

        double totalAmount = fareAmount + taxAmount + commissionAmount;
        String title = isRequest ? "발권" : "예약";
        String btmsUrl = company.getParent() != null ? company.getParent().getBtmsSetting().getUrl() : company.getBtmsSetting().getUrl();

        SendEmailReq sendEmailReq = new SendEmailReq();
        sendEmailReq.setToEmail(user.getEmail());
        sendEmailReq.setToName(user.getName());
        sendEmailReq.setToId(user.getId().toString());
        sendEmailReq.setSubject("[출장예약시스템 BTMS] " + user.getName() + "님, 항공권 " + title + "이 요청되었습니다.");
        sendEmailReq.setContent(this.renderTemplate(company, "mail/air/overseasRequest", new ImmutableMap.Builder<String, Object>()
                .put("company", company)
                .put("travel", travel)
                .put("name", user.getName())
                .put("reserverName", reserver != null ? reserver.getName() : "")
                .put("personCnt", Math.max(travel.getTravelPersonnel() - 1, 0))
                .put("btmsUrl", btmsUrl)
                .put("bookingAdminApprovalType", bookingAdminApprovalType)
                .put("agencyUrl", this.btmsAgencyDomain)
                .put("imgUrl", this.btmsImgDomain)
                .put("userType", user.getRole())
                .put("bookingAirScheduleMap", bookingAirScheduleMap)
                .put("bookingAirScheduleMapSize", bookingAirScheduleMap.size())
                .put("totalAmount", totalAmount)
                .put("fareAmount", fareAmount)
                .put("taxAmount", taxAmount)
                .put("commissionAmount", commissionAmount)
                .put("isRequest", isRequest)
                .put("isAdmin", isAdmin)
                .put("documentNumberList", documentNumbers)
                .put("attachFileList", attachFiles)
                .build()));

        try {
            this.tmssService.send(sendEmailReq);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    public void sendAirApprovalOrRejectEmail(Company company, Travel travel, Traveler reserver) {
        Boolean isApproval = travel.getStatus().equals(TravelStatus.Approved);
        if ((isApproval && company.getAirEmSetting().isApprovalFront()) || (!isApproval && company.getAirEmSetting().isRejectFront())) {
            if (reserver != null && reserver.getTravelerUserId() != null) {
                User user = new User(reserver.getTravelerUserId());
                user.setEmail(reserver.getEmail());
                user.setName(reserver.getName());
                user.setRole(Role.ROLE_CUSTOMER);
                this.sendAirApprovalOrRejectEmail(company, travel, reserver, user, false);
            }
        }
        if ((isApproval && company.getAirEmSetting().isApprovalAdmin()) || (!isApproval && company.getAirEmSetting().isRejectAdmin())) {
            if (travel.getWorkspaceId() != null) {
                List<Customer> admins = this.customerRepo.findActiveAdminByWorkspaceId(travel.getWorkspaceId());
                for (Customer admin : admins) {
                    this.sendAirApprovalOrRejectEmail(company, travel, reserver, admin, true);
                }
            }
        }
        if ((isApproval && company.getAirEmSetting().isApprovalAgency()) || (!isApproval && company.getAirEmSetting().isRejectAgency())) {
            List<BtmsManager> btmsManagers = this.btmsManagerRepo.findFetchTravelAgencyUser(company.getId(), true, null);
            Set<Long> sentUserIds = new HashSet<>();
            for (BtmsManager btmsManager : btmsManagers) {
                if (btmsManager.getManagerType() != ManagerType.Air) {
                    continue;
                }
                TravelAgencyUser travelAgencyUser = btmsManager.getTravelAgencyUser();
                if (sentUserIds.contains(travelAgencyUser.getId())) {
                    continue;
                }
                this.sendAirApprovalOrRejectEmail(company, travel, reserver, travelAgencyUser, true);
                sentUserIds.add(travelAgencyUser.getId());
            }
        }
    }

    private void sendAirApprovalOrRejectEmail(Company company, Travel travel, Traveler reserver, User user, boolean isAdmin) {
        Boolean isApproval = travel.getStatus().equals(TravelStatus.Approved);
        BookingAir bookingAir = travel.getBookingAir();
        Map<Integer, List<BookingAirSchedule>> bookingAirScheduleMap = bookingAir.getBookingAirScheduleMap();
        String btmsUrl = company.getParent() != null ? company.getParent().getBtmsSetting().getUrl() : company.getBtmsSetting().getUrl();

        SendEmailReq sendEmailReq = new SendEmailReq();
        sendEmailReq.setToEmail(user.getEmail());
        sendEmailReq.setToName(user.getName());
        sendEmailReq.setToId(user.getId().toString());
        sendEmailReq.setSubject("[출장예약시스템 BTMS] " + user.getName() + "님이 요청하신 항공 예약이 " + (isApproval ? "승인" : "반려") + "되었습니다.");
        sendEmailReq.setContent(this.renderTemplate(company, "mail/air/requestApproval", new ImmutableMap.Builder<String, Object>()
                .put("travel", travel)
                .put("name", user.getName())
                .put("reserverName", reserver.getName())
                .put("personCnt", Math.max(travel.getTravelPersonnel() - 1, 0))
                .put("btmsUrl", btmsUrl)
                .put("agencyUrl", this.btmsAgencyDomain)
                .put("imgUrl", this.btmsImgDomain)
                .put("isApproval", isApproval)
                .put("userType", user.getRole())
                .put("bookingAirScheduleMap", bookingAirScheduleMap)
                .put("bookingAirScheduleMapSize", bookingAirScheduleMap.size())
                .put("isAdmin", isAdmin)
                .build()));
        try {
            tmssService.send(sendEmailReq);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    public void sendAirCancelEmail(Company company, Travel travel, Traveler reserver) {
        Boolean isOverseas = travel.getIsOverseas();
        if (reserver != null && reserver.getTravelerUserId() != null) {
            User user = new User(reserver.getTravelerUserId());
            user.setEmail(reserver.getEmail());
            user.setName(reserver.getName());
            if (isOverseas) {
                this.sendOverseasAirCancel(company, travel, user);
            } else {
                this.sendDomesticAirCancel(company, travel, user);
            }
        }
    }

    private void sendDomesticAirCancel(Company company, Travel travel, User user) {
        String title = "[출장예약시스템 BTMS] " + user.getName() + "님, 항공권 예약이 취소되었습니다.";
        String btmsUrl = company.getParent() != null ? company.getParent().getBtmsSetting().getUrl() : company.getBtmsSetting().getUrl();
        SendEmailReq sendEmailReq = new SendEmailReq();
        sendEmailReq.setToEmail(user.getEmail());
        sendEmailReq.setToName(user.getName());
        sendEmailReq.setToId(user.getId().toString());
        sendEmailReq.setSubject(title);
        sendEmailReq.setContent(this.renderTemplate(company, "mail/air/domesticCancel", new ImmutableMap.Builder<String, Object>()
                .put("travel", travel)
                .put("name", user.getName())
                .put("personCnt", Math.max(travel.getTravelPersonnel() - 1, 0))
                .put("btmsUrl", btmsUrl)
                .put("imgUrl", this.btmsImgDomain)
                .build()));

        try {
            this.tmssService.send(sendEmailReq);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    private void sendOverseasAirCancel(Company company, Travel travel, User user) {
        BookingAir bookingAir = travel.getBookingAir();
        Map<Integer, List<BookingAirSchedule>> bookingAirScheduleMap = bookingAir.getBookingAirScheduleMap();
        String title = "[출장예약시스템 BTMS] " + user.getName() + "님, 항공권 예약이 취소되었습니다.";
        String btmsUrl = company.getParent() != null ? company.getParent().getBtmsSetting().getUrl() : company.getBtmsSetting().getUrl();
        SendEmailReq sendEmailReq = new SendEmailReq();
        sendEmailReq.setToEmail(user.getEmail());
        sendEmailReq.setToName(user.getName());
        sendEmailReq.setToId(user.getId().toString());
        sendEmailReq.setSubject(title);
        sendEmailReq.setContent(this.renderTemplate(company, "mail/air/overseasCancel", new ImmutableMap.Builder<String, Object>()
                .put("travel", travel)
                .put("name", user.getName())
                .put("personCnt", Math.max(travel.getTravelPersonnel() - 1, 0))
                .put("btmsUrl", btmsUrl)
                .put("imgUrl", this.btmsImgDomain)
                .put("bookingAirScheduleMap", bookingAirScheduleMap)
                .put("bookingAirScheduleMapSize", bookingAirScheduleMap.size())
                .build()));

        try {
            this.tmssService.send(sendEmailReq);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    public void sendContactRequestEmail(ContactRequest contactRequest) {
        try {
            String templateContent = this.getTemplateContent("mail/contactRequest");
            Template template = handlebars.compileInline(templateContent);

            SendEmailReq sendEmailReq = new SendEmailReq();
            sendEmailReq.setToEmail(this.corporateEmail);
            sendEmailReq.setToName(this.corporateEmail);
            sendEmailReq.setToId(this.corporateEmail);
            sendEmailReq.setSubject("상담 문의가 접수되었습니다.");
            sendEmailReq.setContent(template.apply(new ImmutableMap.Builder<String, Object>().put("contactRequest", contactRequest).build()));

            this.tmssService.send(sendEmailReq);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    public void sendVerificationCodeEmail(IdentificationRequest identificationRequest) {
        try {
            String templateContent = this.getTemplateContent("mail/user/identificationRequest");
            Template template = handlebars.compileInline(templateContent);

            SendEmailReq sendEmailReq = new SendEmailReq();
            sendEmailReq.setToEmail(identificationRequest.getVerificationMeansAccount());
            sendEmailReq.setToName(identificationRequest.getVerificationMeansAccount());
            sendEmailReq.setToId(identificationRequest.getVerificationMeansAccount());
            sendEmailReq.setSubject("[출장예약시스템 BTMS] 이메일 인증번호 발급안내");
            sendEmailReq.setContent(template.apply(new ImmutableMap.Builder<String, Object>()
                    .put("verificationCode", identificationRequest.getVerificationCode())
                    .put("imgUrl", this.btmsImgDomain)
                    .build()));

            this.tmssService.send(sendEmailReq);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    private String renderTemplate(Company company, String templateName, Map<String, Object> data) {
        String customTemplateName = null;
        if (company.isCustom()) {
            String siteDomain = company.getBtmsSetting().getUrl().split("\\.")[0];
            String[] pathArray = templateName.split("/");
            String modifiedPath = String.join("/", Arrays.copyOfRange(pathArray, 1, pathArray.length));
            customTemplateName = pathArray[0] + "/custom/" + siteDomain + "/" + modifiedPath;
        }

        try {
            String templateContent = null;
            if (customTemplateName != null) {
                templateContent = this.getTemplateContent(customTemplateName);
            }

            if (templateContent == null) { // if customTemplateName file not exist, use templateName
                templateContent = this.getTemplateContent(templateName);
            }

            if (templateContent == null) { // if templateName file not exist
                throw new RuntimeException(templateName + " not exist");
            }

            Template template = handlebars.compileInline(templateContent);
            return template.apply(data);
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    private String getTemplateContent(String templateName) throws IOException {
        if (this.templateCacheMap.containsKey(templateName)) {
            return this.templateCacheMap.get(templateName);
        }
        String templatePath = "templates/" + templateName + ".hbs";
        InputStream inputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(templatePath);
        if (inputStream == null) {
            return null;
        }
        String templateContent = new String(inputStream.readAllBytes());
        this.templateCacheMap.put(templateName, templateContent);
        return templateContent;
    }
}
