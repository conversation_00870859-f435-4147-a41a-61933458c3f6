package com.tidesquare.btms;

import org.eclipse.microprofile.openapi.annotations.OpenAPIDefinition;
import org.eclipse.microprofile.openapi.annotations.enums.SecuritySchemeType;
import org.eclipse.microprofile.openapi.annotations.enums.SecuritySchemeIn;
import org.eclipse.microprofile.openapi.annotations.info.Contact;
import org.eclipse.microprofile.openapi.annotations.info.Info;
import org.eclipse.microprofile.openapi.annotations.security.SecurityScheme;
import org.eclipse.microprofile.openapi.annotations.security.SecuritySchemes;
import org.eclipse.microprofile.openapi.annotations.servers.Server;

import jakarta.ws.rs.core.Application;

@OpenAPIDefinition(info = @Info(title = "BTMS API", version = "1.0.1", contact = @Contact(name = "Thomas", email = "<EMAIL>")), servers = {
        @Server(description = "Local", url = "http://localhost:3000"),
        @Server(description = "Staging", url = "https://hnbtmsapiv2.tourvis.com"),
        @Server(description = "Prod", url = "https://btmsapiv2.tourvis.com"), })
@SecuritySchemes({
        @SecurityScheme(securitySchemeName = "bearer", type = SecuritySchemeType.HTTP, scheme = "bearer", bearerFormat = "JWT"),
        @SecurityScheme(securitySchemeName = "apiKey", type = SecuritySchemeType.APIKEY, apiKeyName = "X-BTMS-API-KEY", in = SecuritySchemeIn.HEADER)
})
public class BtmsApplication extends Application {

}
