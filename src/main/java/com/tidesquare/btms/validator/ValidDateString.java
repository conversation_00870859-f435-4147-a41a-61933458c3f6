package com.tidesquare.btms.validator;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import jakarta.validation.Constraint;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import jakarta.validation.Payload;

@Retention(RUNTIME)
@Target({ FIELD })
@Constraint(validatedBy = DateStringValidator.class)
public @interface ValidDateString {

    String format() default "yyyy-MM-dd";

    String message() default "date is not valid";

    Class<? extends Payload>[] payload() default {};

    Class<?>[] groups() default {};

}
