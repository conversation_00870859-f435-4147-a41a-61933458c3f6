package com.tidesquare.btms.utils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CreationHelper;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;

import com.tidesquare.btms.constant.Constants;

public class ExcelUtil {

  public static Row addTitleCellList(Workbook workbook, Row row, List<String> list) {

    final CellStyle style = workbook.createCellStyle();
    style.setBorderBottom(BorderStyle.MEDIUM);
    style.setBottomBorderColor(IndexedColors.BLACK.getIndex());

    Cell cell;
    String value;
    for (int i = 0; i < list.size(); i++) {
      value = list.get(i);
      cell = row.createCell(i);
      cell.setCellStyle(style);
      if (value == null) {
        cell.setCellValue(Constants.EMPTY_STRING);
      } else {
        cell.setCellValue(value);
      }
    }

    return row;
  }

  public static Row addCellList2(Workbook workbook, Row row, List<Object> list) {

    final CreationHelper creationHelper = workbook.getCreationHelper();

    final CellStyle dateStyle = workbook.createCellStyle();
    dateStyle.setDataFormat(creationHelper.createDataFormat().getFormat(Constants.DATE_FORMAT));

    final CellStyle integerPositiveStyle = workbook.createCellStyle();
    integerPositiveStyle.setDataFormat(creationHelper.createDataFormat().getFormat("+#,##0"));

    final CellStyle integerStyle = workbook.createCellStyle();
    integerStyle.setDataFormat(creationHelper.createDataFormat().getFormat("#,##0"));

    final CellStyle doublePositiveStyle = workbook.createCellStyle();
    doublePositiveStyle.setDataFormat(creationHelper.createDataFormat().getFormat("+#,##0.###"));

    final CellStyle doubleStyle = workbook.createCellStyle();
    doubleStyle.setDataFormat(creationHelper.createDataFormat().getFormat("#,##0.###"));

    Cell cell;
    Object value;
    for (int i = 0; i < list.size(); i++) {
      value = list.get(i);
      cell = row.createCell(i);

      if (value instanceof String) {
        cell.setCellValue((String) value);
      } else if (value instanceof Integer) {
        cell.setCellValue((Integer) value);
        cell.setCellStyle(integerStyle);
      } else if (value instanceof Double) {
        // Double 도 정수로 Display
        Double dValue = (Double) value;
        cell.setCellValue(dValue);
        if (dValue % 1 == 0) {
          cell.setCellStyle(integerStyle);
        } else {
          cell.setCellStyle(doubleStyle);
        }
      } else if (value instanceof Date) {
        cell.setCellValue((Date) value);

        // set the cell format for dates
        cell.setCellStyle(dateStyle);
        row.getSheet().setColumnWidth(i, (int) (25 * 256 * 0.5));
      } else if (value instanceof BigDecimal) {
        // Double 도 정수로 Display
        BigDecimal bdValue = (BigDecimal) value;
        // dùng doubleStyle
        cell.setCellValue(bdValue.doubleValue());
        if (bdValue.remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) == 0) {
          cell.setCellStyle(integerStyle);
        } else {
          cell.setCellStyle(doubleStyle);
        }
      } else {

        // last ditch effort to populate cell
        if (value == null) {
          cell.setCellValue(Constants.EMPTY_STRING);
        } else {
          cell.setCellValue(value.toString());
        }
      }
    }

    return row;
  }
}
