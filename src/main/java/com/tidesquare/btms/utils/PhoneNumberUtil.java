package com.tidesquare.btms.utils;

public abstract class PhoneNumberUtil {

	public static String formatPhoneNumber(String phoneNumber, String sep) {
		if (phoneNumber == null || phoneNumber.length() < 7) {
			return "";
		}

		int len = phoneNumber.length();

		StringBuilder sb = new StringBuilder();
		if (len == 11) {
			sb.append(phoneNumber.substring(0, 3));
			sb.append(sep);
			sb.append(phoneNumber.substring(3, 7));
			sb.append(sep);
			sb.append(phoneNumber.substring(7));

		} else {
			sb.append(phoneNumber.substring(0, 3));
			sb.append(sep);
			sb.append(phoneNumber.substring(3, 6));
			sb.append(sep);
			sb.append(phoneNumber.substring(6));
		}
		return sb.toString();
	}

}
