package com.tidesquare.btms.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import com.tidesquare.btms.constant.AirComMathType;
import com.tidesquare.btms.constant.CommissionCalculationType;
import com.tidesquare.btms.constant.CompanyType;
import com.tidesquare.btms.constant.DomesticSaleComRateType;
import com.tidesquare.btms.constant.SeatClass;
import com.tidesquare.btms.entity.Company;

public class AirUtil {

	public static double calculationTasf(Company company, boolean isLocal, double fareAmount, double discountAmount, double taxAmount, SeatClass seatTypeCode) {
		// 기존 수수료계산 일반석
		Map<String, Object> calculationMap = calculation(company, isLocal, fareAmount, discountAmount, taxAmount, seatTypeCode);
		return (Double) calculationMap.get("AMOUNT");
	}

	/**
	 * 회사별 항공 수수료 계산.
	 *
	 * @param company        회사정보
	 * @param isLocal        국내/해외 구분(true : 국내, false : 해외)
	 * @param fareAmount     항공 Fare 요금
	 * @param discountAmount 할인요금
	 * @param taxAmount      세금
	 * @param seatTypeCode   M:일반석,W:프리미엄이코노미석,C:비즈니스석,F:일등석
	 * @return RATE 수수료율 AMOUNT 절삭 수수료 UNCUT_AMOUNT 절삭전 수수료 VAT_AMOUNT 절삭 수수료 VAT
	 */
	public static Map<String, Object> calculation(Company company, boolean isLocal, double fareAmount, double discountAmount, double taxAmount, SeatClass seatTypeCode) {
		// *** 법인
		// - 해외 ->net기준 : ((fare-dc) * airComm / 100)
		// ->결제금액 : ((발권금액(fare-dc+tax)+업금액) * airComm/100)
		//
		// - 국내 ->S/FARE : 거래처 국내항공수수료율 코드 기준으로 설정한 경우
		// 해당참고))법인사업팀은 전체금액의 *@%로 받는 곳이 있고, net가 기준으로 *@% 받는 곳도 있음.
		// 요청한 업체에 대해 net가 기준으로 자동 매겨지는 걸로 되어 있음.즉, airComm은 항공사 인컴은 아니고 업체별로 tasf율이 다름.
		// 예를 들어 4% 받는다 했을 때 이 tasf%를 넣은 것 같음.
		// ->판매총액 : 거래처 국내항공수수료율 코드 판매총액 기준으로 설정한 경우 해당
		//
		// *** 상용
		// - 해외 ->net기준 : ((fare-dc) * airComm / 100) : 예, SK건설, SK텔레콤, SK해운 등
		// ->결제금액 : (NET + TAX) * airComm / 100 : 예, SK이노베이션, SK종합화학, SK에너지 등
		//
		// - 국내 ->S/FARE, 판매총액 : 현재 수수료 청구없음. 추후 정액제 시행계획 있음. 예)편도 5,000원/왕복 10,000원
		// 추후, 해외항공도 정액제 시행계획 있음. 예)장거리 100,000 중/장거리 150,000, 단거리 50,000

		Map<String, Object> calculationMap = new HashMap<>();
		CompanyType companyType = company.getCompanyType(); // 법인, 상용 구분
		CommissionCalculationType commissionCalculationType = company.getCommissionCalculationType(); // 해외 - NET기준, 결제금액 기준
		DomesticSaleComRateType domesticSaleComRateType = company.getDomesticSaleComRateType(); // 국내 - S/FARE, 판매총액 기준
		double commissionRate = Double.valueOf(0);
		double netAmount = fareAmount - discountAmount;
		double calculatedCommissionAmount = Double.valueOf(0);

		if (CompanyType.CORP.equals(companyType)) { // 법인
			if (isLocal) { // 국내
				if (DomesticSaleComRateType.SFARE.equals(domesticSaleComRateType)) {
					commissionRate = Optional.ofNullable(company.getDomesticAirComRate()).orElse(Double.valueOf(0)); // S/FARE 국내항공 수수료
				} else {
					commissionRate = Optional.ofNullable(company.getDomesticSaleComRate()).orElse(Double.valueOf(0)); // 판매총액 국내항공 판매수수료
				}
			} else { // 해외
				if (SeatClass.C.equals(seatTypeCode) || SeatClass.F.equals(seatTypeCode)) {
					commissionRate = Optional.ofNullable(company.getBusinessAirComRate()).orElse(Double.valueOf(0)); // 해외 비즈니스 항공 수수료
				} else {
					commissionRate = Optional.ofNullable(company.getEconomyAirComRate()).orElse(Double.valueOf(0)); // 해외 이코노미 항공 수수료
				}
			}
		} else if (CompanyType.COMM.equals(companyType)) { // 상용
			if (isLocal) { // 국내
				calculationMap.put("RATE", commissionRate);
				calculationMap.put("AMOUNT", Double.valueOf(0));
				// 절삭전 수수료 소수점 한자리 값으로 저장
				calculationMap.put("UNCUT_AMOUNT", Double.valueOf(0));
				return calculationMap; // 수수료 없음.
			} else { // 해외
				if (SeatClass.C.equals(seatTypeCode) || SeatClass.F.equals(seatTypeCode)) {
					commissionRate = Optional.ofNullable(company.getBusinessAirComRate()).orElse(Double.valueOf(0)); // 해외 비즈니스 항공 수수료
				} else {
					commissionRate = Optional.ofNullable(company.getEconomyAirComRate()).orElse(Double.valueOf(0)); // 해외 이코노미 항공 수수료
				}
			}
		}
		calculationMap.put("RATE", commissionRate);

		if (CommissionCalculationType.NET.equals(commissionCalculationType)) {
			calculatedCommissionAmount = netBase(netAmount, commissionRate);
		} else if (CommissionCalculationType.TOTAL_AMOUNT.equals(commissionCalculationType)) {
			calculatedCommissionAmount = totalAmountBase(netAmount, taxAmount, commissionRate);
		}

		// 수수료 절삭 소스
		// ----------------------------------------------------------------------------------------------------------------------------------
		AirComMathType airComMathType = company.getAirComMathType();// 절삭방식 : null, 올림, 내림, 반올림

		BigDecimal cutCommissionAmount = BigDecimal.valueOf(calculatedCommissionAmount);

		// 수수료 절삭 정책이 있는 경우
		int newScale = company.getComCuttingUnit() != null ? company.getComCuttingUnit().getScale() : 0;
		if (null != airComMathType) {
			cutCommissionAmount = cutCommissionAmount.setScale(newScale, airComMathType.getRoundingMode());
		} else {
			// 소수점 아래 내림
			cutCommissionAmount = cutCommissionAmount.setScale(newScale, RoundingMode.DOWN);
		}
		calculationMap.put("TASF_AMOUNT", cutCommissionAmount.doubleValue());

		// 반올림인 경우에만 절삭전 수수료 소수점 한자리 값으로 저장
		if (null != airComMathType && AirComMathType.ROUND_HALF_UP.equals(airComMathType)) {
			DecimalFormat decimalFormat = new DecimalFormat("0.0");
			calculationMap.put("UNCUT_AMOUNT", Double.parseDouble(decimalFormat.format(calculatedCommissionAmount)));
		} else {
			calculationMap.put("UNCUT_AMOUNT", cutCommissionAmount.doubleValue());
		}

		// 수수료 VAT 절삭
		// -----------------------------------------------------------------------------
		BigDecimal cuttingVatAmount = getVatAmount(company, cutCommissionAmount.doubleValue());
		calculationMap.put("VAT_AMOUNT", cuttingVatAmount.doubleValue());
		// ------------------------------------------------------------------------------------------

		calculationMap.put("AMOUNT", cutCommissionAmount.add(cuttingVatAmount).doubleValue());

		return calculationMap;
	}

	public static BigDecimal getVatAmount(Company company, double calculatedCommissionAmount) {
		AirComMathType airComVatMathType = company.getAirComVatMathType(); // 절삭방식 : null, 올림, 내림, 반올림
		BigDecimal cuttingVatAmount = BigDecimal.ZERO;
		if (Boolean.TRUE.equals(company.isTaxInvoiceVat())) { // 세금계산서 사용 가능한 경우 TASF의 VAT 10%
			BigDecimal calculationVatAmount = new BigDecimal(calculatedCommissionAmount * 0.1);
			int vatScale = company.getComVatCuttingUnit() != null ? company.getComVatCuttingUnit().getScale() : 0;
			cuttingVatAmount = calculationVatAmount.setScale(vatScale, airComVatMathType.getRoundingMode());
		}
		return cuttingVatAmount;
	}

	private static double netBase(double netAmount, double commissionRate) {
		return netAmount * commissionRate / 100;
	}

	private static double totalAmountBase(double netAmount, double taxAmount, double commissionRate) {
		return (netAmount + taxAmount) * commissionRate / 100;
	}
}
