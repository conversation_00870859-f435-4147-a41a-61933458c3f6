package com.tidesquare.btms.utils;

import java.util.Random;
import java.util.regex.Pattern;

public abstract class StringUtils {

	public static boolean isNullOrEmpty(String str) {
		return str == null || str.length() == 0;
	}

	public static boolean isNullOrBlank(String str) {
		return str == null || str.trim().length() == 0;
	}

	public static boolean isNumeric(String str) {
		Pattern pattern = Pattern.compile("[+-]?\\d+");
		return pattern.matcher(str).matches();
	}

	public static String generateRandomString(int length) {
		Random random = new Random();
		char[] characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789".toCharArray();
		char[] result = new char[length];
		for (int i = 0; i < length; i++) {
			result[i] = characters[random.nextInt(characters.length)];
		}
		return new String(result);
	}
}
