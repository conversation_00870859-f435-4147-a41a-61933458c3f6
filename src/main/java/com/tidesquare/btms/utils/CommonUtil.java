package com.tidesquare.btms.utils;

import java.text.NumberFormat;
import java.text.ParseException;

public final class CommonUtil {
    public static String moneyFormat(String s) {

        NumberFormat numberformat = NumberFormat.getNumberInstance();
        String s1;

        try {
            Number number = numberformat.parse(s);
            s1 = numberformat.format(number);
        } catch (ParseException parseexception) {
            s1 = "0";
        }
        return s1;
    }

    public static String stringDateFormat(String ymd, String pattern) {

        String result = "";

        if (ymd != null) {
            ymd = ymd.replaceAll("-", "");

            if (ymd.length() > 5) {
                result += ymd.substring(0, 4) + pattern;
                result += ymd.substring(4, 6) + pattern;
                result += ymd.substring(6, 8);
            }
        }
        return result;
    }
}
