package com.tidesquare.btms.utils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeFormatterBuilder;
import java.util.Date;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.tidesquare.btms.constant.Constants;
import com.tidesquare.btms.constant.OPType;

public class DateUtil {

    public static Date getStringToDate(String strDate) throws ParseException {
        if (strDate == null || "".equals(strDate))
            return null;
        strDate = strDate.replaceAll("-", "");
        DateFormat sdFormat = new SimpleDateFormat(Constants.DATE_NODASH_FORMAT);
        Date date = sdFormat.parse(strDate);
        return date;
    }

    public static String getDateTimeToString(Date date, String strFormat) {
        if (date == null)
            return null;
        DateFormat sdFormat = new SimpleDateFormat(strFormat, Locale.KOREA);
        String strDate = sdFormat.format(date);
        return strDate;
    }

    /**
     * Convert string to date
     * 
     * @param s      string
     * @param format format
     * @return date
     */
    public static Date string2Date(String s, String format) {
        Date d = null;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            d = sdf.parse(s, new ParsePosition(0));
        } catch (Exception e) {
            throw new RuntimeException("Date format not valid.");
        }
        return d;
    }

    /**
     * Convert date to string
     * 
     * @param date   date
     * @param format format
     * @return string
     */
    public static String date2String(Date date, String format) {
        if (format == null || format.isEmpty()) {
            throw new IllegalArgumentException("Format cannot be null or empty");
        }

        try {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return sdf.format(date);
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("Date format not valid.");
        }
    }

    /**
     * Convert date to string
     * 
     * @param date date
     * @return string
     */
    public static String date2String(Date date) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.format(date);
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("Date format not valid.");
        }
    }

    public static long diffDays(String firstDate, String secondDate, String format) {
        return diffDays(string2Date(firstDate, format), string2Date(secondDate, format));
    }

    /**
     * Calculate the difference in days between two dates
     * 
     * @param firstDate  first date
     * @param secondDate second date
     * @return difference in days
     */
    public static long diffDays(Date firstDate, Date secondDate) {
        if (firstDate == null || secondDate == null) {
            return 0;
        } else {
            return diffDays(firstDate.getTime(), secondDate.getTime());
        }
    }

    /**
     * Calculate the difference in days between two dates
     * 
     * @param firstDate  first date
     * @param secondDate second date
     * @return difference in days
     */
    public static long diffDays(long firstDate, long secondDate) {
        return (firstDate - secondDate) / (24 * 60 * 60 * 1000);
    }

    /**
     * Format minute to HHmm
     * 
     * @param minute minute
     * @return HHmm
     */
    public static String formatMinuteHHmm(int minute) {
        if (minute < 0) {
            return null;
        }
        int hourTime = minute / 60;
        int minTime = minute % 60;
        return String.format("%02d", hourTime).concat(String.format("%02d", minTime));
    }

    /**
    * HHMM 형태의 시간 합산값을 반환
    * @return HHMM 형태로 변경된 문자열값
    */
    public static String plusHHMM(String time1, String time2) {

        if (StringUtils.isNullOrEmpty(time1)) {
            time1 = "0000";
        }
        if (StringUtils.isNullOrEmpty(time2)) {
            time2 = "0000";
        }
        int hour1 = Integer.parseInt(time1.substring(0, 2)) * 60;
        int min1 = Integer.parseInt(time1.substring(2, 4));
        int hour2 = Integer.parseInt(time2.substring(0, 2)) * 60;
        int min2 = Integer.parseInt(time2.substring(2, 4));

        int sumHour = Math.round((hour1 + hour2 + (Math.round((min1 + min2) / 60)) * 60) / 60);
        int sumMin = (min1 + min2) % 60;

        return (sumHour < 10 ? "0" + sumHour : sumHour) + "" + (sumMin < 10 ? "0" + sumMin : sumMin);
    }

    public static int whichDay(String s) throws java.text.ParseException {
        return whichDay(s, "yyyyMMdd");
    }

    public static int whichDay(String s, String format) throws java.text.ParseException {
        java.text.SimpleDateFormat formatter = new java.text.SimpleDateFormat(format, java.util.Locale.KOREA);
        java.util.Date date = check(s, format);

        java.util.Calendar calendar = formatter.getCalendar();
        calendar.setTime(date);
        return calendar.get(java.util.Calendar.DAY_OF_WEEK) - 1;
    }

    public static java.util.Date check(String s, String format) throws java.text.ParseException {
        if (s == null)
            throw new java.text.ParseException("date string to check is null", 0);
        if (format == null)
            throw new java.text.ParseException("format string to check date is null", 0);

        java.text.SimpleDateFormat formatter = new java.text.SimpleDateFormat(format, java.util.Locale.KOREA);
        java.util.Date date = null;
        try {
            date = formatter.parse(s);
        } catch (java.text.ParseException e) {
            /*
             * throw new java.text.ParseException( e.getMessage() +
             * " with format \"" + format + "\"", e.getErrorOffset() );
             */
            throw new java.text.ParseException(" wrong date:\"" + s + "\" with format \"" + format + "\"", 0);
        }

        if (!formatter.format(date).equals(s))
            throw new java.text.ParseException("Out of bound date:\"" + s + "\" with format \"" + format + "\"", 0);
        return date;
    }

    public static Date getAmadeusDate(String pnrRawData, OPType opType, Date bookingDate) {
        String patternStr = opType.name() + "-([0-9]{2}(?:JAN|FEB|MAR|APR|MAY|JUN|JUL|AUG|SEP|OCT|NOV|DEC)):([0-9]{4})";
        Pattern pattern = Pattern.compile(patternStr);
        Matcher matcher = pattern.matcher(pnrRawData);
        if (matcher.find()) {
            String dateStr = matcher.group(1);
            String timeStr = matcher.group(2);

            if (opType.equals(OPType.OPW)) {
                timeStr = "1600";
            }

            LocalDateTime bookingDateTime = bookingDate.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();

            String amadeusDateWithBookingYear = dateStr + bookingDateTime.getYear() + " " + timeStr;

            DateTimeFormatter formatter = new DateTimeFormatterBuilder()
                    .parseCaseInsensitive()
                    .appendPattern("ddMMMyyyy HHmm")
                    .toFormatter(Locale.ENGLISH);

            LocalDateTime amadeusDateTime = LocalDateTime.parse(amadeusDateWithBookingYear, formatter);

            if (amadeusDateTime.isBefore(bookingDateTime)) {
                int nextYearOfBookingDate = bookingDateTime.getYear() + 1;
                String amadeusDateWithCurrentYear = dateStr + nextYearOfBookingDate + " " + timeStr;
                amadeusDateTime = LocalDateTime.parse(amadeusDateWithCurrentYear, formatter);
            }

            ZonedDateTime zonedDateTime = ZonedDateTime.of(amadeusDateTime, ZoneOffset.UTC);
            return Date.from(zonedDateTime.withZoneSameInstant(ZoneId.systemDefault()).toInstant());
        }
        return null;
    }
}
