package com.tidesquare.btms.utils;

import java.util.ArrayList;
import java.util.List;

public class ListUtil {
    public static <T> List<List<T>> chunk(List<T> list, int chunkSize) {
        List<List<T>> result = new ArrayList<>();
        int size = list.size();
        for (int i = 0; i < size; i += chunkSize) {
            int end = Math.min(size, i + chunkSize);
            result.add(new ArrayList<>(list.subList(i, end)));
        }
        return result;
    }
}
